package com.birdeye.social.entities;

import lombok.ToString;

import javax.persistence.*;

@Entity
@Table(name = "permission_mapping")
@ToString
public class PermissionMapping {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "channel")
    private String channel;

    @Column(name = "permission_name")
    private String permissionName;

    @Column(name = "permission_code")
    private Integer permissionCode;

    @Column(name = "error_parentcode")
    private Integer errorParentCode;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "error_code")
    private String errorCode;

    @Column(name = "http_response_code")
    private Integer httpResponse;

    @Column(name = "error_message_actual")
    private String errorActualMessage;

    @Column(name = "module")
    private String module;

    @Column(name = "failed_bucket")
    private Integer bucket;

    @Column(name = "mark_invalid")
    private Integer markInvalid;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getPermissionName() {
        return permissionName;
    }

    public void setPermissionName(String permissionName) {
        this.permissionName = permissionName;
    }

    public Integer getPermissionCode() {
        return permissionCode;
    }

    public void setPermissionCode(Integer permissionCode) {
        this.permissionCode = permissionCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public void seterrorParentCode(Integer errorParentCode) {
        this.errorParentCode = errorParentCode;
    }
    public Integer geterrorParentCode() {
        return errorParentCode;
    }

    public String geterrorActualMessage() {
        return errorActualMessage;
    }
    public void seterrorActualMessagee(String errorActualMessage) {
        this.errorActualMessage = errorActualMessage;
    }

    public void sethttpResponse(Integer httpResponse) {
        this.httpResponse = httpResponse;
    }

    public Integer gethttpResponse() {
        return httpResponse;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public Integer getBucket() {
        return bucket;
    }

    public void setBucket(Integer bucket) {
        this.bucket = bucket;
    }

    public Integer getMarkInvalid() {
        return markInvalid;
    }

    public void setMarkInvalid(Integer markInvalid) {
        this.markInvalid = markInvalid;
    }
}
