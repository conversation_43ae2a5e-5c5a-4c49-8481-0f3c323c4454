package com.birdeye.social.entities;

import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.Date;

@Entity
@ToString
@Table(name = "linkedin_refresh_token")
public class LinkedinRefreshToken {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "refresh_token")
    @Size(max = 1000)
    private String refreshToken;

    @Column(name = "expires_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date expiresOn;

    @Column(name = "linkedin_profile_id")
    private String linkedinProfileId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Date getExpiresOn() {
        return expiresOn;
    }

    public void setExpiresOn(Date expiresOn) {
        this.expiresOn = expiresOn;
    }

    public String getLinkedinProfileId() {
        return linkedinProfileId;
    }

    public void setLinkedinProfileId(String linkedinProfileId) {
        this.linkedinProfileId = linkedinProfileId;
    }

    public LinkedinRefreshToken(LinkedinRefreshToken token) {
        this.userId = token.getUserId();
        this.refreshToken = token.getRefreshToken();
        this.expiresOn = token.getExpiresOn();
    }

    public LinkedinRefreshToken() {
    }
}
