package com.birdeye.social.entities.competitor;

import lombok.*;

import javax.persistence.*;
import java.util.Date;

@Entity
@Builder
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "instagram_competitor_info")
public class InstagramCompetitorInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    @Column(name = "full_name")
    private String fullName;

    @Column(name = "page_id")
    private String pageId;

    @Column(name = "page_url")
    private String pageUrl;

    @Column(name = "profile_picture_url")
    private String profilePictureUrl;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "description")
    private String description;

    @Column(name = "last_scan_date")
    private Date lastScanDate;

    @Column(name= "verified")
    private Integer verified;

    @Column(name = "scanned_once")
    private Integer scannedOnce;
}
