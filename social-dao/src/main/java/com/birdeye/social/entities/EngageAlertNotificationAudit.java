package com.birdeye.social.entities;


import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Entity
@Builder
@Data
@Table(name = "engage_alert_notification_audit")
public class EngageAlertNotificationAudit implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "enterprise_id")
    private Long enterpriseId;

    @Column(name="business_id")
    private Integer businessId;

    @Column(name = "feed_id")
    private String feedId;

    @Column(name = "is_real_time_notification")
    private Boolean isRealTimeNotification;

    @Column(name = "payload")
    private String payload;

    @Column(name = "status")
    private String status;

    @Column(name = "comment")
    private String comment;

    @Column(name = "receiver_emails")
    private String receiverEmails;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at")
    private Date createdAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at")
    private Date updatedAt;
}


