/**
 * 
 */
package com.birdeye.social.entities;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "gmb_review_notification")
public class GMBReviewNotification implements Serializable{

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Integer id;


	@Column(name = "business_id")
	private Integer buisness_id;

	@Column(name = "status")
	private String status;

	@Column(name = "comment")
	private String comment;

	@Column(name = "location_id")
	private String locationId;

	@Column(name = "source_review_id")
	private String sourceReviewId;

	@Column(name = "notification_type")
	private String notificationType;

	@Column(name = "review_uri")
	private String reviewUri;

	@Column(name = "event_data")
	private String eventData;

	@Column(name = "decoded_event_data")
	private String decodedEventData;
	
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created")
	private Date created;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated")
	private Date updated;

	@Column(name = "error_code")
	private Integer errorCode;

	@Column(name = "place_id")
	private String placeId;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getBuisness_id() {
		return buisness_id;
	}

	public void setBuisness_id(Integer buisness_id) {
		this.buisness_id = buisness_id;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public String getLocationId() {
		return locationId;
	}

	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}

	public String getSourceReviewId() {
		return sourceReviewId;
	}

	public void setSourceReviewId(String sourceReviewId) {
		this.sourceReviewId = sourceReviewId;
	}

	public String getNotificationType() {
		return notificationType;
	}

	public void setNotificationType(String notificationType) {
		this.notificationType = notificationType;
	}

	public String getReviewUri() {
		return reviewUri;
	}

	public void setReviewUri(String reviewUri) {
		this.reviewUri = reviewUri;
	}

	public String getEventData() {
		return eventData;
	}

	public void setEventData(String eventData) {
		this.eventData = eventData;
	}

	public String getDecodedEventData() {
		return decodedEventData;
	}

	public void setDecodedEventData(String decodedEventData) {
		this.decodedEventData = decodedEventData;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public Date getUpdated() {
		return updated;
	}

	public void setUpdated(Date updated) {
		this.updated = updated;
	}

	public Integer getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(Integer errorCode) {
		this.errorCode = errorCode;
	}

	public String getPlaceId() {
		return placeId;
	}

	public void setPlaceId(String placeId) {
		this.placeId = placeId;
	}

	@Override
	public String toString() {
		return "GMBReviewNotification [id=" + id + ", buisness_id=" + buisness_id + ", status=" + status + ", comment="
				+ comment + ", locationId=" + locationId + ", sourceReviewId=" + sourceReviewId + ", notificationType="
				+ notificationType + ", reviewUri=" + reviewUri + ", eventData=" + eventData + ", decodedEventData="
				+ decodedEventData + ", created=" + created
				+ ", updated=" + updated + ", errorCode=" + errorCode + ", placeId=" + placeId + "]";
	}

	
}
