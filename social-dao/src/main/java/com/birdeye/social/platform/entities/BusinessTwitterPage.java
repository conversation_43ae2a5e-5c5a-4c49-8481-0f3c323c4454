package com.birdeye.social.platform.entities;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import com.birdeye.social.scheduler.dto.ExternalIntegration;

@Entity
@Table(name = "business_twitter_page")
public class BusinessTwitterPage implements Serializable, ExternalIntegration {
	/**
	 *
	 */
	private static final long serialVersionUID = -5261994169146189351L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	
	@Column(name = "business_id")
	private Integer businessId;
	
	@Column(name = "access_token")
	@Size(max = 255)
	private String accessToken;

	@Column(name = "access_secret")
	@Size(max = 255)
	private String accessSecret;

	@Column(name = "min_rating")
	private Integer minRating = 4;
	
	@Column(name = "enabled")
	private Integer enabled = 0;
	
	@Column(name = "day_post_count")
	private Integer dayPostCount = 0;

	@Column(name = "page_url")
	private String pageUrl;

	@Column(name = "max_post_allowed")
	private Integer maxPostAllowed = 3;
	
	@Column(name = "profile_id")
	private Long profileId;
	
	@Column(name = "profile_picture_url")
	private String imageUrl;
	
	@Column (name = "handle")
	private String handle;
	
	@Column (name = "name")
	private String name;
	
	@Column(name = "is_valid")
	private Integer isValid = 1;

	@Column(name = "last_scanned_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date				lastScannedOn;
	
	@Column (name = "location")
	private String location;
	
	@Column(name="created_by")
	private Integer createdBy;
	
	@Column(name="updated_by")
	private Integer updatedBy;

	public BusinessTwitterPage() {
	}

	public String getAccessSecret() {
		return accessSecret;
	}

	public void setAccessSecret(String accessSecret) {
		this.accessSecret = accessSecret;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public Integer getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getEnabled() {
		return enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}

	public Integer getMinRating() {
		return minRating;
	}

	public void setMinRating(Integer minRating) {
		this.minRating = minRating;
	}

	public Long getProfileId() {
		return profileId;
	}

	public void setProfileId(Long profileId) {
		this.profileId = profileId;
	}

	public String getImageUrl() {
		return imageUrl;
	}

	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}

	public String getHandle() {
		return handle;
	}

	public void setHandle(String handle) {
		this.handle = handle;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Override
	public boolean equals(Object obj) {
		if (obj == null) {
			return false;
		}
		if (getClass() != obj.getClass()) {
			return false;
		}
		final BusinessTwitterPage other = (BusinessTwitterPage) obj;
		if (this.businessId != other.businessId && (this.businessId == null || !this.businessId.equals(other.businessId))) {
			return false;
		}
		return true;
	}

	@Override
	public int hashCode() {
		int hash = 5;
		hash = 89 * hash + (this.businessId != null ? this.businessId.hashCode() : 0);
		return hash;
	}

	public Integer getDayPostCount() {
		return dayPostCount;
	}

	public void setDayPostCount(Integer dayPostCount) {
		this.dayPostCount = dayPostCount;
	}

	public String getPageUrl() {
		return pageUrl;
	}

	public void setPageUrl(String pageUrl) {
		this.pageUrl = pageUrl;
	}

	public Integer getMaxPostAllowed() {
		return maxPostAllowed;
	}

	public void setMaxPostAllowed(Integer maxPostAllowed) {
		this.maxPostAllowed = maxPostAllowed;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}
	
	public Date getLastScannedOn() {
		return lastScannedOn;
	}
	
	public void setLastScannedOn(Date lastScannedOn) {
		this.lastScannedOn = lastScannedOn;
	}

	/**
	 * @return the location
	 */
	public String getLocation() {
		return location;
	}

	/**
	 * @param location the location to set
	 */
	public void setLocation(String location) {
		this.location = location;
	}

	/**
	 * @return the createdBy
	 */
	public Integer getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the updatedBy
	 */
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}


	@Override
	public String toString() {
		return "BusinessTwitterPage [id=" + id + ", businessId=" + businessId + ", accessToken=" + accessToken
				+ ", accessSecret=" + accessSecret + ", minRating=" + minRating + ", enabled=" + enabled
				+ ", dayPostCount=" + dayPostCount + ", pageUrl=" + pageUrl + ", maxPostAllowed=" + maxPostAllowed
				+ ", profileId=" + profileId + ", imageUrl=" + imageUrl + ", handle=" + handle + ", name=" + name
				+ ", isValid=" + isValid + ", lastScannedOn=" + lastScannedOn + ", location=" + location
				+ ", createdBy=" + createdBy + ", updatedBy=" + updatedBy +  "]";
	}



}
