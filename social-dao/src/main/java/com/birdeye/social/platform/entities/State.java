/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.birdeye.social.platform.entities;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "state")
public class State implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "name")
    private String name;
    
    @Column(name = "code")
    private String code;
    
    @JoinColumn(name = "country_id", referencedColumnName = "id")
    @ManyToOne(fetch = FetchType.EAGER, cascade= CascadeType.MERGE)
    private Country country;
    
    @Column(name = "country_id", insertable=false, updatable=false)
    private Integer countryId;
    
    @Column(name = "is_business_mapped")
    private Integer isBusinessMapped = 0;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Country getCountry() {
        return country;
    }

    public void setCountry(Country country) {
        this.country = country;
    }

    public Integer getCountryId() {
        return countryId;
    }

    public void setCountryId(Integer countryId) {
        this.countryId = countryId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof State)) {
            return false;
        }
        State other = (State) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    public Integer getIsBusinessMapped() {
        return isBusinessMapped;
    }

    public void setIsBusinessMapped(Integer isBusinessMapped) {
        this.isBusinessMapped = isBusinessMapped;
    }

    @Override
    public String toString() {
        return "State{" + "id=" + id + ", name=" + name + ", code=" + code + ", country=" + country + ", countryId=" + countryId + ", isBusinessMapped=" + isBusinessMapped + '}';
    }

}
