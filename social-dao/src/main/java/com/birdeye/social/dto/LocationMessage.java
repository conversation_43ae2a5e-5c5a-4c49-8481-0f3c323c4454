package com.birdeye.social.dto;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.utils.CoreUtils;

public class LocationMessage {
    
    private Integer id;
    private String address1;
    private String address2;
    private String city;
    private String state;
    private String zip;
    private String countryCode;
    private String countryName;
    private String lat;
    private String lng;
    
    public LocationMessage() {
    }
  
    public LocationMessage(ReviewerEntity location) {
        if(location != null){
            this.id = location.getLocationId();
            this.address1 = location.getAddress1();
            this.address2 = location.getAddress2();
            this.city = location.getCity();
            this.state = location.getState();
            this.countryCode = location.getCountryCode();
            this.countryName = location.getCountryName();
            this.zip = CoreUtils.formatZipCode(location.getZip(), location.getCountryCode());
            if(location.getLatitude() != null){
                this.lat = (location.getLatitude() / Constants.LAT_LNG_MULTIPLIER) + "";
            }
            if(location.getLongitude() != null){
                this.lng = (location.getLongitude() / Constants.LAT_LNG_MULTIPLIER) + "";
            }
        }
    }

    public LocationMessage(LocationDetails location) {
        this.city = location.getCity();
        this.state = location.getState();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getAddress1() {
        return address1;
    }

    public String getAddress2() {
        return address2;
    }

    public String getCity() {
        return city;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public String getCountryName() {
        return countryName;
    }

    public String getState() {
        return state;
    }

    public String getZip() {
        return zip;
    }

    public void setAddress1(String address1) {
        this.address1 = address1;
    }

    public void setAddress2(String address2) {
        this.address2 = address2;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public void setState(String state) {
        this.state = state;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

}