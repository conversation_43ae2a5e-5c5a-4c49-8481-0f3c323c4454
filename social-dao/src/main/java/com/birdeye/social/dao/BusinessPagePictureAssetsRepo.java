package com.birdeye.social.dao;

import com.birdeye.social.entities.BusinessPagePictureAssets;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BusinessPagePictureAssetsRepo extends JpaRepository<BusinessPagePictureAssets, Integer> {
    BusinessPagePictureAssets findByExternalPageIdAndSourceId(String externalPageId, Integer sourceId);

    List<BusinessPagePictureAssets> findBySourceIdAndExternalPageIdIn(Integer sourceId, List<String> externalPageId);
}
