package com.birdeye.social.dao.competitor;

import com.birdeye.social.entities.competitor.InstagramCompetitorInfo;
import com.birdeye.social.entities.competitor.InstagramCompetitorInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;

@Repository
public interface InstagramCompetitorInfoRepo extends JpaRepository<InstagramCompetitorInfo, Integer> {
    InstagramCompetitorInfo findByPageId(String pageId);

    List<InstagramCompetitorInfo> findByPageIdIn(List<String> pageId);

    @Query(value = "SELECT ic.* from instagram_competitor_info as ic join instagram_competitor_mapping im " +
            "on ic.id = im.raw_competitor_id where ic.last_scan_date < :lastScanDate " +
            "ORDER by last_scan_date asc LIMIT :limit", nativeQuery = true)
    List<InstagramCompetitorInfo> findRecordsByLastScanDate(@Param("lastScanDate") Date lastScanDate, @Param("limit") int limit);

    Page<InstagramCompetitorInfo> findByLastScanDateIsLessThanOrderByLastScanDateAsc(Date lastScanDate, Pageable pageable);

    @Modifying
    @Transactional
    @Query("UPDATE InstagramCompetitorInfo b  SET b.lastScanDate = :lastScanDate where b.id in :ids")
    int updateLastScanDate(@Param("lastScanDate") Date lastScanDate, @Param("ids") List<Integer> ids);

    @Modifying
    @Transactional
    @Query("UPDATE InstagramCompetitorInfo b  SET b.scannedOnce = :scannedOnce where b.id = :id")
    int updateScannedOnce(@Param("scannedOnce") Integer scannedOnce, @Param("id") Integer id);
}
