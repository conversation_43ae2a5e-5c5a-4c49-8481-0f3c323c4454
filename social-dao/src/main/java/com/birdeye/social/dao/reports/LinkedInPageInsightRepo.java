package com.birdeye.social.dao.reports;

import com.birdeye.social.entities.report.LinkedInPageInsight;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface LinkedInPageInsightRepo extends JpaRepository<LinkedInPageInsight, Integer> {

    List<LinkedInPageInsight> findByPageIdOrderByIdDesc(String externalId);
    LinkedInPageInsight findFirstByPageIdOrderByIdAsc(String externalId);
    List<LinkedInPageInsight> findByPageIdAndDateBetween(String pageId, Date startDate, Date endDate);
}
