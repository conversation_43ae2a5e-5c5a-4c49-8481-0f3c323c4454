package com.birdeye.social.dao.reports;

import com.birdeye.social.entities.report.BusinessPosts;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Repository
public interface BusinessPostsRepository extends JpaRepository<BusinessPosts, Integer>, PagingAndSortingRepository<BusinessPosts, Integer> {

    BusinessPosts findById(Integer id);

    List<BusinessPosts> findByBePostId(Integer bePostId);
    Page<BusinessPosts> findByLastScanDateLessThanAndSourceId(Date lastScanDate, Integer sourceId, Pageable pageable);
    Page<BusinessPosts> findByLastScanDateLessThanAndSourceIdAndPostIdNotNull(Date lastScanDate, Integer sourceId, Pageable pageable);

    BusinessPosts findTopByExternalPageIdAndSourceIdOrderByIdDesc(String externalPageId, Integer sourceId);

    BusinessPosts findTopByExternalPageIdAndSourceIdAndBePostIdIsNullOrderByPublishDateDesc(String externalPageId, Integer sourceId);
    BusinessPosts findByPostIdAndSourceId(String postId,Integer sourceId);
    List<BusinessPosts> findBySourceIdAndExternalPageIdAndPublishDateGreaterThan(Integer sourceId,String externalPageId, Date publishDate);

    List<BusinessPosts> findByPostIdIn(Collection<String> postIds);
    List<BusinessPosts> findByIdIn(List<Integer> ids);

    @Transactional
    @Modifying
    @Query("update BusinessPosts b set b.businessId = :businessId, b.enterpriseId = :enterpriseId where b.externalPageId = :pageId and sourceId = :sourceId")
    void updateBusinessIdWherePageId(@Param("pageId") String pageId,@Param("sourceId") Integer sourceId,@Param("businessId") Integer businessId,@Param("enterpriseId") Long enterpriseId);

    @Query(value = "select a.id,a.external_page_id,a.post_id,c.page_access_token from social.business_posts a " +
            "join social.business_fb_page c on c.facebook_page_id = a.external_page_id  " +
            "where a.source_id =110 and ((a.image_urls is not null and a.image_urls != '') or (a.video_urls is not null and a.video_urls != '')) \n#pageable\n",nativeQuery = true)
    List<Object[]> getDataForVideoAndPhoto(Pageable pageable);

    @Modifying
    @Transactional
    @Query("update BusinessPosts b set b.postId = :postId where id = :id")
    void updatePostIdWhereId(@Param("postId") String postId,@Param("id") Integer id);


    public static interface BPI {
        public Integer getId();
    }

    @Query("Select bp.postId from BusinessPosts bp where bp.postId in :postIds")
    List<String> findBusinessPostsIds(@Param("postIds") Collection<String> postIds);
  
    @Modifying
    @Transactional
    @Query("update BusinessPosts b set b.lastScanDate = :lastScanDate , b.nextScanDate = :nextScanDate where b.id in (:id)")
    void updateNextScanDateAndLastScanDateById(@Param("nextScanDate") Date nextScanDate,@Param("lastScanDate") Date lastScanDate,@Param("id") List<Integer> id);

    @Query(nativeQuery = true, value ="Select group_concat(bp.id) from social.business_posts bp where bp.source_id=108 and bp.enterprise_id is not null and bp.business_id is not null and bp.last_scan_date< :lastScanDate group by bp.external_page_id limit :lim")
    List<String> findTwitterPosts(@Param("lastScanDate") Date lastScanDate,@Param("lim") Integer lim);

    BusinessPosts findTopByExternalPageIdAndSourceIdAndBePostIdIsNullOrderByLastModifiedDateDesc(String externalPageId, Integer sourceId);

    @Query("Select DISTINCT bp.externalPageId from BusinessPosts bp where bp.imageUrls is not null and bp.publishDate> :publishDate and bp.sourceId = :sourceId")
    List<String> findExternalIds(@Param("publishDate") Date publishDate,@Param("sourceId") Integer sourceId);

    @Query("Select bp.id from BusinessPosts bp where bp.sourceId =195 and bp.postType = 'story' and bp.publishDate> :startDate and bp.publishDate< :endDate")
    List<Integer> findIgStoryInInterval(@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    @Query("SELECT b.postEndDate FROM BusinessPosts b WHERE b.id = :postId")
    LocalDateTime findPostEndDateById(@Param("postId") Integer postId);

    List<BusinessPosts> findByExternalPageIdAndSourceIdAndVideoUrlsIsNotNullOrderByPublishDateDesc(String externalPageId, Integer sourceId);

    @Transactional
    @Modifying
    @Query("update BusinessPosts b set b.bePostId = :bePostId, b.postText = :postText where b.postId = :postId")
    void updateEditPublishedContent(@Param("bePostId") Integer bePostId, @Param("postId") String postId, @Param("postText") String postText);

    @Transactional
    @Modifying
    @Query("update BusinessPosts b set b.bePostId = :bePostId, b.postText = :postText where b.externalPageId = :externalPageId and b.bePostId = :oldBePostId")
    void updateEditPublishedContentV2(@Param("bePostId") Integer bePostId, @Param("postText") String postText, @Param("oldBePostId") Integer oldBePostId, @Param("externalPageId") String externalPageId);

    List<BusinessPosts> findByBePostIdAndExternalPageId(Integer id, String externalPageId);

    @Query("SELECT bp FROM BusinessPosts bp " +
            "WHERE bp.publishDate >= :minPublishDate " +
            " AND bp.publishDate < :maxPublishDate"+
            " AND bp.postId NOT IN (" +
            "    SELECT si.postId FROM SocialAIUsefulPosts si" +
            ")")
    Page<BusinessPosts> findUnprocessedBusinessPosts(@Param("minPublishDate") Date minPublishDate,@Param("maxPublishDate") Date maxPublishDate,Pageable pageable);

    @Query("SELECT bp FROM BusinessPosts bp " +
            "WHERE bp.publishDate >= :minPublishDate " +
            " AND bp.publishDate < :maxPublishDate"+
            " AND bp.enterpriseId=:enterpriseId"+
            " AND bp.postId NOT IN (" +
            "    SELECT si.postId FROM SocialAIUsefulPosts si" +
            ")")
    Page<BusinessPosts> findUnprocessedBusinessPostsPerAccount(@Param("enterpriseId") Long enterpriseId,@Param("minPublishDate") Date minPublishDate,@Param("maxPublishDate") Date maxPublishDate,Pageable pageable);
}
