package com.birdeye.social.dao;

import com.birdeye.social.entities.SocialSamaySchedulingInfo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;


public interface SocialRetryPostRepo  extends JpaRepository<SocialSamaySchedulingInfo, Integer> {

     SocialSamaySchedulingInfo findById(Integer id);

     SocialSamaySchedulingInfo findBySocialPostId(Integer postId);
     List<SocialSamaySchedulingInfo> findBySocialPostIdAndIdentifier(Integer postId, String identifier);


}
