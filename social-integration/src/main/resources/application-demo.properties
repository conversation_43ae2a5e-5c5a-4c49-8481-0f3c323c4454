gnip.username=foo
gnip.password=bar
gnip.account=test-account
gnip.type=test-stream
aws.secret.enabled = true
aws.secret.region=us-east-1
aws.sm.gnip.secret.id=demo/social/secrets
#Gnip4j
#gnip.username=<EMAIL>
#gnip.password=P@55word
#gnip.account=BirdEye
#gnip.type=dev

#Kafka
kafka.server=*************:9092

#threadpool
max.pool.size=100
core.pool.size=50
threadpool.queue.capacity=15000
keep.alive.seconds=60

#consumer config
gnip.kafka=true
gnip.mock=true

gnip.baseUrl=http://api.demo.birdeye.com:8080/

server.port=9090

spring.datasource.url=***********************************************************************************
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=bazaar360

# Spring JPA
#turn off hibernate validation
spring.jpa.properties.javax.persistence.validation.mode=none
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect

#debug
debug=false