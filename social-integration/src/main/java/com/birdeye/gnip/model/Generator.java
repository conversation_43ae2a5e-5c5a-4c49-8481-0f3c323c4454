/**
 * 
 */
package com.birdeye.gnip.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Generator {

    private String displayName;
    private String link;

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    @Override
    public String toString() {
        return "Generator{" + "displayName=" + displayName + ", link=" + link + '}';
    }

}
