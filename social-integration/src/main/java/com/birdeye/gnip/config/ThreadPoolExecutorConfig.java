package com.birdeye.gnip.config;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 *
 *         9 Mar 2018
 **/
@Configuration
public class ThreadPoolExecutorConfig {
	
	@Bean
	public ThreadPoolExecutor threadPoolExecutor(@Value("${max.pool.size}") Integer maxPoolSize,
			@Value("${keep.alive.seconds}") Integer keepAliveSeconds, @Value("${core.pool.size}") Integer corePoolSize,
			@Value("${threadpool.queue.capacity}") Integer queueCapacity) {
		return new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAliveSeconds, TimeUnit.MILLISECONDS, 
				new ArrayBlockingQueue<>(queueCapacity));
		
	}
	
	 
}
