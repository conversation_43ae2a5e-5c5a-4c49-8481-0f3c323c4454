/**
 * 
 */
package com.birdeye.gnip.impl;

import java.net.URI;
import java.util.concurrent.ThreadPoolExecutor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.gnip.model.GNIPActivity;
import com.birdeye.gnip.model.Tweet;
import com.birdeye.gnip.model.UserAuthentication;
import com.birdeye.gnip.service.KafkaProducerService;
import com.birdeye.gnip.service.TweetConsumerService;
import com.birdeye.gnip.util.UtilsServiceImpl;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zaubersoftware.gnip4j.api.GnipFacade;
import com.zaubersoftware.gnip4j.api.GnipStream;
import com.zaubersoftware.gnip4j.api.StreamNotificationAdapter;
import com.zaubersoftware.gnip4j.api.UriStrategy;
import com.zaubersoftware.gnip4j.api.exception.GnipException;
import com.zaubersoftware.gnip4j.api.exception.TransportGnipException;
import com.zaubersoftware.gnip4j.api.impl.DefaultGnipFacade;
import com.zaubersoftware.gnip4j.api.impl.ImmutableGnipAuthentication;
import com.zaubersoftware.gnip4j.api.model.Activity;
import com.zaubersoftware.gnip4j.api.support.http.JRERemoteResourceProvider;

/**
 * <AUTHOR>
 *
 */
@Service
public class MockTweetConsumerKafka implements TweetConsumerService {
	
	private static final Logger		logger	= LoggerFactory.getLogger(MockTweetConsumerKafka.class);
	
	private static final String		TOPIC	= "social-mention";
	
	@Autowired
	private UserAuthentication		auth;
	
	@Autowired
	private KafkaProducerService	producer;
	
	@Autowired
	private ObjectMapper			mapper;
	
	@Autowired
	private ThreadPoolExecutor		executor;
	
	@Override
	public void consumeTweet() throws InterruptedException {
		final UriStrategy uriStrategy = new UriStrategy() {
			
			@Override
			public URI createStreamUri(String account, String streamName) {
				return URI.create("http://localhost:9898");
			}
			
			@Override
			public URI createRulesUri(String account, String streamName) {
				return null;
			}
			
			@Override
			public URI createRulesDeleteUri(String account, String streamName) {
				return null;
			}
			
			@Override
			public String getHttpMethodForRulesDelete() {
				return null;
			}
			
			@Override
			public URI createStreamUri(String account, String streamName, Integer backFillMinutes) {
				return null;
			}
			
			@Override
			public URI createRulesValidationUri(String account, String streamName) {
				return null;
			}
			
		};
		final StreamNotificationAdapter<Activity> feedObserver = new StreamNotificationAdapter<Activity>() {
			@Override
			public void notify(final Activity activity, final GnipStream stream) {
				try {
					logger.info("processing activity :: id : {} and body : {}", activity.getId(), mapper.writeValueAsString(activity));
					Tweet tweet = UtilsServiceImpl.getTweetFromActivity(activity);
					if (tweet.getBody().startsWith("RT @")) {
						logger.info("without encoding retweet :: id : {} and body : {}", tweet.getId(), tweet.getBody());
					} else {
						String tweetData = mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).writeValueAsString(tweet);
						producer.send(TOPIC, new GNIPActivity(tweetData));
						logger.info("processed tweet data : {} ", tweetData);
					}
				} catch (Exception ex) {
					logger.error("Error pushing activity to kafka : {}", ex);
				}
			}
			
			@Override
			public void notifyConnectionError(TransportGnipException e) {
				logger.error("CONNECT Error, Retry would be done : {}", e);
			}
			
			@Override
			public void notifyReConnectionError(GnipException e) {
				logger.error("RECONNECTION FAILED : {}", e);
			}
		};
		
		final JRERemoteResourceProvider resourceProvider = new JRERemoteResourceProvider(new ImmutableGnipAuthentication(auth.getUsername(), auth.getPassword()));
		final GnipFacade gnip = new DefaultGnipFacade(resourceProvider, uriStrategy);
		final GnipStream stream = gnip.createPowertrackStream(Activity.class).withAccount(auth.getAccount()).withType(auth.getType()).withExecutorService(executor).withObserver(feedObserver).build();
		
	}
}
