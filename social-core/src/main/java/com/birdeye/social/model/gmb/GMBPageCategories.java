package com.birdeye.social.model.gmb;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@ToString
public class GMBPageCategories implements Serializable {
    private GMBCategory primaryCategory;
    private List<GMBCategory> additionalCategories;
}
