package com.birdeye.social.constant;

/**
 *
 * <AUTHOR>
 */
public enum BusinessAccountTypeEnum {

    DIRECT(1, "Direct"),
    COBRANDED(2, "Cobranded"),
    WHITELABELED(3, "Whitelabel");

    int id;
    String name;

    private BusinessAccountTypeEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static BusinessAccountTypeEnum getBusinessAccoutTypeByName(String name) {
        for (BusinessAccountTypeEnum status : BusinessAccountTypeEnum.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }
}
