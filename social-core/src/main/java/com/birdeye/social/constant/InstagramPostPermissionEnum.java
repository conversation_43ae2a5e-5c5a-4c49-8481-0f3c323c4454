/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.birdeye.social.constant;

/**
 *
 * <AUTHOR>
 */
public enum InstagramPostPermissionEnum {
    ADS_MANAGEMENT(0, "ads_management"), BUSINESS_MANAGEMENT(1,"business_management"),
    INSTAGRAM_BASIC(2,"instagram_basic"), INSTAGRAM_CONTENT_PUBLISH(4,"instagram_content_publish"),
    PAGES_READ_ENGAGEMENT(5,"pages_read_engagement");

    final Integer id;
    final String name;

    private InstagramPostPermissionEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static InstagramPostPermissionEnum getPostStatusByName(String name) {
        for (InstagramPostPermissionEnum status : InstagramPostPermissionEnum.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }
    public static InstagramPostPermissionEnum getPostStatusById(Integer id) {
        for (InstagramPostPermissionEnum status : InstagramPostPermissionEnum.values()) {
            if (status.getId().equals(id)) {
                return status;
            }
        }
        return null;
    }
}