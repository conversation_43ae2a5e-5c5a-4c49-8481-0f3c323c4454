## Used by Docker images in our integration test
http.host: 0.0.0.0
network.host: 0.0.0.0
transport.host: 0.0.0.0

node.store.allow_mmap: false
cluster.routing.allocation.disk.threshold_enabled: false

xpack.license.self_generated.type: trial
xpack.security.enabled: true
xpack.security.http.ssl.enabled: true
xpack.security.http.ssl.client_authentication: optional
xpack.security.http.ssl.verification_mode: certificate
xpack.security.http.ssl.key:  ssl/elasticsearch.key
xpack.security.http.ssl.certificate: ssl/elasticsearch.crt
xpack.security.http.ssl.certificate_authorities: [ "ssl/ca/ca.crt" ]

xpack.security.transport.ssl.enabled: true
xpack.security.transport.ssl.key:  ssl/elasticsearch.key
xpack.security.transport.ssl.certificate: ssl/elasticsearch.crt
xpack.security.transport.ssl.certificate_authorities: [ "ssl/ca/ca.crt" ]
