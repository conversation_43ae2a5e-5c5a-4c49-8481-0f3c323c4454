package com.birdeye.gnip.mock.server.netty;

import com.birdeye.gnip.mock.server.GnipServer;
import com.birdeye.gnip.mock.server.GnipServerFactory;

/**
 * A {@link GnipServerFactory} that creates {@link NettyGnipServer} instances
 *
* <AUTHOR>
 */
public final class NettyGnipServerFactory implements GnipServerFactory {

    @Override
    public GnipServer createServer(final int port, final NettyChunkedInputFactory handlerFactory) {
        if (handlerFactory == null) {
            throw new IllegalArgumentException("The handlerFactory cannot be null");
        }
        return new NettyGnipServer(port, handlerFactory);
    }

}
