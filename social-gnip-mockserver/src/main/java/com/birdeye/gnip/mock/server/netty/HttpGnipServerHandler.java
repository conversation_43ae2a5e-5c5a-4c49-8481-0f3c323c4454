package com.birdeye.gnip.mock.server.netty;

import static org.jboss.netty.handler.codec.http.HttpHeaders.Names.CONTENT_TYPE;
import static org.jboss.netty.handler.codec.http.HttpHeaders.Names.LOCATION;
import static org.jboss.netty.handler.codec.http.HttpHeaders.Names.TRANSFER_ENCODING;
import static org.jboss.netty.handler.codec.http.HttpHeaders.setHeader;
import static org.jboss.netty.handler.codec.http.HttpMethod.GET;
import static org.jboss.netty.handler.codec.http.HttpResponseStatus.BAD_REQUEST;
import static org.jboss.netty.handler.codec.http.HttpResponseStatus.INTERNAL_SERVER_ERROR;
import static org.jboss.netty.handler.codec.http.HttpResponseStatus.METHOD_NOT_ALLOWED;
import static org.jboss.netty.handler.codec.http.HttpResponseStatus.MOVED_PERMANENTLY;
import static org.jboss.netty.handler.codec.http.HttpResponseStatus.OK;
import static org.jboss.netty.handler.codec.http.HttpVersion.HTTP_1_1;

import org.jboss.netty.buffer.ChannelBuffers;
import org.jboss.netty.channel.Channel;
import org.jboss.netty.channel.ChannelFuture;
import org.jboss.netty.channel.ChannelFutureListener;
import org.jboss.netty.channel.ChannelHandlerContext;
import org.jboss.netty.channel.ChannelStateEvent;
import org.jboss.netty.channel.ExceptionEvent;
import org.jboss.netty.channel.MessageEvent;
import org.jboss.netty.channel.SimpleChannelUpstreamHandler;
import org.jboss.netty.handler.codec.frame.TooLongFrameException;
import org.jboss.netty.handler.codec.http.DefaultHttpResponse;
import org.jboss.netty.handler.codec.http.HttpRequest;
import org.jboss.netty.handler.codec.http.HttpResponse;
import org.jboss.netty.handler.codec.http.HttpResponseStatus;
import org.jboss.netty.handler.stream.ChunkedInput;
import org.jboss.netty.util.CharsetUtil;

/**
 * <AUTHOR> Kumar
 *
 */
public class HttpGnipServerHandler extends SimpleChannelUpstreamHandler {

    private final GnipChunkedInput chunkedInput;

    public HttpGnipServerHandler(final GnipChunkedInput gnipChunkedInput) {
        this.chunkedInput = gnipChunkedInput;
    }

    @Override
    public void messageReceived(final ChannelHandlerContext ctx, final MessageEvent e) throws Exception {
        final HttpRequest request = (HttpRequest) e.getMessage();

        if (request.getMethod() != GET) {
            sendError(ctx, METHOD_NOT_ALLOWED);
            return;
        }

        final String uri = request.getUri();
        if (uri.equals("/")) {
            final HttpResponse response = new DefaultHttpResponse(HTTP_1_1, OK);
            response.setStatus(MOVED_PERMANENTLY);
            setHeader(response, LOCATION, "/fake-stream");

            final Channel ch = e.getChannel();
            ChannelFuture writeFuture;

            // Write the initial line and the header.
            writeFuture = ch.write(response);
            writeFuture.addListener(ChannelFutureListener.CLOSE);
        } else {
            final HttpResponse response = new DefaultHttpResponse(HTTP_1_1, OK);
            setHeader(response, TRANSFER_ENCODING, "chunked");
            response.setChunked(true);
            final Channel ch = e.getChannel();
            ch.write(response);
            ch.write(getChunkedInput());
//            sendError(ctx, NOT_FOUND);
//            return;
        }
    }

    public ChunkedInput getChunkedInput() {
        return chunkedInput;
    }

    @Override
    public void channelClosed(final ChannelHandlerContext ctx, final ChannelStateEvent e) throws Exception {
        super.channelClosed(ctx, e);
        System.out.println("Proxy Server Disconnect");
            
    }
    @Override
    public void channelConnected(final ChannelHandlerContext ctx, final ChannelStateEvent e) throws Exception {
        super.channelConnected(ctx, e);
    }
    
    @Override
    public void channelOpen(final ChannelHandlerContext ctx, final ChannelStateEvent e) throws Exception {
        final Channel channel = e.getChannel();
        NettyGnipServer.allChannels.add(channel);
        chunkedInput.setChannel(channel);
        super.channelOpen(ctx, e);
    }

    @Override
    public void exceptionCaught(final ChannelHandlerContext ctx, final ExceptionEvent e) throws Exception {
        final Channel ch = e.getChannel();
        final Throwable cause = e.getCause();
        if (cause instanceof TooLongFrameException) {
            sendError(ctx, BAD_REQUEST);
            return;
        }

        //cause.printStackTrace();
        if (ch.isConnected()) {
            sendError(ctx, INTERNAL_SERVER_ERROR);
        }
    }

    private void sendError(final ChannelHandlerContext ctx, final HttpResponseStatus status) {
        final HttpResponse response = new DefaultHttpResponse(HTTP_1_1, status);
        setHeader(response, CONTENT_TYPE, "text/plain; charset=UTF-8");
        response.setContent(ChannelBuffers.copiedBuffer(
                "Failure: " + status.toString() + "\r\n",
                CharsetUtil.UTF_8));

        // Close the connection as soon as the error message is sent.
        ctx.getChannel().write(response).addListener(ChannelFutureListener.CLOSE);
    }
}
