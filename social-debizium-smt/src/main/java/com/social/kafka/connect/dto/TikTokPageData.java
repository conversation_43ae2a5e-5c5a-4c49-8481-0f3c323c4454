package com.social.kafka.connect.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TikTokPageData {

    private Integer code;
    private String message;
    private Data data;
    @JsonProperty("request_id")
    private String requestId;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    @Override
    public String toString() {
        return "TikTokPageData{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", requestId='" + requestId + '\'' +
                '}';
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Data {
        @JsonProperty("audience_countries")
        private List<AudienceCountries> audienceCountries;

        @JsonProperty("total_likes")
        private Integer totalLikes;//The total number of times people have liked your published videos.

        @JsonProperty("is_verified")
        private Boolean isVerified;

        @JsonProperty("audience_cities")
        private List<AudienceCities> audienceCities;

        @JsonProperty("following_count")
        private Integer followingCount;

        @JsonProperty("username")
        private String userName;

        @JsonProperty("videos_count")
        private Integer videosCount;

        @JsonProperty("audience_genders")
        private List<AudienceGenders> audienceGenders;

        @JsonProperty("bio_description")
        private String bioDescription;

        @JsonProperty("audience_ages")
        private List<AudienceAges> audienceAges;

        @JsonProperty("profile_image")
        private String profileImage;

        @JsonProperty("profile_deep_link")
        private String profileDeepLink;

        @JsonProperty("metrics")
        private List<PageMetrics> metrics;

        @JsonProperty("followers_count")
        private Integer followersCount;//The total number of people who are following your account on the date.

        @JsonProperty("is_business_account")
        private Boolean isBusinessAccount;

        @JsonProperty("display_name")
        private String displayName;

        public List<AudienceCountries> getAudienceCountries() {
            return audienceCountries;
        }

        public void setAudienceCountries(List<AudienceCountries> audienceCountries) {
            this.audienceCountries = audienceCountries;
        }

        public Integer getTotalLikes() {
            return totalLikes;
        }

        public void setTotalLikes(Integer totalLikes) {
            this.totalLikes = totalLikes;
        }

        public Boolean getVerified() {
            return isVerified;
        }

        public void setVerified(Boolean verified) {
            this.isVerified = verified;
        }

        public List<AudienceCities> getAudienceCities() {
            return audienceCities;
        }

        public void setAudienceCities(List<AudienceCities> audienceCities) {
            this.audienceCities = audienceCities;
        }

        public Integer getFollowingCount() {
            return followingCount;
        }

        public void setFollowingCount(Integer followingCount) {
            this.followingCount = followingCount;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public Integer getVideosCount() {
            return videosCount;
        }

        public void setVideosCount(Integer videosCount) {
            this.videosCount = videosCount;
        }

        public List<AudienceGenders> getAudienceGenders() {
            return audienceGenders;
        }

        public void setAudienceGenders(List<AudienceGenders> audienceGenders) {
            this.audienceGenders = audienceGenders;
        }

        public String getBioDescription() {
            return bioDescription;
        }

        public void setBioDescription(String bioDescription) {
            this.bioDescription = bioDescription;
        }

        public List<AudienceAges> getAudienceAges() {
            return audienceAges;
        }

        public void setAudienceAges(List<AudienceAges> audienceAges) {
            this.audienceAges = audienceAges;
        }

        public String getProfileImage() {
            return profileImage;
        }

        public void setProfileImage(String profileImage) {
            this.profileImage = profileImage;
        }

        public String getProfileDeepLink() {
            return profileDeepLink;
        }

        public void setProfileDeepLink(String profileDeepLink) {
            this.profileDeepLink = profileDeepLink;
        }

        public List<PageMetrics> getMetrics() {
            return metrics;
        }

        public void setMetrics(List<PageMetrics> metrics) {
            this.metrics = metrics;
        }

        public Integer getFollowersCount() {
            return followersCount;
        }

        public void setFollowersCount(Integer followersCount) {
            this.followersCount = followersCount;
        }

        public Boolean getBusinessAccount() {
            return isBusinessAccount;
        }

        public void setBusinessAccount(Boolean businessAccount) {
            this.isBusinessAccount = businessAccount;
        }

        public String getDisplayName() {
            return displayName;
        }

        public void setDisplayName(String displayName) {
            this.displayName = displayName;
        }

        @Override
        public String toString() {
            return "Data{" +
                    "audienceCountries=" + audienceCountries +
                    ", totalLikes=" + totalLikes +
                    ", isVerified=" + isVerified +
                    ", audienceCities=" + audienceCities +
                    ", followingCount=" + followingCount +
                    ", userName='" + userName + '\'' +
                    ", videosCount=" + videosCount +
                    ", audienceGenders=" + audienceGenders +
                    ", bioDescription='" + bioDescription + '\'' +
                    ", audienceAges=" + audienceAges +
                    ", profileImage='" + profileImage + '\'' +
                    ", profileDeepLink='" + profileDeepLink + '\'' +
                    ", metrics=" + metrics +
                    ", followersCount=" + followersCount +
                    ", isBusinessAccount=" + isBusinessAccount +
                    ", displayName='" + displayName + '\'' +
                    '}';
        }
    }


    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PageMetrics {

        @JsonProperty("likes")
        private Integer likes;//Daily likes. The daily number of times people have liked your published videos.

        @JsonProperty("unique_video_views")
        private Integer uniqueVideoViews;

        @JsonProperty("daily_total_followers")
        private Integer dailyTotalFollowers;//Daily net growth. The daily change in the number of followers.

        @JsonProperty("followers_count")
        private Integer followersCount;

        @JsonProperty("phone_number_clicks")
        private Integer phoneNumberClicks;

        @JsonProperty("bio_link_clicks")
        private Integer bioLinkCLlicks;

        @JsonProperty("lead_submissions")
        private Integer leadSubmissions;

        @JsonProperty("shares")
        private Integer shares;

        @JsonProperty("audience_activity")
        private List<AudienceActivity> audienceActivity;

        @JsonProperty("app_download_clicks")
        private Integer appDownloadClicks;

        @JsonProperty("daily_new_followers")
        private Integer dailyNewFollowers;

        @JsonProperty("daily_lost_followers")
        private Integer dailyLostFollowers;//The daily number of followers you have lost.

        @JsonProperty("video_views")
        private Integer videoViews;

        @JsonProperty("address_clicks")
        private Integer addressClicks;

        @JsonProperty("engaged_audience")
        private Integer engagedAudience;

        @JsonProperty("comments")
        private Integer comments;

        @JsonProperty("email_clicks")
        private Integer emailClicks;

        @JsonProperty("profile_views")
        private Integer profileViews;

        @JsonProperty("date")//"2025-01-30"
        private String date;

        public Integer getLikes() {
            return likes;
        }

        public void setLikes(Integer likes) {
            this.likes = likes;
        }

        public Integer getUniqueVideoViews() {
            return uniqueVideoViews;
        }

        public void setUniqueVideoViews(Integer uniqueVideoViews) {
            this.uniqueVideoViews = uniqueVideoViews;
        }

        public Integer getDailyTotalFollowers() {
            return dailyTotalFollowers;
        }

        public void setDailyTotalFollowers(Integer dailyTotalFollowers) {
            this.dailyTotalFollowers = dailyTotalFollowers;
        }

        public Integer getFollowersCount() {
            return followersCount;
        }

        public void setFollowersCount(Integer followersCount) {
            this.followersCount = followersCount;
        }

        public Integer getPhoneNumberClicks() {
            return phoneNumberClicks;
        }

        public void setPhoneNumberClicks(Integer phoneNumberClicks) {
            this.phoneNumberClicks = phoneNumberClicks;
        }

        public Integer getBioLinkCLlicks() {
            return bioLinkCLlicks;
        }

        public void setBioLinkCLlicks(Integer bioLinkCLlicks) {
            this.bioLinkCLlicks = bioLinkCLlicks;
        }

        public Integer getLeadSubmissions() {
            return leadSubmissions;
        }

        public void setLeadSubmissions(Integer leadSubmissions) {
            this.leadSubmissions = leadSubmissions;
        }

        public Integer getShares() {
            return shares;
        }

        public void setShares(Integer shares) {
            this.shares = shares;
        }

        public List<AudienceActivity> getAudienceActivity() {
            return audienceActivity;
        }

        public void setAudienceActivity(List<AudienceActivity> audienceActivity) {
            this.audienceActivity = audienceActivity;
        }

        public Integer getAppDownloadClicks() {
            return appDownloadClicks;
        }

        public void setAppDownloadClicks(Integer appDownloadClicks) {
            this.appDownloadClicks = appDownloadClicks;
        }

        public Integer getDailyNewFollowers() {
            return dailyNewFollowers;
        }

        public void setDailyNewFollowers(Integer dailyNewFollowers) {
            this.dailyNewFollowers = dailyNewFollowers;
        }

        public Integer getDailyLostFollowers() {
            return dailyLostFollowers;
        }

        public void setDailyLostFollowers(Integer dailyLostFollowers) {
            this.dailyLostFollowers = dailyLostFollowers;
        }

        public Integer getVideoViews() {
            return videoViews;
        }

        public void setVideoViews(Integer videoViews) {
            this.videoViews = videoViews;
        }

        public Integer getAddressClicks() {
            return addressClicks;
        }

        public void setAddressClicks(Integer addressClicks) {
            this.addressClicks = addressClicks;
        }

        public Integer getEngagedAudience() {
            return engagedAudience;
        }

        public void setEngagedAudience(Integer engagedAudience) {
            this.engagedAudience = engagedAudience;
        }

        public Integer getComments() {
            return comments;
        }

        public void setComments(Integer comments) {
            this.comments = comments;
        }

        public Integer getEmailClicks() {
            return emailClicks;
        }

        public void setEmailClicks(Integer emailClicks) {
            this.emailClicks = emailClicks;
        }

        public Integer getProfileViews() {
            return profileViews;
        }

        public void setProfileViews(Integer profileViews) {
            this.profileViews = profileViews;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        @Override
        public String toString() {
            return "PageMetrics{" +
                    "likes=" + likes +
                    ", uniqueVideoViews=" + uniqueVideoViews +
                    ", dailyTotalFollowers=" + dailyTotalFollowers +
                    ", followersCount=" + followersCount +
                    ", phoneNumberClicks=" + phoneNumberClicks +
                    ", bioLinkCLlicks=" + bioLinkCLlicks +
                    ", leadSubmissions=" + leadSubmissions +
                    ", shares=" + shares +
                    ", audienceActivity=" + audienceActivity +
                    ", appDownloadClicks=" + appDownloadClicks +
                    ", dailyNewFollowers=" + dailyNewFollowers +
                    ", dailyLostFollowers=" + dailyLostFollowers +
                    ", videoViews=" + videoViews +
                    ", addressClicks=" + addressClicks +
                    ", engagedAudience=" + engagedAudience +
                    ", comments=" + comments +
                    ", emailClicks=" + emailClicks +
                    ", profileViews=" + profileViews +
                    ", date='" + date + '\'' +
                    '}';
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class AudienceActivity {

            @JsonProperty("hour")
            private String hour;

            @JsonProperty("count")
            private Integer count;

            public String getHour() {
                return hour;
            }

            public void setHour(String hour) {
                this.hour = hour;
            }

            public Integer getCount() {
                return count;
            }

            public void setCount(Integer count) {
                this.count = count;
            }

            @Override
            public String toString() {
                return "AudienceActivity{" +
                        "hour='" + hour + '\'' +
                        ", count=" + count +
                        '}';
            }
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AudienceGenders {
        @JsonProperty("gender")
        private String gender;

        @JsonProperty("percentage")
        private Double percentage;

        private Integer total;

        public String getGender() {
            return gender;
        }

        public void setGender(String gender) {
            this.gender = gender;
        }

        public Double getPercentage() {
            return percentage;
        }

        public void setPercentage(Double percentage) {
            this.percentage = percentage;
        }

        public Integer getTotal() {
            return total;
        }

        public void setTotal(Integer total) {
            this.total = total;
        }

        @Override
        public String toString() {
            return "AudienceGenders{" +
                    "gender='" + gender + '\'' +
                    ", percentage=" + percentage +
                    ", total=" + total +
                    '}';
        }
    }


    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AudienceAges {
        @JsonProperty("age")
        private String age;

        @JsonProperty("percentage")
        private Double percentage;

        public String getAge() {
            return age;
        }

        public void setAge(String age) {
            this.age = age;
        }

        public Double getPercentage() {
            return percentage;
        }

        public void setPercentage(Double percentage) {
            this.percentage = percentage;
        }

        @Override
        public String toString() {
            return "AudienceAges{" +
                    "age='" + age + '\'' +
                    ", percentage=" + percentage +
                    '}';
        }
    }


    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AudienceCities {
        @JsonProperty("city_name")
        private String city;

        @JsonProperty("percentage")
        private Double percentage;

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public Double getPercentage() {
            return percentage;
        }

        public void setPercentage(Double percentage) {
            this.percentage = percentage;
        }

        @Override
        public String toString() {
            return "AudienceCities{" +
                    "city='" + city + '\'' +
                    ", percentage=" + percentage +
                    '}';
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AudienceCountries {
        @JsonProperty("country")
        private String country;

        @JsonProperty("percentage")
        private Double percentage;

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public Double getPercentage() {
            return percentage;
        }

        public void setPercentage(Double percentage) {
            this.percentage = percentage;
        }

        @Override
        public String toString() {
            return "AudienceCountries{" +
                    "country='" + country + '\'' +
                    ", percentage=" + percentage +
                    '}';
        }
    }
}