package com.birdeye.social.config;


import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;

import com.amazonaws.auth.InstanceProfileCredentialsProvider;
import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.AWSSecretsManagerClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class AwsSecretManangerConfig {


    @Value("${aws.secret.region}")
    private String region;


    @Bean("defaultSecretsManagerClient")
    @ConditionalOnProperty(prefix = "aws.secret", value = "enabled", havingValue = "true")
    public AWSSecretsManager getDefaultSecretsManagerClient() {
        return AWSSecretsManagerClientBuilder.standard().withCredentials(new DefaultAWSCredentialsProviderChain())
                .withRegion(region).build();
    }

}
