package com.birdeye.social.config;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.SocialRestTemplate;

@Configuration
public class RestClientConfig {

	// Determines the timeout in milliseconds until a connection is established.
	private static final int CONNECT_TIMEOUT = 45000;
	// Returns the timeout in milliseconds used when requesting a connection
	// from the connection manager.
	private static final int REQUEST_TIMEOUT = 45000;
	// Defines the socket timeout (SO_TIMEOUT) in milliseconds, which is the
	// timeout for waiting for data or,
	// put differently, a maximum period inactivity between two consecutive data
	// packets).
	private static final int SOCKET_TIMEOUT = 60000;
	
	private static final int MAX_TOTAL_CONNECTIONS = 150;

	private static final int DEFAULT_MAX_PER_ROUTE = 50; // TODO move to property file
	
	private static final int IDLE_CONNECTION_WAIT_TIME = 30;

	private final RateLimitInterceptor rateLimitInterceptor;

	public RestClientConfig(RateLimitInterceptor rateLimitInterceptor) {
		this.rateLimitInterceptor = rateLimitInterceptor;
	}


	@Bean("socialRestTemplate")
	@Primary
	public RestTemplate restTemplate() {
		RestTemplate restTemplate = new SocialRestTemplate(new HttpComponentsClientHttpRequestFactory(getHttpClient()));
		restTemplate.setMessageConverters(getMessageConverters());

		//restTemplate.setInterceptors(getInterceptors());
		restTemplate.getInterceptors().add(rateLimitInterceptor);
		restTemplate.getInterceptors().add(new CustomHeaderInterceptor("SERVICE-NAME", "social"));

		return restTemplate;
	}

	
	// Setup Httpclient
	private CloseableHttpClient getHttpClient() {
		RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(REQUEST_TIMEOUT)
				.setConnectTimeout(CONNECT_TIMEOUT).setSocketTimeout(SOCKET_TIMEOUT).build();
		// Review for https support. SocketFactory may needs to be customized
		PoolingHttpClientConnectionManager poolingConnectionManager = new PoolingHttpClientConnectionManager();
		poolingConnectionManager.setDefaultMaxPerRoute(DEFAULT_MAX_PER_ROUTE);
		poolingConnectionManager.setMaxTotal(MAX_TOTAL_CONNECTIONS);
		
		return HttpClients.custom().setDefaultRequestConfig(requestConfig)
				.setConnectionManager(poolingConnectionManager)
				.evictIdleConnections(IDLE_CONNECTION_WAIT_TIME, TimeUnit.SECONDS).build();
	}
	
	//Setup message converter : jackson 2 message converter
	private List<HttpMessageConverter<?>> getMessageConverters() {
		List<HttpMessageConverter<?>> converters = new ArrayList<HttpMessageConverter<?>>();
		converters.add(new FormHttpMessageConverter());
		MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverternew = new MappingJackson2HttpMessageConverter();
		mappingJackson2HttpMessageConverternew.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON, MediaType.APPLICATION_OCTET_STREAM, MediaType.TEXT_PLAIN));
		converters.add(new MappingJackson2HttpMessageConverter());
		converters.add(new ByteArrayHttpMessageConverter());
		converters.add(new StringHttpMessageConverter());
		return converters;
	}
	
	//Rest template : Request and response interceptor
	/*	private List<ClientHttpRequestInterceptor> getInterceptors() {
		List<ClientHttpRequestInterceptor> interceptors = new ArrayList<ClientHttpRequestInterceptor>();
		interceptors.add(new LoggingRequestInterceptor());
		return interceptors;
	}*/
	
}
