/**
 * 
 */
package com.birdeye.social.config;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.birdeye.social.google.GoogleApiKeys;
import com.birdeye.social.platform.dao.GoogleplusApiKeyRepo;
import com.birdeye.social.platform.entities.GoogleplusApiKey;

/**
 * <AUTHOR>
 *
 */
@Configuration
public class GooglePlusKeysConfig {
	
	@Autowired
	private GoogleplusApiKeyRepo gplusKeyRepo;
	
	@Bean
	public GoogleApiKeys getGplusApiKeys() {
		GoogleApiKeys keys = new GoogleApiKeys();
		keys.setApiKeys(getApiKeys());
		return keys;

	}
	
	private Map<String,List<String>> getApiKeys(){
		List<GoogleplusApiKey> keys = gplusKeyRepo.findAll();
		return populateKeyMap(keys);
	}
	
	private Map<String,List<String>> populateKeyMap(List<GoogleplusApiKey> apiKeys){
		Map<String,List<String>> keyMap = new HashMap<>();
		apiKeys.stream().forEach(key -> {
			String mapKey = key.getKeyType() == null ? "global" : key.getKeyType();
			List<String> apiKeyList = keyMap.get(mapKey);
			if(apiKeyList == null) {
				apiKeyList = new ArrayList<>();
			}
			apiKeyList.add(key.getApiKey());
			keyMap.put(mapKey, apiKeyList);
		});
		return keyMap;
	}
}
