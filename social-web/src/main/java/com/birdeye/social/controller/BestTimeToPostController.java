package com.birdeye.social.controller;

import com.birdeye.social.external.request.btprequest.BTPPriorityRequest;
import com.birdeye.social.external.request.btprequest.SocialAIRequest;
import com.birdeye.social.external.request.btprequest.SocialBTPRequest;
import com.birdeye.social.external.response.btp.BTPTop3Times;
import com.birdeye.social.external.response.btp.ScheduleBTPResponse;
import com.birdeye.social.external.response.btp.SocialBTPCalenderResponse;
import com.birdeye.social.external.response.btp.SocialBTPHeatMapResponse;
import com.birdeye.social.model.SocialPostAdditionalRequest;
import com.birdeye.social.service.btp.BestTimeToPostService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/social/post/btp")
public class BestTimeToPostController {

    private static final Logger logger = LoggerFactory.getLogger(BestTimeToPostController.class);
    @Autowired
    private BestTimeToPostService bestTimeToPostService;

    @PostMapping(value = "/heat-map/save")
    public ResponseEntity<Void> saveHeatMapData(@RequestBody SocialAIRequest socialAIRequest){
        bestTimeToPostService.saveHeatMapData(socialAIRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/heat-map/save/sync")
    public ResponseEntity<Void> saveHeatMapData(@RequestBody SocialAIRequest socialAIRequest,
                                                @RequestParam("numberOfWeeks") int numberOfWeeks){
        bestTimeToPostService.saveHeatMapData(socialAIRequest,numberOfWeeks);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/daily-stats/save")
    public ResponseEntity<Void> saveDailyStats(@RequestBody SocialAIRequest socialAIRequest){
        bestTimeToPostService.saveDailyStats(socialAIRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/daily-stats/save/sync")
    public ResponseEntity<Void> saveDailyStats(@RequestBody SocialAIRequest socialAIRequest,
                                               @RequestParam("numberOfWeeks") int numberOfWeeks){
        bestTimeToPostService.saveDailyStats(socialAIRequest,numberOfWeeks);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/reports")
    public ResponseEntity<SocialBTPHeatMapResponse> getHeatMapData(@RequestBody SocialBTPRequest socialBTPRequest){
        logger.info("Request received to getHeatMapData for data :{}",socialBTPRequest);
        return new ResponseEntity<>(bestTimeToPostService.getHeatMapData(socialBTPRequest),HttpStatus.OK);
    }

    @PostMapping(value = "/schedule/reseller")
    public ResponseEntity<ScheduleBTPResponse> getScheduleDataForReseller(@RequestHeader("time-zone-id") String timezone,
                                                                          @RequestBody SocialBTPRequest socialBTPRequest){
        logger.info("Request received to getScheduleDataForReseller for data :{}",socialBTPRequest);
        return new ResponseEntity<>(bestTimeToPostService.getScheduleDataForReseller(socialBTPRequest,timezone),HttpStatus.OK);
    }

    @PostMapping(value = "/schedule")
    public ResponseEntity<ScheduleBTPResponse> getScheduleData(@RequestHeader("time-zone-id") String timezone,
                                                               @RequestBody SocialBTPRequest socialBTPRequest){
        logger.info("Request received to getScheduleData for data :{} and timezone:{}",socialBTPRequest,timezone);
        return new ResponseEntity<>(bestTimeToPostService.getScheduleData(socialBTPRequest,timezone),HttpStatus.OK);
    }

    @PostMapping(value = "/per-day")
    public ResponseEntity<SocialBTPCalenderResponse> getScheduleTimePerDayForCalender(@RequestHeader("time-zone-id") String timezone,
                                                                                      @RequestHeader(value = "account-id", required = false) Integer enterpriseId,
                                                                                      @RequestBody SocialBTPRequest socialBTPRequest){
        logger.info("Request received to getScheduleTimePerDayForCalender for data :{} with enterprise ID: {}",socialBTPRequest, enterpriseId);
        // Set enterprise ID in request if provided via header
        if (enterpriseId != null) {
            socialBTPRequest.setEnterpriseId(enterpriseId);
        }
        return new ResponseEntity<>(bestTimeToPostService.getScheduleTimePerDayForCalender(socialBTPRequest,timezone),HttpStatus.OK);
    }

    @PostMapping(value = "/per-week")
    public ResponseEntity<BTPTop3Times> getScheduleTimePerWeek(@RequestHeader("time-zone-id") String timezone,
                                                               @RequestBody SocialBTPRequest socialBTPRequest){
        logger.info("Request received to getScheduleTimePerWeek for data :{}",socialBTPRequest);
        return new ResponseEntity<>(bestTimeToPostService.getScheduleTimePerWeek(socialBTPRequest,timezone),HttpStatus.OK);
    }

    @DeleteMapping(value = "/priority/{id}")
    public ResponseEntity<Void> deletePriority(@PathVariable("id") Integer id){
        logger.info("Delete priority with id :{}",id);
        bestTimeToPostService.deletePriority(id);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PatchMapping(value = "/priority/add-update")
    public ResponseEntity<Void> addOrUpdatePriority(@RequestBody BTPPriorityRequest btpPriorityRequest){
        logger.info("Request received to add or update priority :{}",btpPriorityRequest);
        bestTimeToPostService.addOrUpdatePriority(btpPriorityRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/add/info")
    public ResponseEntity<Void> addOrUpdateSocialPostAdditionalInfo(@RequestBody SocialPostAdditionalRequest request){
        logger.info("Request received to add or update info :{}",request);
        bestTimeToPostService.addOrUpdateSocialPostAdditionalInfo(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping(value = "/migrate/btp")
    public ResponseEntity<Void> migrateBtp(@RequestBody SocialAIRequest socialAIRequest){
        bestTimeToPostService.migrateBtp(socialAIRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
