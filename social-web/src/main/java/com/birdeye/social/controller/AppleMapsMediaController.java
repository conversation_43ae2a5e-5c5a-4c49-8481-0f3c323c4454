package com.birdeye.social.controller;

import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.service.appleMaps.AppleMapsMediaService;
import com.birdeye.social.sro.appleMaps.AppleBrandMediaDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/social/apple/media")
public class AppleMapsMediaController {

    private final AppleMapsMediaService appleMapsMediaService;

    /* ------------------------------*/
    @PostMapping("/handle-brand-media")
    public ResponseEntity<?> handleBrandMedia(@Valid @RequestBody AppleBrandMediaDto appleBrandMediaDto, BindingResult bindingResult){
        if(bindingResult.hasErrors()){
            log.error("[AppleMapsMediaController] Validation failure:{}", bindingResult.getAllErrors());
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, "Validation failure: ".concat(bindingResult.getFieldErrors().stream().map(fe->fe.getField()).collect(Collectors.joining(","))));
        }
        return ResponseEntity.ok(this.appleMapsMediaService.handleBrandMedia(appleBrandMediaDto));
    }

    @GetMapping("/fetch-media-status")
    public ResponseEntity<?> fetchMediaStatus(@RequestParam("appleCompanyId") String appleCompanyId, @RequestParam("appleMediaId") String appleMediaId){
        return ResponseEntity.ok(this.appleMapsMediaService.getImageMetaData(appleCompanyId, appleMediaId));
    }

    @GetMapping("/fetch-businessAsset-status")
    public ResponseEntity<?> fetchBusinessAssetStatus(@RequestParam("appleCompanyId") String appleCompanyId, @RequestParam("appleBusinessId") String appleBusinessId, @RequestParam("businessAssetId") String businessAssetId){
        return ResponseEntity.ok(this.appleMapsMediaService.getBusinessAsset(appleCompanyId, appleBusinessId, businessAssetId));
    }

    @GetMapping("/fetch-business-status")
    public ResponseEntity<?> fetchBusinessStatus(@RequestParam("appleCompanyId") String appleCompanyId, @RequestParam("appleBusinessId") String appleBusinessId){
        return ResponseEntity.ok(this.appleMapsMediaService.getBusiness(appleCompanyId, appleBusinessId));
    }
}
