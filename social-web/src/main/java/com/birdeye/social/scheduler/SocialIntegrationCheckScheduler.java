/**
 *
 */
package com.birdeye.social.scheduler;

import java.util.Collection;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.birdeye.social.scheduler.dto.ExternalIntegration;

/**
 * <AUTHOR>
 *
 */
public abstract class SocialIntegrationCheckScheduler {
	private static Logger LOGGER = LoggerFactory.getLogger(SocialIntegrationCheckScheduler.class);
	
	
	// Scheduled
	public void updateIntegrationStatus() {
		if (!isEnabled()) {
			LOGGER.info("SocialIntegrationCheckScheduler job for {} is disabled", getJobText());
			return;
		}
		LOGGER.info("Starting job :: SocialIntegrationCheckScheduler {} ", getJobText());
		Collection<? extends ExternalIntegration> activePages = getIntegrations();
		LOGGER.info("SocialIntegrationCheckScheduler {} :: Number of active pages found for current batch is {} ", getJobText(), CollectionUtils.size(activePages));
		if (CollectionUtils.isEmpty(activePages)) {
			LOGGER.info("Terminating job :: SocialIntegrationCheckScheduler {} as active pages are empty", getJobText());
			return;
		}
		activePages.parallelStream().forEach(page -> updateIntegrationStatusForAPage(page));
		LOGGER.info("Terminating job :: SocialIntegrationCheckScheduler {} is finished SUCCESSFULLY.", getJobText());
	}

	public void updateIntegrationStatusV2() {
//		if (!isEnabled()) {
//			LOGGER.info("SocialIntegrationCheckScheduler job for {} is disabled", getJobText());
//			return;
//		}
		LOGGER.info("Starting job :: SocialIntegrationCheckScheduler {} ", getJobText());
		Collection<? extends ExternalIntegration> activePages = getIntegrationsV2();
		LOGGER.info("SocialIntegrationCheckScheduler {} :: Number of active pages found for current batch is {} ", getJobText(), CollectionUtils.size(activePages));
		if (CollectionUtils.isEmpty(activePages)) {
			LOGGER.info("Terminating job :: SocialIntegrationCheckScheduler {} as active pages are empty", getJobText());
			return;
		}
		activePages.parallelStream().forEach(page -> updateIntegrationStatusForAPageV2(page));
		LOGGER.info("Terminating job :: SocialIntegrationCheckScheduler {} is finished SUCCESSFULLY.", getJobText());
	}
	
	public abstract void updateIntegrationStatusForAPage(ExternalIntegration integration);

	public void updateIntegrationStatusForAPageV2(ExternalIntegration integration) {
		throw new UnsupportedOperationException("Need implementation of external integration class");
	}

	public boolean isEnabled() {
		return false;
	}
	
	public String getJobText() {
		return null;
	}
	
	public Collection<? extends ExternalIntegration> getIntegrations() {
		throw new UnsupportedOperationException("Need implementation of external integration class");
	}

	public Collection<? extends ExternalIntegration> getIntegrationsV2() {
		throw new UnsupportedOperationException("Need implementation of external integration class");
	}
	
	// TODO: This needs to be defined for parallel policies
	// static class ExecutorConfig {
	// private int nThreads;
	// }
	//
	// public ExecutorConfig getConfig(){
	// return null;
	// }
}
