{
  "size": 0,
  "query": {
    "bool": {
      "must": [
        {
          "term": {
            "source_id": "${sourceIds}"
          }
        },
        {
           "terms": {
              "page_id": [
                ${pageIds}
              ]
           }
        }
      ]
    }
  },
  "aggs": {
    "data": {
      "filter": {
	      "bool": {
	        "must": [{
            "range": {
              "posted_date": {
              "gte": "${startDate}",
              "lt": "${endDate}"
              }
            }
          }]
	     }
	    },
	    "aggs": {
	      "histogram": {
	        "aggs": {
	          "post_count": {
	            "value_count": {
	              "field": "page_id"
              }
            }
	        },
	        "date_histogram": {
	          "field": "posted_date",
              "interval": "${type}",
              "format": "yyyy-MM-dd HH:mm:ss",
              "keyed": true
	        }
	      }
	    }
    },
    "prev_data":{
      "filter": {
	      "bool": {
	        "must": [{
            "range": {
              "posted_date": {
              "gte": "${prevDate}",
              "lt": "${startDate}"
              }
            }
          }]
	     }
	    },
	    "aggs": {
	      "post_count": {
	            "value_count": {
	              "field": "page_id"
              }
            }
      }
    },
    "current_data":{
      "filter": {
	      "bool": {
	        "must": [{
            "range": {
              "posted_date": {
              "gte": "${startDate}",
              "lt": "${endDate}"
              }
            }
          }]
	    }
    },
    "aggs": {
	    "post_count": {
	        "value_count": {
	            "field": "page_id"
            }
        }
      }
    }
  }
}