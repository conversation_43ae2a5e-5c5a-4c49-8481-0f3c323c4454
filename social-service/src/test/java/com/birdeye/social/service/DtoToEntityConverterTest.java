package com.birdeye.social.service;

import static org.junit.Assert.assertEquals;

import java.util.Collections;
import java.util.Map;

import org.junit.Test;

import com.birdeye.social.entities.Mention;
import com.birdeye.social.model.GNIPActivity;
import com.birdeye.social.utils.JSONUtils;

public class DtoToEntityConverterTest {
	
	@Test
	public void testForFB_WithReviwer(){
		//@formatter:off
		String fbactivity = "{\"source\": \"facebook\","
					+ " \"post\":{"
					+ 		"\"name\": null, "
					+ 		"\"values\": null, "
					+ 		"\"id\": \"***************_***************\", "
					+ 		"\"message\": \"<PERSON><PERSON>'s lasik\","
					+ 		"\"created_time\": \"2019-02-16T16:23:08+0000\","
					+ 		"\"postDate\": *************,"
					+ 		"\"from\": {"
					+ 			"\"name\": \"LasikPlus - St. Louis\", "
					+ 			"\"id\": \"***************\""
					+ "}}}";
		//@formatter:on
		Mention mention = DtoToEntityConverter.forFB(JSONUtils.fromJSON(fbactivity, GNIPActivity.class), 1234567890l,12112);
		assertEquals("facebook",mention.getChannelType());
		assertEquals(1234567890l,mention.getAccountId().longValue());
		assertEquals("***************_***************",mention.getExternalId());
		assertEquals(*************l,mention.getMentionDate().getTime());
		assertEquals("https://www.facebook.com/***************/posts/***************",mention.getMentionUrl());
		assertEquals("Dione's lasik",mention.getComments());
		// Test reviewer info.
		@SuppressWarnings("unchecked")
		Map<String,String> userInfo = JSONUtils.fromJSON(mention.getUserInfo(), Map.class);
		assertEquals("***************",userInfo.get("profileId"));
		assertEquals("LasikPlus - St. Louis",userInfo.get("name"));
		assertEquals("https://www.facebook.com/***************",userInfo.get("profileUrl"));
	}
	 

	@Test
	public void testForFB_WithoutReviwer(){
		//@formatter:off
		String fbactivity = "{\"source\": \"facebook\","
					+ " \"post\":{"
					+ 		"\"name\": null, "
					+ 		"\"values\": null, "
					+ 		"\"id\": \"***************_****************\", "
					+ 		"\"message\": \"Why doesn\'t this email link work?\\n\\<EMAIL>\\n\\nWhy can I not complete my account registration to pay my bill on line?\","
					+ 		"\"created_time\": \"2019-02-28T21:04:05+0000\","
					+ 		"\"postDate\": *************,"
					+ 		"\"from\": null"
					+ "}}";
		//@formatter:on
		Mention mention = DtoToEntityConverter.forFB(JSONUtils.fromJSON(fbactivity, GNIPActivity.class), 2468135790l,121211);
		assertEquals("facebook",mention.getChannelType());
		assertEquals(2468135790l,mention.getAccountId().longValue());
		assertEquals("***************_****************",mention.getExternalId());
		assertEquals(*************l,mention.getMentionDate().getTime());
		assertEquals("https://www.facebook.com/***************/posts/****************",mention.getMentionUrl());
		assertEquals("Why doesn't this email link work?\n\<EMAIL>\n\nWhy can I not complete my account registration to pay my bill on line?",mention.getComments());
		@SuppressWarnings("unchecked")
		Map<String,String> userInfo = JSONUtils.fromJSON(mention.getUserInfo(), Map.class);
		assertEquals(0,userInfo.size());
	}
	
	@Test
	public void testForTwitter(){
		//@formatter:off
		String twitterActivity="{"
				+"\"id\":\"tag:search.twitter.com,2005:1097313258602516480\","
				+" \"actor\":"
					+" {"
					+"  \"displayName\":\"TiaHillary\","
					+"  \"followersCount\":null,"
					+"  \"statusesCount\":3969,"
					+"  \"preferredUsername\":\"TiaHillary\","
					+"  \"link\":\"http://www.twitter.com/TiaHillary\","
					+"  \"location\":null,"
					+"  \"image\":\"https://pbs.twimg.com/profile_images/801695389120241665/iHGv9kbm_normal.jpg\""
					+" },"
				+" \"body\":\"I checked in at MOD Pizza on #Yelp https://t.co/DMhbLSDM0P\","
				+" \"postedTime\":*************,"
				+" \"link\":\"http://twitter.com/TiaHillary/statuses/1097313258602516480\","
				+" \"location\":null,"
				+" \"gnip\":{"
					+"\"matching_rules\":"
						+"  ["
						+"     {"
						+"       \"tag\":\"146170658701161\","
						+"       \"value\":null,"
						+"      \"id\":9534951***********"
						+"    }"
						+"  ]"
					+" },"
				+ "\"generator\":{"
					+"  \"displayName\":\"Yelp\","
					+"  \"link\":\"http://www.yelp.com/\""
					+	"},"
				+ "\"twitter_entities\":{"
					+"   \"media\":null"
					+"	}"
				+" }";
			//@formatter:on
		GNIPActivity activity = new GNIPActivity();
		activity.setActivity(twitterActivity);
		Mention mention = DtoToEntityConverter.forTwitter(activity);
		assertEquals("twitter",mention.getChannelType());
		assertEquals(146170658701161l,mention.getAccountId().longValue());
		assertEquals("tag:search.twitter.com,2005:1097313258602516480",mention.getExternalId());
		assertEquals(*************l,mention.getMentionDate().getTime());
		assertEquals("http://twitter.com/TiaHillary/statuses/1097313258602516480",mention.getMentionUrl());
		assertEquals("I checked in at MOD Pizza on #Yelp https://t.co/DMhbLSDM0P",mention.getComments());
		// Test reviewer info.
		@SuppressWarnings("unchecked")
		Map<String,String> userInfo = JSONUtils.fromJSON(mention.getUserInfo(), Map.class);
		assertEquals("TiaHillary",userInfo.get("profileId"));
		assertEquals("TiaHillary",userInfo.get("name"));
		assertEquals("http://www.twitter.com/TiaHillary",userInfo.get("profileUrl"));
		assertEquals("https://pbs.twimg.com/profile_images/801695389120241665/iHGv9kbm_normal.jpg",userInfo.get("profileImage"));
	
	}
	
	// Currently location data is not getting used in Mention
	@Test
	public void testForTwitterWithLocationData(){
		//@formatter:off
		String twitterActivity="{"
				+"\"id\":\"tag:search.twitter.com,2005:1097313258602516480\","
				+" \"actor\":"
					+" {"
					+"  \"displayName\":\"TiaHillary\","
					+"  \"followersCount\":null,"
					+"  \"statusesCount\":3969,"
					+"  \"preferredUsername\":\"TiaHillary\","
					+"  \"link\":\"http://www.twitter.com/TiaHillary\","
					+"  \"location\":null,"
					+"  \"image\":\"https://pbs.twimg.com/profile_images/801695389120241665/iHGv9kbm_normal.jpg\""
					+" },"
				+" \"body\":\"I checked in at MOD Pizza on #Yelp https://t.co/DMhbLSDM0P\","
				+" \"postedTime\":*************,"
				+" \"link\":\"http://twitter.com/TiaHillary/statuses/1097313258602516480\","
				+" \"location\":  {"
				+ "    \"displayName\": \"Nepal\"" 
				+ "  },"
				+" \"gnip\":{"
					+"\"matching_rules\":"
						+"  ["
						+"     {"
						+"       \"tag\":\"146170658701161\","
						+"       \"value\":null,"
						+"      \"id\":9534951***********"
						+"    }"
						+"  ]"
					+" },"
				+ "\"generator\":{"
					+"  \"displayName\":\"Yelp\","
					+"  \"link\":\"http://www.yelp.com/\""
					+	"},"
				+ "\"twitter_entities\":{"
					+"   \"media\":null"
					+"	}"
				+" }";
			//@formatter:on
		GNIPActivity activity = new GNIPActivity();
		activity.setActivity(twitterActivity);
		Mention mention = DtoToEntityConverter.forTwitter(activity);
		assertEquals("twitter",mention.getChannelType());
		assertEquals(146170658701161l,mention.getAccountId().longValue());
		assertEquals("tag:search.twitter.com,2005:1097313258602516480",mention.getExternalId());
		assertEquals(*************l,mention.getMentionDate().getTime());
		assertEquals("http://twitter.com/TiaHillary/statuses/1097313258602516480",mention.getMentionUrl());
		assertEquals("I checked in at MOD Pizza on #Yelp https://t.co/DMhbLSDM0P",mention.getComments());
		// Test reviewer info.
		@SuppressWarnings("unchecked")
		Map<String,String> userInfo = JSONUtils.fromJSON(mention.getUserInfo(), Map.class);
		assertEquals("TiaHillary",userInfo.get("profileId"));
		assertEquals("TiaHillary",userInfo.get("name"));
		assertEquals("http://www.twitter.com/TiaHillary",userInfo.get("profileUrl"));
		assertEquals("https://pbs.twimg.com/profile_images/801695389120241665/iHGv9kbm_normal.jpg",userInfo.get("profileImage"));
	
	}
	
	@Test
	public void testForYoutube(){
		//@formatter:off
	String youtubeActivity="{"
		    +"\"source\": \"youtube\","
		    +"\"youtubeActivity\": {"
		    +"    \"id\": {"
		    +"        \"kind\": \"youtube#video\","
		    +"        \"videoId\": \"ngK1H0MQZbU\""
		    +"    },"
		    +"    \"url\": \"https://www.youtube.com/watch?v=ngK1H0MQZbU\","
		    +"    \"channelTitle\": null,"
		    +"    \"snippet\": {"
		    +"        \"channelId\": \"UCyrQBJmX3pv3UhFjvQiZTbw\","
		    +"        \"publishedAt\": \"2019-02-17T10:49:27.000Z\","
		    +"        \"activityDate\": 1550400567000,"
		    +"        \"title\": \"Cold pyro\","
		    +"        \"description\": \"Best quality cold pyro.\","
		    +"        \"thumbnails\": {"
		    +"            \"medium\": {"
		    +"                \"url\": \"https://i.ytimg.com/vi/ngK1H0MQZbU/mqdefault.jpg\""
		    +"            },"
		    +"            \"high\": {"
		    +"                \"url\": \"https://i.ytimg.com/vi/ngK1H0MQZbU/hqdefault.jpg\""
		    +"            }"
		    +"         }"
		    +"   }}}";
		//@formatter:on
		Mention mention = DtoToEntityConverter.forYoutube(JSONUtils.fromJSON(youtubeActivity, GNIPActivity.class), 1234567890l,1111,Collections.emptyMap());
		assertEquals("youtube",mention.getChannelType());
		assertEquals(1234567890l,mention.getAccountId().longValue());
		assertEquals("ngK1H0MQZbU",mention.getExternalId());
		assertEquals(1550400567000l,mention.getMentionDate().getTime());
		assertEquals("https://www.youtube.com/watch?v=ngK1H0MQZbU",mention.getMentionUrl());
		assertEquals("Best quality cold pyro.",mention.getComments());
		// Test reviewer info.
		@SuppressWarnings("unchecked")
		Map<String,String> userInfo = JSONUtils.fromJSON(mention.getUserInfo(), Map.class);
		assertEquals("UCyrQBJmX3pv3UhFjvQiZTbw",userInfo.get("profileId"));
		//assertEquals("Cold pyro",userInfo.get("name"));
		assertEquals("https://www.youtube.com/channel/UCyrQBJmX3pv3UhFjvQiZTbw",userInfo.get("profileUrl"));
	}

}
