package com.birdeye.social.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.birdeye.social.constant.Constants;
import org.junit.Assert;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.PageRequest;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.dao.SocialTwitterAccountRepository;
import com.birdeye.social.dto.BusinessEntity;
import com.birdeye.social.dto.BusinessLocationEntity;
import com.birdeye.social.entities.BusinessTwitterAccounts;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.SocialPageListInfo;
import com.birdeye.social.model.ReviewOptionDto;
import com.birdeye.social.platform.dao.BusinessTwitterPageRepository;
import com.birdeye.social.platform.dao.BusinessUserRepository;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.platform.entities.BusinessTwitterPage;
import com.birdeye.social.platform.entities.User;
import com.birdeye.social.sro.LocationPageMapping;
import com.birdeye.social.sro.LocationPageMappingRequest;
import com.birdeye.social.utils.BusinessUtilsService;

/**
 * //<AUTHOR>
 *
 */
public class TwitterSocialAccountServiceImplTest {
	
	//@Mock
	private BusinessUtilsService			businessUtilService;
	
	//@Mock
	private BusinessUserRepository			businessUserRepository;
	
	//@Mock
	private BusinessTwitterPageRepository	businessTwitterPageRepo;
	
	//@Mock
	private SocialTwitterAccountRepository	socialTwitterRepo;
	
	//@Mock
	private BusinessEntity					businessEntity;

	@Mock
	private KafkaProducerService			kafkaProducerService;
	
	//@InjectMocks
	private TwitterSocialAccountServiceImpl	twitterSocialAccountServiceImpl;
	
	private List<Integer>					totalEnterprisebusinessIds;
	
	private List<BusinessLocationEntity>	businessLocations;
	
	private List<BusinessTwitterPage>		businessTwitterPages;
	
	private static final long				BUSINESS_ID			= 153510231349728L;
	
	private static final int				ID					= 123;
	
	private static final String				TEST_TWITTER_USER	= "test twitter user";
	
	private static final String				PROFILE_ID			= "*********";
	
	/**
	 * //@throws java.lang.Exception
	 */
	//@Before
	public void setUp() throws Exception {
		
		MockitoAnnotations.initMocks(this);
		
		SystemPropertiesCache systemPropertiesCache = new SystemPropertiesCache();
		systemPropertiesCache.addProperty("enable.social.autopost", "0");
		CacheManager.getInstance().setCache(systemPropertiesCache);
		
		prepareDataForTestGetLocationMappingPages();
		
	}
	
	/**
	 * Test method for
	 * {//@link com.birdeye.social.service.TwitterSocialAccountServiceImpl#getLocationMappingPages(java.lang.Long, java.lang.Integer, java.lang.Integer, java.lang.String, java.lang.Integer, java.lang.Integer)}.
	 * 
	 * //@throws Exception
	 */
	//@Test
	public void testGetLocationMappingPages() throws Exception {
		

		LocationPageMapping locationMappingPages = twitterSocialAccountServiceImpl.getLocationMappingPages(BUSINESS_ID, 144,null,null,null,null,null,null);
		
		Assert.assertEquals("testLocationName", locationMappingPages.getLocationList().get(0).getLocationName());
	}
	
	//@Test(expected = BirdeyeSocialException.class)
	public void testGetLocationMappingPagesCheckingEmptyBusinessList() throws Exception {
		
		Mockito.when(businessUserRepository.findBusiness(144, totalEnterprisebusinessIds)).thenReturn(null);
		
		twitterSocialAccountServiceImpl.getLocationMappingPages(BUSINESS_ID, 144,null,null,null,null,null,null);
		
	}
	
	//@Test
	public void testGetLocationMappingPagesSortingNotNull() throws Exception {
		
		businessLocations.get(0).setAlias1("alias1");
		

		LocationPageMapping locationMappingPages = twitterSocialAccountServiceImpl.getLocationMappingPages(BUSINESS_ID, 144,null,null,null,null,null,null);
		
		Assert.assertEquals("alias1", locationMappingPages.getLocationList().get(0).getLocationName());
	}
	
	private void prepareDataForTestGetLocationMappingPages() {
		totalEnterprisebusinessIds = getTotalEnterprisebusinessIds();
		
		businessLocations = new ArrayList<>();
		BusinessLocationEntity businessLocEnt = new BusinessLocationEntity(ID, "testLocationName", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null,
				"address1", "address2", "city", "state", "222222", null, null, null, null);
		businessLocations.add(businessLocEnt);
		
		businessTwitterPages = getBusinessTwitterPagesForGetLocationMappingTest();
		
		Mockito.when(businessUtilService.getBusinessLocationsForEnterprise(Mockito.any(Business.class), Mockito.any(User.class))).thenReturn(totalEnterprisebusinessIds);
		Mockito.when(businessUserRepository.findBusiness(144, totalEnterprisebusinessIds)).thenReturn(totalEnterprisebusinessIds);
		Mockito.when(businessTwitterPageRepo.findAllByBusinessIdIn(Mockito.anyListOf(Integer.class))).thenReturn(businessTwitterPages);
	}
	
	private List<Integer> getTotalEnterprisebusinessIds() {
		List<Integer> totalEnterprisebusinessIds = new ArrayList<>();
		totalEnterprisebusinessIds.add(237432);
		totalEnterprisebusinessIds.add(237392);
		return totalEnterprisebusinessIds;
	}
	
	private List<BusinessTwitterPage> getBusinessTwitterPagesForGetLocationMappingTest() {
		
		List<BusinessTwitterPage> businessTwitterPages = new ArrayList<>();
		BusinessTwitterPage businessTwitterPage1 = new BusinessTwitterPage();
		businessTwitterPage1.setBusinessId(123);
		businessTwitterPage1.setIsValid(0);
		BusinessTwitterPage businessTwitterPage2 = new BusinessTwitterPage();
		businessTwitterPage2.setBusinessId(123);
		businessTwitterPage2.setIsValid(1);
		businessTwitterPage2.setProfileId(3939L);
		businessTwitterPage2.setName(TEST_TWITTER_USER);
		businessTwitterPages.add(businessTwitterPage1);
		businessTwitterPages.add(businessTwitterPage2);
		return businessTwitterPages;
	}
	
	/**
	 * Test method for {//@link com.birdeye.social.service.TwitterSocialAccountServiceImpl#getUnmappedGPlusPagesByEnterpriseId(java.lang.Long)}.
	 */
	//@Test
	public void testGetUnmappedGPlusPagesByEnterpriseId() {
		
		List<BusinessTwitterAccounts> connectedTwitterAccounts = getBusinessTwitterAccountsList();
		
		List<BusinessTwitterPage> mappedTwitterPages = getMappedTwitterPages();
		
		Mockito.when(socialTwitterRepo.findByEnterpriseIdAndIsSelected(BUSINESS_ID, 1)).thenReturn(connectedTwitterAccounts);
		Mockito.when(businessTwitterPageRepo.findByProfileIdIn(Mockito.anyListOf(Long.class))).thenReturn(mappedTwitterPages);
		
		List<SocialPageListInfo> pagesByEnterpriseId = twitterSocialAccountServiceImpl.getUnmappedTwitterPagesByEnterpriseId(BUSINESS_ID);
		
		Assert.assertEquals(TEST_TWITTER_USER, pagesByEnterpriseId.get(0).getPageName());
		
	}
	
	private List<BusinessTwitterPage> getMappedTwitterPages() {
		List<BusinessTwitterPage> mappedTwitterPages = new ArrayList<>();
		BusinessTwitterPage businessTwitterPage = new BusinessTwitterPage();
		businessTwitterPage.setProfileId(BUSINESS_ID);
		mappedTwitterPages.add(businessTwitterPage);
		return mappedTwitterPages;
	}
	
	private List<BusinessTwitterAccounts> getBusinessTwitterAccountsList() {
		List<BusinessTwitterAccounts> connectedTwitterAccounts = new ArrayList<>();
		BusinessTwitterAccounts businessTwitterAccounts = new BusinessTwitterAccounts();
		businessTwitterAccounts.setProfileId(BUSINESS_ID);
		businessTwitterAccounts.setName(TEST_TWITTER_USER);
		businessTwitterAccounts.setIsValid(0);
		connectedTwitterAccounts.add(businessTwitterAccounts);
		return connectedTwitterAccounts;
	}
	
	//@Test
	public void testGetUnmappedGPlusPagesByEnterpriseIdEmptyTwitterAccountsList() {
		
		List<SocialPageListInfo> pagesByEnterpriseId = twitterSocialAccountServiceImpl.getUnmappedTwitterPagesByEnterpriseId(BUSINESS_ID);
		
		Assert.assertEquals(0, pagesByEnterpriseId.size());
		
	}
	
	//@Test
	public void testGetUnmappedGPlusPagesByEnterpriseIdSetMappedToFalse() {
		
		List<BusinessTwitterAccounts> connectedTwitterAccounts = getBusinessTwitterAccountsList();
		connectedTwitterAccounts.get(0).setIsValid(1);
		
		List<BusinessTwitterPage> mappedTwitterPages = getMappedTwitterPages();
		mappedTwitterPages.get(0).setProfileId(153510231349727L);
		
		Mockito.when(socialTwitterRepo.findByEnterpriseIdAndIsSelected(BUSINESS_ID, 1)).thenReturn(connectedTwitterAccounts);
		Mockito.when(businessTwitterPageRepo.findByProfileIdIn(Mockito.anyListOf(Long.class))).thenReturn(mappedTwitterPages);
		
		List<SocialPageListInfo> pagesByEnterpriseId = twitterSocialAccountServiceImpl.getUnmappedTwitterPagesByEnterpriseId(BUSINESS_ID);
		
		Assert.assertEquals(TEST_TWITTER_USER, pagesByEnterpriseId.get(0).getPageName());
		
	}
	
	//@Test
	public void testSaveTwitterLocationMapping() throws Exception {
		
		List<BusinessTwitterAccounts> tempTwitterAccounts = new ArrayList<>();
		BusinessTwitterAccounts businessTwitterAcc = new BusinessTwitterAccounts();
		businessTwitterAcc.setName(TEST_TWITTER_USER);
		businessTwitterAcc.setHandle("//@" + TEST_TWITTER_USER);
		tempTwitterAccounts.add(businessTwitterAcc);
		
		Mockito.when(socialTwitterRepo.findByProfileId(Long.valueOf(PROFILE_ID))).thenReturn(tempTwitterAccounts);
		
		twitterSocialAccountServiceImpl.saveTwitterLocationMapping(237392, Long.valueOf(PROFILE_ID), 12121, Constants.ENTERPRISE, null);
		
	}
	
	//@Test(expected = BirdeyeSocialException.class)
	public void testSaveTwitterLocationMappingAccNotFound() throws Exception {
		
		twitterSocialAccountServiceImpl.saveTwitterLocationMapping(237392, Long.valueOf(PROFILE_ID), 12121,Constants.ENTERPRISE, null);
		
	}
	
	//@Test(expected = BirdeyeSocialException.class)
	public void testSaveTwitterLocationMappingAccAlreadyMapped() throws Exception {
		
		List<BusinessTwitterAccounts> tempTwitterAccounts = new ArrayList<>();
		BusinessTwitterAccounts businessTwitterAcc = new BusinessTwitterAccounts();
		tempTwitterAccounts.add(businessTwitterAcc);
		
		List<BusinessTwitterPage> btpList = getMappedTwitterPages();
		
		Mockito.when(socialTwitterRepo.findByProfileId(Long.valueOf(PROFILE_ID))).thenReturn(tempTwitterAccounts);
		Mockito.when(businessTwitterPageRepo.findByProfileId(Long.valueOf(PROFILE_ID))).thenReturn(btpList);
		
		twitterSocialAccountServiceImpl.saveTwitterLocationMapping(237392, Long.valueOf(PROFILE_ID), 12121,Constants.ENTERPRISE, null);
		
	}
	
	//@Test
	public void removeTwitterLocationAccountMappingTest() {
		
		List<BusinessTwitterPage> btpList = getMappedTwitterPages();
		
		Mockito.when(businessTwitterPageRepo.findByProfileIdIn(Arrays.asList(Long.valueOf(PROFILE_ID))))
				.thenReturn(btpList);
		twitterSocialAccountServiceImpl.removeTwitterLocationAccountMappings(Collections.singletonList(new LocationPageMappingRequest(237392, PROFILE_ID)),Constants.ENTERPRISE,false);
	}
	
	//@Test(expected = BirdeyeSocialException.class)
	public void removeTwitterLocationAccountMappingExceptionTest() {
		twitterSocialAccountServiceImpl.removeTwitterLocationAccountMappings(Collections.singletonList(new LocationPageMappingRequest(237392, PROFILE_ID)),Constants.ENTERPRISE,false);
	}
	
	//@Test(expected = BirdeyeSocialException.class)
	public void removeTwitterLocationAccountMappingException2Test() {
		
		List<BusinessTwitterPage> btpList = getMappedTwitterPages();
		btpList.add(new BusinessTwitterPage());
		
		Mockito.when(businessTwitterPageRepo.findByProfileId(Long.valueOf(PROFILE_ID))).thenReturn(btpList);

		twitterSocialAccountServiceImpl.removeTwitterLocationAccountMappings(Collections.singletonList(new LocationPageMappingRequest(237392, PROFILE_ID)),Constants.ENTERPRISE,false);
	}

// Removing this test case as we don't need to check multiple mappings case for Twitter
//	@Test(expected = BirdeyeSocialException.class)
//	public void removeTwitterLocationAccountMappingException2Test() {
//
//		List<BusinessTwitterPage> btpList = getMappedTwitterPages();
//		btpList.add(new BusinessTwitterPage());
//
//		Mockito.when(businessTwitterPageRepo.findByProfileIdIn(Arrays.asList(Long.valueOf(PROFILE_ID))))
//				.thenReturn(btpList);
//
//		twitterSocialAccountServiceImpl.removeTwitterLocationAccountMappings(Collections.singletonList(new LocationPageMappingRequest(237392, PROFILE_ID)));
//
//	}

	
	//@Test
	public void removeTwitterAccountTest() {
		
		List<String> profileIds = new ArrayList<>();
		profileIds.add(PROFILE_ID);
		List<BusinessTwitterPage> mappedTwitterPages = getMappedTwitterPages();
		
		Mockito.when(socialTwitterRepo.deleteByProfileIdIn(Mockito.anyListOf(Long.class))).thenReturn(1L);
		Mockito.when(businessTwitterPageRepo.findByProfileIdIn(Mockito.anyListOf(Long.class))).thenReturn(mappedTwitterPages);
		
		twitterSocialAccountServiceImpl.removeTwitterAccount(profileIds);
		
	}
	
	//@Test
	public void removeTwitterAccountTestEmptyList() {
		
		List<String> profileIds = new ArrayList<>();
		profileIds.add(PROFILE_ID);
		
		Mockito.when(socialTwitterRepo.deleteByProfileIdIn(Mockito.anyListOf(Long.class))).thenReturn(1L);
		
		twitterSocialAccountServiceImpl.removeTwitterAccount(profileIds);
		
	}
	
	//@Test
	public void getReviewShareOptionsTest() throws Exception {
		
		List<BusinessTwitterPage> existingPages = prepareBusinessTwitterPagesList();
		existingPages.get(0).setBusinessId(ID);
		existingPages.get(0).setEnabled(0);
		
		Map<Integer, BusinessEntity> idToBusinessMap = prepareDataForGetReviewSahreOptions(existingPages);
		
		Mockito.when(socialTwitterRepo.countByEnterpriseIdAndIsSelected(BUSINESS_ID, 1)).thenReturn(2);
		
		twitterSocialAccountServiceImpl.getReviewShareOptions(BUSINESS_ID, idToBusinessMap, new HashMap<>(), 1, 1);
	}
	
	//@Test
	public void getReviewShareOptionsTestCoveringNegetiveConditions() throws Exception {
		
		List<BusinessTwitterPage> existingPages = prepareBusinessTwitterPagesList();
		existingPages.get(0).setBusinessId(ID);
		
		Map<Integer, BusinessEntity> idToBusinessMap = prepareDataForGetReviewSahreOptions(existingPages);
		
		Mockito.when(businessEntity.getAlias1()).thenReturn("pizzaHut");
		Mockito.when(socialTwitterRepo.countByEnterpriseIdAndIsSelected(BUSINESS_ID, 1)).thenReturn(null);
		
		twitterSocialAccountServiceImpl.getReviewShareOptions(BUSINESS_ID, idToBusinessMap, new HashMap<>(), 1, 1);
	}
	
	private Map<Integer, BusinessEntity> prepareDataForGetReviewSahreOptions(List<BusinessTwitterPage> existingPages) {
		
		Map<Integer, BusinessEntity> idToBusinessMap = new HashMap<>();
		idToBusinessMap.put(ID, businessEntity);
		
		List<Integer> busienssIds = new ArrayList<>(idToBusinessMap.keySet());
		
		Mockito.when(businessTwitterPageRepo.findAllByBusinessIdInWithLimit(Mockito.eq(busienssIds), Mockito.any(PageRequest.class))).thenReturn(existingPages);
		Mockito.when(businessTwitterPageRepo.countByBusinessIdIn(Mockito.anyListOf(Integer.class))).thenReturn(busienssIds);
		
		return idToBusinessMap;
		
	}
	
	//@Test
	public void updateReviewSharingOptionsTest() {
		
		prepareBusinessTwitterPagesList();
		
		ReviewOptionDto reviewOptionDto = twitterSocialAccountServiceImpl.updateReviewSharingOptions(ID, PROFILE_ID, true, 0, 0, 12121);
		
		Assert.assertEquals(true, reviewOptionDto.getAutoPostingEnabled());
		
	}
	
	//@Test(expected = BirdeyeSocialException.class)
	public void updateReviewSharingOptionsTestMultipleBusinessForSingleProfileException() {
		
		List<BusinessTwitterPage> businessTwitterPages = getBusinessTwitterPagesForGetLocationMappingTest();
		
		Mockito.when(businessTwitterPageRepo.findByBusinessIdAndProfileId(ID, Long.parseLong(PROFILE_ID))).thenReturn(businessTwitterPages);
		
		twitterSocialAccountServiceImpl.updateReviewSharingOptions(ID, PROFILE_ID, true, 0, 0, 12121);
		
	}
	
	//@Test
	public void updateReviewSharingOptionsTestMinRating() {
		
		prepareBusinessTwitterPagesList();
		
		twitterSocialAccountServiceImpl.updateReviewSharingOptions(ID, PROFILE_ID, null, 3, 0, 12121);
		
	}
	
	//@Test
	public void updateReviewSharingOptionsTestMaxPost() throws Exception {
		
		prepareBusinessTwitterPagesList();
		
		twitterSocialAccountServiceImpl.updateReviewSharingOptions(ID, PROFILE_ID, null, null, 4, 12121);
		
	}
	
	//@Test
	public void updateReviewSharingOptionsTestEmptyProfileIds() {
		
		twitterSocialAccountServiceImpl.updateReviewSharingOptions(ID, PROFILE_ID, null, 3, 0, 12121);
		
	}
	
	//@Test
	public void updateReviewSharingOptionsTestAutoPostingEnabledFalse() {
		
		prepareBusinessTwitterPagesList();
		
		ReviewOptionDto reviewOptionDto = twitterSocialAccountServiceImpl.updateReviewSharingOptions(ID, PROFILE_ID, false, 0, 0, 12121);
		
		Assert.assertEquals(false, reviewOptionDto.getAutoPostingEnabled());
		
	}
	
	//@Test
	public void updateReviewSharingOptionsTestMaxPostNull() throws Exception {
		
		prepareBusinessTwitterPagesList();
		
		twitterSocialAccountServiceImpl.updateReviewSharingOptions(ID, PROFILE_ID, null, null, null, 12121);
		
	}
	
	/**
	 * //@return
	 */
	private List<BusinessTwitterPage> prepareBusinessTwitterPagesList() {
		List<BusinessTwitterPage> businessTwitterPages = new ArrayList<>();
		BusinessTwitterPage businessTwitterPage = new BusinessTwitterPage();
		businessTwitterPage.setEnabled(1);
		businessTwitterPages.add(businessTwitterPage);
		
		Mockito.when(businessTwitterPageRepo.findByBusinessIdAndProfileId(ID, Long.parseLong(PROFILE_ID))).thenReturn(businessTwitterPages);
		
		return businessTwitterPages;
	}
	
}
