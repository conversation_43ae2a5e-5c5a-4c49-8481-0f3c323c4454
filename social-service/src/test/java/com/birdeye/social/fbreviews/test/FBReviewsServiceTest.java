/**
 *
 *
 */
package com.birdeye.social.fbreviews.test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.request.facebook.GetFBReviewCommentRequest;
import com.birdeye.social.external.service.IFacebookReviewService;
import com.birdeye.social.facebook.FacebookServiceImpl;
import com.birdeye.social.model.ActivityResponse;
import com.birdeye.social.model.FbGetReviewsRequest;
import com.birdeye.social.model.FbPostReviewReplyRequest;
import com.birdeye.social.model.PostingPage;
import com.birdeye.social.model.ReviewPostingMetaData;
import com.birdeye.social.model.SocialPostInputMessage;
import com.birdeye.social.service.ReviewSharingServiceImpl;
import com.birdeye.social.service.SocialPostFacebookServiceImpl;
import com.birdeye.social.service.impl.FBReviewServiceImpl;

/**
 * //<AUTHOR>
 *
 */
//@RunWith(MockitoJUnitRunner.class)
public class FBReviewsServiceTest {

	/*//@Mock
	BusinessFacebookPageRepository	fbRepo;

	//@Mock
	IFacebookReviewService			reviewService;

	//@InjectMocks
	FBReviewServiceImpl				fbReviewService;

	//@InjectMocks
	ReviewSharingServiceImpl reviewSharingService;

	//@Mock
	SocialPostFacebookServiceImpl socialPostFacebookService;

	//@InjectMocks
	FacebookServiceImpl facebookService;

	//@Mock
	BusinessRepository businessRepo;

	//@Mock
	ParametersRepository parametersRepo;

	//@Test
	public void TestGetAllFacebookReviews_withAccessToken() throws IOException {
		FbGetReviewsRequest getFbReviewRequest = new FbGetReviewsRequest();
		Mockito.when(fbRepo.findByFacebookPageIdAndIsValid(Mockito.any(),Mockito.any())).thenReturn(withAccessToken());

		fbReviewService.getAllFacebookReviews(getFbReviewRequest);

	}

	//@Test(expected = BirdeyeSocialException.class)
	public void TestGetAllFacebookReviews_nullAccessToken() throws IOException {
		FbGetReviewsRequest getFbReviewRequest = new FbGetReviewsRequest();
		Mockito.when(fbRepo.findByFacebookPageIdAndIsValid(Mockito.any(),Mockito.any())).thenReturn(null);

		fbReviewService.getAllFacebookReviews(getFbReviewRequest);

	}

	//@Test(expected = BirdeyeSocialException.class)
	public void TestGetAllFacebookReviews_multipleAccessToken() throws IOException {
		FbGetReviewsRequest getFbReviewRequest = new FbGetReviewsRequest();
		Mockito.when(fbRepo.findByFacebookPageIdAndIsValid(Mockito.any(),Mockito.any())).thenReturn(multipleAccessToken());

		fbReviewService.getAllFacebookReviews(getFbReviewRequest);

	}

	//@Test(expected = NullPointerException.class)
	public void TestGetAllFacebookReviews_withAccessTokenAndNullRawReviews() throws IOException {
		FbGetReviewsRequest getFbReviewRequest = new FbGetReviewsRequest();
		Mockito.when(fbRepo.findByFacebookPageIdAndIsValid(Mockito.any(),Mockito.any())).thenReturn(withAccessToken());
		Mockito.when(reviewService.getRawReviews(Mockito.any())).thenReturn(null);

		fbReviewService.getAllFacebookReviews(getFbReviewRequest);

	}

	//@Test
	public void TestGetAllFacebookReviews_withAccessTokenAndNullRawReviewsAndErrorKey() throws IOException {

		SystemPropertiesCache systemPropertiesCache = new SystemPropertiesCache();
		systemPropertiesCache.addProperty("social.fb.reviews.pagesize", "200");
		CacheManager.getInstance().setCache(systemPropertiesCache);
		FbGetReviewsRequest getFbReviewRequest = new FbGetReviewsRequest();

		Mockito.when(fbRepo.findByFacebookPageIdAndIsValid(Mockito.any(),Mockito.any())).thenReturn(withAccessToken());
		Mockito.when(reviewService.getRawReviews(Mockito.any())).thenReturn(withError());

		fbReviewService.getAllFacebookReviews(getFbReviewRequest);

	}

	//@Test
	public void TestGetAllFacebookReviews_withAccessTokenAndNullRawReviewsAndDataKey() throws IOException {

		SystemPropertiesCache systemPropertiesCache = new SystemPropertiesCache();
		systemPropertiesCache.addProperty("social.fb.reviews.pagesize", "200");
		CacheManager.getInstance().setCache(systemPropertiesCache);
		FbGetReviewsRequest getFbReviewRequest = new FbGetReviewsRequest(null);

		Mockito.when(fbRepo.findByFacebookPageIdAndIsValid(Mockito.any(),Mockito.any())).thenReturn(withAccessToken());
		Mockito.when(reviewService.getRawReviews(Mockito.any())).thenReturn(withData());

		fbReviewService.getAllFacebookReviews(getFbReviewRequest);

	}

	//@Test
	public void TestGetAllFacebookReviews_withAccessTokenAndNullRawReviewsAndDataKeyCheckParseException() throws IOException {

		SystemPropertiesCache systemPropertiesCache = new SystemPropertiesCache();
		systemPropertiesCache.addProperty("social.fb.reviews.pagesize", "200");
		CacheManager.getInstance().setCache(systemPropertiesCache);
		FbGetReviewsRequest getFbReviewRequest = new FbGetReviewsRequest(null);

		Mockito.when(fbRepo.findByFacebookPageIdAndIsValid(Mockito.any(),Mockito.any())).thenReturn(withAccessToken());
		Mockito.when(reviewService.getRawReviews(Mockito.any())).thenReturn(withData());

		fbReviewService.getAllFacebookReviews(getFbReviewRequest);

	}

	//@Test
	public void TestGetAllFacebookReviews_withAccessTokenAndNullRawReviewsAndDataPagingKey() throws IOException {

		SystemPropertiesCache systemPropertiesCache = new SystemPropertiesCache();
		systemPropertiesCache.addProperty("social.fb.reviews.pagesize", "200");
		CacheManager.getInstance().setCache(systemPropertiesCache);
		FbGetReviewsRequest getFbReviewRequest = new FbGetReviewsRequest();

		Mockito.when(fbRepo.findByFacebookPageIdAndIsValid(Mockito.any(),Mockito.any())).thenReturn(withAccessToken());
		Mockito.when(reviewService.getRawReviews(Mockito.any())).thenReturn(withPaging());

		fbReviewService.getAllFacebookReviews(getFbReviewRequest);

	}

	private List<BusinessFacebookPageNew> multipleAccessToken() {

		List<BusinessFacebookPageNew> pageList = new ArrayList<>();
		BusinessFacebookPageNew page1 = new BusinessFacebookPageNew();
		page1.setAccessToken("token1");
		pageList.add(page1);

		BusinessFacebookPageNew page2 = new BusinessFacebookPageNew();
		page2.setAccessToken("token2");
		pageList.add(page2);

		return pageList;

	}

	private List<BusinessFacebookPageNew> withAccessToken() {

		List<BusinessFacebookPageNew> pageList = new ArrayList<>();
		BusinessFacebookPageNew page1 = new BusinessFacebookPageNew();
		page1.setAccessToken("token1");
		pageList.add(page1);

		return pageList;
	}

	private Map<String, Object> withError() {

		Map<String, Object> errorList = new HashMap<>();

		errorList.put("error", "errorData");

		return errorList;
	}

	private Map<String, Object> withData() {

		Map<String, Object> dataList = new HashMap<>();
		List<Object> objList = new LinkedList<>();
		Map<String, Object> openGraphStoryObj = new HashMap<>();
		Map<String, Object> review = new HashMap<>();
		review.put("start_time", "2018-09-10");
		openGraphStoryObj.put("open_graph_story", review);
		objList.add(openGraphStoryObj);
		dataList.put("data", objList);

		return dataList;
	}

	private Map<String, Object> withPaging() {

		Map<String, Object> pagingList = new HashMap<>();
		Map<String, Object> cursor = new HashMap<>();
		List<Object> objList = new LinkedList<>();
		objList.add(1);
		objList.add("string");
		pagingList.put("data", objList);

		objList.add(1);
		objList.add("string");
		objList.add(cursor);
		Map<String, Object> pagingMap = new HashMap<>();
		Map<String, Object> cursorMap = new HashMap<>();
		cursorMap.put("after", "testAfter");
		pagingMap.put("cursors", cursorMap);
		pagingList.put("paging", pagingMap);
		return pagingList;
	}

	//@Test(expected = BirdeyeSocialException.class)
	public void TestPostFBReviewReplyNullAccessToken() throws IOException {
		FbPostReviewReplyRequest postReviewReplyRequest = new FbPostReviewReplyRequest();
		Mockito.when(fbRepo.findByFacebookPageIdAndIsValid(Mockito.any(),Mockito.any())).thenReturn(null);

		fbReviewService.postFBReviewReply(postReviewReplyRequest);
	}

	//@Test
	public void TestPostFBReviewReplywithAccessToken() throws IOException {
		FbPostReviewReplyRequest postReviewReplyRequest = new FbPostReviewReplyRequest();
		Mockito.when(fbRepo.findByFacebookPageIdAndIsValid(Mockito.any(),Mockito.any())).thenReturn(withAccessToken());
		Mockito.when(reviewService.getRawReviews(Mockito.any())).thenReturn(null);

		fbReviewService.postFBReviewReply(postReviewReplyRequest);
	}

	//@Test(expected = BirdeyeSocialException.class)
	public void TestPostFBReviewReplyMultipleAccessToken() throws IOException {
		FbPostReviewReplyRequest postReviewReplyRequest = new FbPostReviewReplyRequest();
		Mockito.when(fbRepo.findByFacebookPageIdAndIsValid(Mockito.any(),Mockito.any())).thenReturn(multipleAccessToken());

		fbReviewService.postFBReviewReply(postReviewReplyRequest);
	}

	//@Test
	public void TestGetFBReviewById() throws IOException {
		Mockito.when(fbRepo.findByFacebookPageIdAndIsValid(Mockito.any(),Mockito.any())).thenReturn(withAccessToken());
		Mockito.when(reviewService.getReviewById(Mockito.any(GetFBReviewCommentRequest.class))).thenReturn(null);

		fbReviewService.getFBReviewById("pageId","reviewId");

	}

	//@Test
	public void TestFBReviewShareInvalidPage() {
		SocialPostInputMessage socialPost = createSocialPostInputMessage(getReviewMetaData());
		Mockito.when(businessRepo.findById(1)).thenReturn(Mockito.any());
		Mockito.when(fbRepo.findByBusinessId(1)).thenReturn(getBusinessFacebookPage(0,0,0));
		List<ActivityResponse> activityResponses = reviewSharingService.shareReview(socialPost);
		Assert.assertEquals(ActivityResponse.ErrorResponseCode.INVALID_PAGE.getCode(), activityResponses.get(0).getErrorCode());
	}

//	//@Test
//	public void TestFBReviewShareSharingDisabled() {
//		SocialPostInputMessage socialPost = createSocialPostInputMessage(getReviewMetaDataForSharingDisabled());
//		Mockito.when(businessRepo.findById(1)).thenReturn(Mockito.any());
//		Mockito.when(fbRepo.findByBusinessId(1)).thenReturn(withAccessToken());
//		List<ActivityResponse> activityResponses = reviewSharingService.shareReview(socialPost);
//		Assert.assertEquals(ActivityResponse.ErrorResponseCode.SHARING_DISABLED.getCode(), activityResponses.get(0).getErrorCode());
//	}

	//@Test
	public void TestFBReviewGenericException() {
		SocialPostInputMessage socialPost = createSocialPostInputMessage(getReviewMetaDataWithRating(5f));

		Mockito.when(businessRepo.findById(Mockito.anyInt())).thenReturn(new Business());
		List<BusinessFacebookPageNew> businessFacebookPageNews = getBusinessFacebookPage(1, 1, 1);
		Mockito.when(fbRepo.findByBusinessId(Mockito.anyInt())).thenReturn(businessFacebookPageNews);

		try {
//			Mockito.when(socialPostFacebookService.shareReview(Mockito.anyString(), Mockito.anyString(), Mockito.any(),
//					Mockito.any(BusinessFacebookPageNew.class), Mockito.any()))
//					.thenThrow(new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK));
			List<ActivityResponse> activityResponses = reviewSharingService.shareReview(socialPost);
			Assert.assertEquals(ActivityResponse.ErrorResponseCode.EXCEPTION.getCode(),
					activityResponses.get(0).getErrorCode());
		} catch (Exception e) {
			Assert.fail();
		}
	}

	//@Test
	public void TestFBReviewOAuthException() {
		SocialPostInputMessage socialPost = createSocialPostInputMessage(getReviewMetaDataWithRating(5f));

		Mockito.when(businessRepo.findById(Mockito.anyInt())).thenReturn(new Business());
		List<BusinessFacebookPageNew> businessFacebookPageNews = getBusinessFacebookPage(1, 1, 1);
		Mockito.when(fbRepo.findByBusinessId(Mockito.anyInt())).thenReturn(businessFacebookPageNews);
		
		try {
//			Mockito.when(socialPostFacebookService.shareReview(Mockito.anyString(), Mockito.anyString(), Mockito.any(),
//					Mockito.any(BusinessFacebookPageNew.class), Mockito.any()))
//					.thenThrow(new BirdeyeSocialException(ErrorCodes.FB_OAUTH_EXCEPTION));
			List<ActivityResponse> activityResponses = reviewSharingService.shareReview(socialPost);
			Assert.assertEquals(ActivityResponse.ErrorResponseCode.FB_OAUTH_EXCEPTION.getCode(),
					activityResponses.get(0).getErrorCode());
		} catch (Exception e) {
			Assert.fail();
		}
	}
	
	private SocialPostInputMessage createSocialPostInputMessage(ReviewPostingMetaData reviewMetaData) {
		SocialPostInputMessage socialPost = new SocialPostInputMessage();
		Map<String, List<PostingPage>> postingMap =new HashMap<>();
		postingMap.put("facebook", new ArrayList<>());
		socialPost.setPostingSites(postingMap);
		socialPost.setReviewMetaData(reviewMetaData);
		List<String> links = new LinkedList<>(Arrays.asList("http://testFbLink.com"));
		socialPost.setPostText("TestPostText");
		socialPost.setLinks(links);
		return socialPost;
	}
	
	private ReviewPostingMetaData getReviewMetaData() {
		ReviewPostingMetaData reviewPostingMetaData = new ReviewPostingMetaData();
		return reviewPostingMetaData;
	}
	
	private ReviewPostingMetaData getReviewMetaDataForSharingDisabled() {
		ReviewPostingMetaData reviewPostingMetaData = new ReviewPostingMetaData();
		reviewPostingMetaData.setAutoShared(true);
		return reviewPostingMetaData;
	}
	
	private ReviewPostingMetaData getReviewMetaDataWithRating(float rating) {
		ReviewPostingMetaData reviewPostingMetaData = new ReviewPostingMetaData();
		reviewPostingMetaData.setAutoShared(true);
		reviewPostingMetaData.setReviewRating(rating);
		return reviewPostingMetaData;
	}
	
	private List<BusinessFacebookPageNew> getBusinessFacebookPage(int isValid, int enabled, int dayPostCount) {
		
		List<BusinessFacebookPageNew> pageList = new ArrayList<>();
		BusinessFacebookPageNew page1 = new BusinessFacebookPageNew();
		page1.setAccessToken("token1");
		page1.setIsValid(isValid);
		page1.setEnabled(enabled);
		page1.setDayPostCount(dayPostCount);
		page1.setFacebookPageId("AnyFbId");
		page1.setBusinessId(1);
		page1.setId(1);
		pageList.add(page1);
		
		return pageList;
	}*/
}