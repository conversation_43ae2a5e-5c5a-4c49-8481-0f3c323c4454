/**
 *
 *
 */
package com.birdeye.social.twitter.test;

import java.util.ArrayList;
import java.util.List;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.sro.TwitterConnectAccountRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.junit4.SpringRunner;

import com.birdeye.social.constant.Status;
import com.birdeye.social.dao.BusinessGetPageReqRepo;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.dao.SocialPostsAssetsRepository;
import com.birdeye.social.dao.SocialTwitterAccountRepository;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.entities.BusinessTwitterAccounts;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.platform.dao.BusinessTwitterPageRepository;
import com.birdeye.social.platform.dao.SessionTokenRepository;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.SocialPostTwitterService;
import com.birdeye.social.service.SocialPostTwitterServiceImpl;
import com.birdeye.social.service.TwitterSocialAccountService;
import com.birdeye.social.twitter.TwitterService;

/**
 * <AUTHOR>
 *
 */
@RunWith(SpringRunner.class)
public class TestTwitterConnectPage {
	
	@MockBean
	SocialPostInfoRepository				socialPostInfoRepository;
	
	@MockBean
	SocialPostsAssetsRepository				socialPostsAssetsRepository;
	
	@MockBean
	BusinessTwitterPageRepository			twitterRepo;
	
	@MockBean
	CommonService							commonService;
	
	@MockBean
	SessionTokenRepository					sessionTokenRepository;
	
	@MockBean
	BusinessGetPageReqRepo					businessGetPageReqRepo;
	
	@MockBean
	IRedisLockService						redisService;
	
	@MockBean
	SocialTwitterAccountRepository	socialTwitterRepo;
	
	@MockBean
	TwitterService							twitterService;
	
	@MockBean
	TwitterSocialAccountService twitterSocialAccountService;
	
	@Autowired
	SocialPostTwitterService socialPostTwitterService;
	
	@TestConfiguration
	static class TestSocialPostTwitterServiceConfiguration {
		@Bean
		public SocialPostTwitterService socialPostTwitterService() {
			return new SocialPostTwitterServiceImpl();
		}
	}
	
	@Test
	public void testConnectTwitterPagesV1WithFetchedRequestWithTwitterAccounts() {
		Long businessId = 1L;
		List<String> accId = new ArrayList<>();
		accId.add("123");
		accId.add("1234");
		
		Business business = new Business();
		business.setAccountType("Direct");
		business.setType("Product");
		
		Mockito.when(businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannel(Mockito.any(), Mockito.eq(Status.FETCHED.getName()), Mockito.any())).thenReturn(fetchedRequest());
		

		TwitterConnectAccountRequest twitterConnectAccountRequest= createAccountRequest(accId,businessId);
		socialPostTwitterService.connectTwitterPagesV1(twitterConnectAccountRequest);
	}

	private TwitterConnectAccountRequest createAccountRequest(List<String> accId, Long businessId) {
		TwitterConnectAccountRequest twitterConnectAccountRequest= new TwitterConnectAccountRequest();
		twitterConnectAccountRequest.setId(accId);
		twitterConnectAccountRequest.setBusinessId(businessId);
		return twitterConnectAccountRequest;
	}

	@Test
	public void testConnectTwitterPagesV1WithFetchedRequestWithTwitterAccountsWithBusinessTypeAsBusiness() {
		Long businessId = 1L;
		List<String> accId = new ArrayList<>();
		accId.add("123");
		accId.add("1234");
		
		Business business = new Business();
		business.setAccountType("Direct");
		business.setType("Business");
		
		Mockito.when(businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannel(Mockito.any(), Mockito.eq(Status.FETCHED.getName()), Mockito.any())).thenReturn(fetchedRequest());
		

		TwitterConnectAccountRequest twitterConnectAccountRequest= createAccountRequest(accId,businessId);
		socialPostTwitterService.connectTwitterPagesV1(twitterConnectAccountRequest);
	}
	
	@Test
	public void testConnectTwitterPagesV1WithFetchedRequestWithTwitterAccountsWithAccountTypeNotDirect() {
		Long businessId = 1L;
		List<String> accId = new ArrayList<>();
		accId.add("123");
		accId.add("1234");
		
		Business business = new Business();
		business.setAccountType(null);
		business.setType("Business");
		
		Mockito.when(businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannel(Mockito.any(), Mockito.eq(Status.FETCHED.getName()), Mockito.any())).thenReturn(fetchedRequest());

		TwitterConnectAccountRequest twitterConnectAccountRequest= createAccountRequest(accId,businessId);

		socialPostTwitterService.connectTwitterPagesV1(twitterConnectAccountRequest);
	}
	
	@Test
	public void testConnectTwitterPagesV1WithFetchedRequestWithTwitterAccountsWithTypeNotBusinessNotProduct() {
		Long businessId = 1L;
		List<String> accId = new ArrayList<>();
		accId.add("123");
		accId.add("1234");
		
		Business business = new Business();
		business.setAccountType("Direct");
		business.setType(null);
		
		Mockito.when(businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannel(Mockito.any(), Mockito.eq(Status.FETCHED.getName()), Mockito.any())).thenReturn(fetchedRequest());
		
		Mockito.when(socialTwitterRepo.findByProfileIdIn(Mockito.any())).thenReturn(getBusinessTwitterAccounts());
		
		TwitterConnectAccountRequest twitterConnectAccountRequest= createAccountRequest(accId,businessId);
		socialPostTwitterService.connectTwitterPagesV1(twitterConnectAccountRequest);
	}
	
	@Test(expected=BirdeyeSocialException.class)
	public void testConnectTwitterPagesV1NullFetchedRequest() {
		
		Long businessId = 1L;
		List<String> accId = new ArrayList<>();
		accId.add("123");
		accId.add("1234");
		
		Mockito.when(businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannel(Mockito.any(), Mockito.eq(Status.FETCHED.getName()), Mockito.any())).thenReturn(null);
		TwitterConnectAccountRequest twitterConnectAccountRequest= createAccountRequest(accId,businessId);

		socialPostTwitterService.connectTwitterPagesV1(twitterConnectAccountRequest);
	}
	
	@Test(expected=BirdeyeSocialException.class)
	public void testConnectTwitterPagesV1MultipleFetchedRequest() {
		
		Long businessId = 1L;
		List<String> accId = new ArrayList<>();
		accId.add("123");
		accId.add("1234");
		
		Mockito.when(businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannel(Mockito.any(), Mockito.eq(Status.FETCHED.getName()), Mockito.any())).thenReturn(multiFetchedRequest());
		TwitterConnectAccountRequest twitterConnectAccountRequest= createAccountRequest(accId,businessId);
		socialPostTwitterService.connectTwitterPagesV1(twitterConnectAccountRequest);
	}
	
	
	
	private List<BusinessGetPageRequest> fetchedRequest() {
		List<BusinessGetPageRequest> requests = new ArrayList<>();
		BusinessGetPageRequest req = new BusinessGetPageRequest();
		req.setBirdeyeUserId(123);
		req.setChannel("twitter");
		req.setEnterpriseId(12345L);
		req.setId(420);
		req.setPageCount(1);
		req.setStatus("fetched");
		req.setSocialUserId("1");
		req.setTotalPages(2);
		
		requests.add(req);
		return requests;
	}
	
	private List<BusinessGetPageRequest> multiFetchedRequest() {
		List<BusinessGetPageRequest> requests = new ArrayList<>();
		
		BusinessGetPageRequest req = new BusinessGetPageRequest();
		req.setBirdeyeUserId(123);
		req.setChannel("twitter");
		req.setEnterpriseId(12345L);
		req.setId(420);
		req.setPageCount(1);
		req.setStatus("fetched");
		req.setSocialUserId("1");
		req.setTotalPages(2);
		
		requests.add(req);
		
		BusinessGetPageRequest req2 = new BusinessGetPageRequest();
		req2.setBirdeyeUserId(1234);
		req2.setChannel("twitter");
		req2.setEnterpriseId(123456L);
		req2.setId(421);
		req2.setPageCount(2);
		req2.setStatus("fetched");
		req2.setSocialUserId("2");
		req2.setTotalPages(3);
		
		requests.add(req2);
		return requests;
	}
	
	private List<BusinessTwitterAccounts> getBusinessTwitterAccounts() {
		List<BusinessTwitterAccounts> connectedAccounts = new ArrayList<>();
		BusinessTwitterAccounts accounts = new BusinessTwitterAccounts();
		accounts.setAccessSecret("access secret");
		accounts.setAccessToken("access token");
		accounts.setEnterpriseId(123456789L);
		accounts.setHandle("@Handle");
		accounts.setName("Twitter Test Name");
		accounts.setProfileId(1234L);
		accounts.setRequestId("12");
		
		connectedAccounts.add(accounts);
		return connectedAccounts;
	}
}
