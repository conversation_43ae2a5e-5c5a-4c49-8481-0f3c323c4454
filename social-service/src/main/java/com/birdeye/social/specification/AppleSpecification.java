package com.birdeye.social.specification;


import com.birdeye.social.constant.PageSortDirection;
import com.birdeye.social.entities.BusinessAppleLocation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class AppleSpecification {
    public Specification<BusinessAppleLocation> hasResellerId(Long resellerId) {
        if(Objects.isNull(resellerId)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.equal(root.get("resellerId"),  resellerId);
        });
    }

    public Specification<BusinessAppleLocation> hasEmail(String email) {
        if(Objects.isNull(email)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.equal(root.get("userEmailId"),  "%" + email + "%");
        });
    }

    public Specification<BusinessAppleLocation> hasPageName(String pageName) {

        if(Objects.isNull(pageName)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.like(root.get("locationName"),  "%" + pageName + "%");
        });
    }

    public Specification<BusinessAppleLocation> hasAddress(String address) {
        if(Objects.isNull(address)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.like(root.get("singleLineAddress"),  "%" + address + "%");
        });
    }

    public Specification<BusinessAppleLocation> isSelected(Integer i) {
        if(Objects.isNull(i)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.equal(root.get("isSelected"), i);
        });
    }

    public Specification<BusinessAppleLocation> hasRequestId(String requestId) {
        if(Objects.isNull(requestId)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.equal(root.get("requestId"), requestId);
        });
    }

    public Specification<BusinessAppleLocation> inBusinessIds(List<Integer> businessIds) {
        if(CollectionUtils.isEmpty(businessIds)){
            return null;
        }
        return ((root, query, cb) -> {
            return root.get("businessId").in(businessIds);
        });
    }

    public Specification<BusinessAppleLocation> inValidityTypes(List<Integer> validityTypes) {
        if(CollectionUtils.isEmpty(validityTypes)){
            return null;
        }
        return ((root, query, cb) -> {
            return root.get("isValid").in(validityTypes);
        });
    }

    public Specification<BusinessAppleLocation> inCreatedByIds(List<Integer> createdByIds) {
        if(CollectionUtils.isEmpty(createdByIds)){
            return null;
        }
        return ((root, query, cb) -> {
            return root.get("createdBy").in(createdByIds);
        });
    }

    public Specification<BusinessAppleLocation> hasBusinessIdNullOrNotNull(boolean isNull) {
        return ((root, query, cb) -> {
            return isNull
                    ?cb.isNull(root.get("businessId"))
                    :cb.isNotNull(root.get("businessId"));
        });
    }

    public  Specification<BusinessAppleLocation> sortBusinessIdNullsFirst() {
        return (root, query, criteriaBuilder) -> {
            // Sort: NULL businessId first, then non-NULL
            query.orderBy(
                    criteriaBuilder.asc(
                            criteriaBuilder.selectCase()
                                    .when(criteriaBuilder.isNull(root.get("businessId")), 0)
                                    .otherwise(1)
                    ),
                    criteriaBuilder.asc(root.get("locationName"))
            );
            return criteriaBuilder.conjunction();
        };
    }

    public  Specification<BusinessAppleLocation> sortValidTypesInGroup(PageSortDirection sortDirection) {
        return (root, query, criteriaBuilder) -> {
            // Sort: NULL businessId first, then non-NULL
            query.orderBy(
                    PageSortDirection.ASC.equals(sortDirection)?criteriaBuilder.asc(
                            criteriaBuilder.selectCase()
                                    .when(criteriaBuilder.equal(root.get("isValid"), 1), 0)
                                    .when(criteriaBuilder.equal(root.get("isValid"), 1),1)
                                    .otherwise(1)
                    )
                            : criteriaBuilder.desc(
                            criteriaBuilder.selectCase()
                                    .when(criteriaBuilder.equal(root.get("isValid"), 1), 0)
                                    .when(criteriaBuilder.equal(root.get("isValid"), 1),1)
                                    .otherwise(1)
                    ),
                    criteriaBuilder.asc(root.get("locationName"))
            );
            return criteriaBuilder.conjunction();
        };
    }

    public Specification<BusinessAppleLocation> hasEnterpriseId(Long enterpriseId) {
        if(Objects.isNull(enterpriseId)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.equal(root.get("enterpriseId"),  enterpriseId);
        });
    }
}
