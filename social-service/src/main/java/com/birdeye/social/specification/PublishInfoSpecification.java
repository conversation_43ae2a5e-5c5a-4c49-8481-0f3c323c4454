package com.birdeye.social.specification;

import com.birdeye.social.entities.SocialPostPublishInfo;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class PublishInfoSpecification {

    public Specification<SocialPostPublishInfo> postId(Integer id) {
        if(Objects.isNull(id)) {
            return null;
        }
        return (((root, query, cb) -> cb.equal(root.get("id"), id)));
    }

    public Specification<SocialPostPublishInfo> sourceId(Integer sourceId) {
        if(Objects.isNull(sourceId)) {
            return null;
        }
        return (((root, query, cb) -> cb.equal(root.get("sourceId"), sourceId)));
    }

    public Specification<SocialPostPublishInfo> businessId(Integer businessId) {
        if(Objects.isNull(businessId)) {
            return null;
        }
        return (((root, query, cb) -> cb.equal(root.get("businessId"), businessId)));
    }

    public Specification<SocialPostPublishInfo> enterpriseId(Integer enterpriseId) {
        if(Objects.isNull(enterpriseId)) {
            return null;
        }
        return (((root, query, cb) -> cb.equal(root.get("enterpriseId"), enterpriseId)));
    }

    public Specification<SocialPostPublishInfo> failureCode(Integer failureCode) {
        if(Objects.isNull(failureCode)) {
            return null;
        }
        return (((root, query, cb) -> cb.equal(root.get("failureCode"), failureCode)));
    }

    public Specification<SocialPostPublishInfo> isPublished(Integer isPublished) {
        if(Objects.isNull(isPublished)) {
            return null;
        }
        return (((root, query, cb) -> cb.equal(root.get("isPublished"), isPublished)));
    }
    public Specification<SocialPostPublishInfo> socialPostId(Integer socialPostId) {
        if(Objects.isNull(socialPostId)) {
            return null;
        }
        return (((root, query, cb) -> cb.equal(root.get("socialPostId"), socialPostId)));
    }

}


