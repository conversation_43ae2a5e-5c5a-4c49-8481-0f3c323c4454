/**
 * 
 */
package com.birdeye.social.lock;

/**
 * <AUTHOR>
 *
 */
public enum LockStatus {
	NO_LOCK(0), PRESENT(1), ACQUIRED(2);
	
	private int status;
	
	private LockStatus(int status) {
		this.status = status;
	}
	
	public int getStatus() {
		return this.status;
	}
	
	public static LockStatus getStatus(int status) {
		for (LockStatus ls : LockStatus.values()) {
			if (ls.getStatus() == status) {
				return ls;
			}
		}
		return null;
	}
}
