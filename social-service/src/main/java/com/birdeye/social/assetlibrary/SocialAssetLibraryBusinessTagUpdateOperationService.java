package com.birdeye.social.assetlibrary;

import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryOperation;
import com.birdeye.social.entities.assetlibrary.SocialAssetLibraryBusinessTag;
import com.birdeye.social.sro.assetlibrary.SocialAssetLibraryBusinessTagOperationResponse;
import com.birdeye.social.sro.assetlibrary.SocialAssetLibraryTagBasicDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 23/10/23
 */
@Service
@Slf4j
public class SocialAssetLibraryBusinessTagUpdateOperationService extends AbstractSocialAssetLibraryBusinessTagOperationService {

    @Autowired
    private SocialAssetLibraryDBService socialAssetLibraryDBService;

    @Override
    public SocialAssetLibraryBusinessTagOperationResponse performOperation(Integer accountId, Long accountNum, Long userId, Set<SocialAssetLibraryTagBasicDetail> tagBasicDetails) {
        // This is fetched from #allBusinessTags internally via cache hence no additional DB hit
        Map<Long, SocialAssetLibraryBusinessTag> tagIdToTagMap = socialAssetLibraryDBService.getTagIdToBusinessTagMap(accountId);
        Map<String, Long> existingTagToIdMap = socialAssetLibraryDBService.getAllLowerCaseBusinessTagNameToTagIdMap(accountId);

        // If there already exists a tag from the same name then do NOT update it on a given tag ID
        // Ignoring the case by lower casing the existing and the new tag while comparing
        SocialAssetLibraryBusinessTagOperationResponse businessTagOperationResponse =
                new SocialAssetLibraryBusinessTagOperationResponse(SocialAssetLibraryOperation.UPDATE);
        Set<SocialAssetLibraryTagBasicDetail> tags = new HashSet<>();
        List<SocialAssetLibraryBusinessTag> editedBusinessTags = new LinkedList<>();
        for (SocialAssetLibraryTagBasicDetail tagBasicDetail : tagBasicDetails) {
            Long tagId = tagBasicDetail.getId();
            String tagName = StringUtils.strip(tagBasicDetail.getName());
            String lowerCaseTagName = StringUtils.lowerCase(tagName);
            Long existingTagId = existingTagToIdMap.get(lowerCaseTagName);
            // If the tagName is already taken by an existing tag, then it ain't
            // eligible to be update
            if (Objects.nonNull(existingTagId)) {
                log.info("[SocialAssetLibraryBusinessTagUpdateOperationService] performOperation cannot be performed for a tagId:{} tagName :{}"
                         + " is already taken for accountId:{}", tagId, tagName, accountId);
                tags.add(new SocialAssetLibraryTagBasicDetail(existingTagId, tagName));
            } else {
                SocialAssetLibraryBusinessTag existingTag = tagIdToTagMap.get(tagId);
                if (Objects.isNull(existingTag)) {
                    log.info("[SocialAssetLibraryBusinessTagUpdateOperationService] performOperation tagId:{} DNE for accountId:{}", tagId, accountId);
                    continue;
                } else {
                    existingTag.setName(tagName);
                    existingTag.setUpdatedBy(userId);
                    editedBusinessTags.add(existingTag);

                    tags.add(new SocialAssetLibraryTagBasicDetail(tagId, tagName));
                    // This is done so that within an update request of we have the same tag name
                    existingTagToIdMap.put(lowerCaseTagName, tagId);
                }
            }
        }

        boolean isOperationExecuted = false;
        if (CollectionUtils.isNotEmpty(editedBusinessTags)) {
            isOperationExecuted = true;
            editedBusinessTags = socialAssetLibraryDBService.saveAllAndFlushBusinessTags(editedBusinessTags);
            Set<Long> editBusinessTagIds = editedBusinessTags.stream().map(SocialAssetLibraryBusinessTag::getId).collect(Collectors.toSet());
            List<Long> tagMappedAssetIds = socialAssetLibraryDBService.findDistinctAssetIdIdMappedToTagIds(editBusinessTagIds);

            // This is done so that all the thus updated tag(s) can be updated on ES as well
            if (CollectionUtils.isNotEmpty(tagMappedAssetIds)) {
                publishAssetActionEvent(accountId, accountNum, userId, tagMappedAssetIds);
            }

            // Evict the account level created cache for all tags for this account
            // Only if new tags are actually edited
            evictAllTagsByAccountCache(accountId);
        }

        businessTagOperationResponse.setTags(tags);
        businessTagOperationResponse.setIsOperationExecuted(isOperationExecuted);

        return businessTagOperationResponse;
    }
}
