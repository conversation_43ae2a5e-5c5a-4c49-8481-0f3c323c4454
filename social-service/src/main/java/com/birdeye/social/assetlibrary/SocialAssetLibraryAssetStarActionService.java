package com.birdeye.social.assetlibrary;

import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryAction;
import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryOperation;
import com.birdeye.social.entities.assetlibrary.SocialAssetLibrary;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.sro.assetlibrary.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> on 12/10/23
 */
@Service
@Slf4j
public class SocialAssetLibraryAssetStarActionService extends AbstractSocialAssetLibraryAssetActionService {

    @Autowired
    private SocialAssetLibraryDBService assetLibraryDBService;

    @Override
    public SocialAssetLibraryAssetActionResponse performAction(Long assetId, SocialAssetLibraryAssetActionRequest assetActionRequest, SocialAssetLibraryOperation operation, SocialAssetLibraryAction action) {
        if (Objects.isNull(assetId)) {
            log.error("[SocialAssetLibraryAssetStarActionService] cannot proceed for a NULL assetId");
            throw new BirdeyeSocialException(ErrorCodes.ASSET_LIB_INVALID_INPUT, "Non existent asset cannot be starred and/or un-starred");
        }
        Optional<SocialAssetLibrary> assetO = assetLibraryDBService.findActiveAssetByAssetId(assetId);
        if (!assetO.isPresent()) {
            throw new BirdeyeSocialException(ErrorCodes.ASSET_LIB_ASSET_DNE, "This asset does not exists.");
        }
        SocialAssetLibrary asset = assetO.get();
        SocialAssetLibraryMetadata existingMetadata = asset.getMetadata();
        Boolean newStarredValue = assetActionRequest.getStarred();

        // For optimisation of a DB query in case no update is done
        boolean isActionExecuted = false;

        SocialAssetLibraryMetadata.SocialAssetLibraryOrganicMetadata organicMetadata;
        Long userId = assetActionRequest.getUserId();
        SocialAssetLibraryAuditPersistenceDetail formerState = new SocialAssetLibraryAuditPersistenceDetail(asset.getAccountId(), userId);
        SocialAssetLibraryAuditPersistenceDetail currentState = new SocialAssetLibraryAuditPersistenceDetail(assetActionRequest.getAccountId(), userId);

        if (Objects.isNull(existingMetadata) || Objects.isNull(existingMetadata.getOrganicMetadata())) {
            isActionExecuted = true;

            SocialAssetLibraryMetadata metadata = new SocialAssetLibraryMetadata();
            organicMetadata = new SocialAssetLibraryMetadata.SocialAssetLibraryOrganicMetadata();
            organicMetadata.setStarred(BooleanUtils.isTrue(assetActionRequest.getStarred()));

            metadata.setOrganicMetadata(organicMetadata);
            asset.setMetadata(metadata);

            currentState.setStarred(newStarredValue);
        } else {
            organicMetadata = existingMetadata.getOrganicMetadata();
            Boolean formerStarredValue = organicMetadata.getStarred();
            if (!Objects.equals(formerStarredValue, newStarredValue)) {
                isActionExecuted = true;
                organicMetadata.setStarred(newStarredValue);

                formerState.setStarred(formerStarredValue);
                currentState.setStarred(newStarredValue);
            }
        }
        if (isActionExecuted) {
            asset.setUpdatedBy(userId);

            // While publishing an event we need this
            assetActionRequest.setType(asset.getType());

            assetLibraryDBService.saveAndFlush(asset);

            publishAssetActionEvent(assetId, assetActionRequest, operation, action);
            createAudit(assetId, formerState, currentState, operation, action);
        }
        return new SocialAssetLibraryAssetActionResponse(assetId, isActionExecuted);
    }

    @Override
    public void performBulkAction(SocialAssetLibraryAssetBulkActionRequest assetBulkActionRequest,
                                  SocialAssetLibraryOperation operation, SocialAssetLibraryAction action) {
        if (Objects.isNull(assetBulkActionRequest.getStarred())) {
            log.info("[SocialAssetLibraryAssetStarActionService] performBulkAction cannot be performed for Missing mandatory field value for accountId:{}",
                     assetBulkActionRequest.getAccountId());
            throw new BirdeyeSocialException(ErrorCodes.ASSET_LIB_INVALID_INPUT, "Missing mandatory field value for bulk " + action + " action");
        }
        super.publishAssetBulkActionAtomicEvents(assetBulkActionRequest, operation, action);
    }
}