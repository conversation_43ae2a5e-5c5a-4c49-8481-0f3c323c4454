package com.birdeye.social.assetlibrary;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR> on 26/10/23
 */
@Service
@Slf4j
public class SocialAssetLibraryAssetBizTagMappingDeleteActionService extends SocialAssetLibraryAssetBizTagMappingActionService {

    @Autowired
    private SocialAssetLibraryDBService socialAssetLibraryDBService;

    public void performAction(Long assetId, Set<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            return;
        }
        socialAssetLibraryDBService.deleteAssetBusinessTagMappingByTagIds(tagIds);
        log.info("[SocialAssetLibraryAssetBizTagMappingDeleteActionService] performAction executed successfully for assetId:{} and tagIds:{}", assetId, tagIds);
    }
}
