package com.birdeye.social.assetlibrary;

import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryAssetStatus;
import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryOperation;
import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryAction;
import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryConstants;
import com.birdeye.social.dto.assetlibrary.SocialAssetLibraryLiteInfo;
import com.birdeye.social.entities.assetlibrary.SocialAssetLibrary;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.service.CacheService;
import com.birdeye.social.sro.assetlibrary.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> on 18/10/23
 */
@Service
@Slf4j
public class SocialAssetLibraryAssetDeleteActionService extends AbstractSocialAssetLibraryAssetActionService {

    @Autowired
    private SocialAssetLibraryDBService socialAssetLibraryDBService;

    @Autowired
    private CacheService cacheService;

    @Override
    public SocialAssetLibraryAssetActionResponse performAction(Long assetId, SocialAssetLibraryAssetActionRequest assetActionRequest,
                                                               SocialAssetLibraryOperation operation, SocialAssetLibraryAction action) {
        if (Objects.isNull(assetId)) {
            log.error("[SocialAssetLibraryAssetActionResponse] cannot proceed for a NULL assetId");
            throw new BirdeyeSocialException(ErrorCodes.ASSET_LIB_INVALID_INPUT, "Non existent asset cannot be deleted");
        }
        Optional<SocialAssetLibrary> assetO = socialAssetLibraryDBService.findActiveAssetByAssetId(assetId);
        if (!assetO.isPresent()) {
            throw new BirdeyeSocialException(ErrorCodes.ASSET_LIB_ASSET_DNE, "This asset does not exists.");
        }
        SocialAssetLibrary asset = assetO.get();
        Long userId = assetActionRequest.getUserId();

        // While publishing an event we need this
        assetActionRequest.setType(asset.getType());

        asset.setStatus(SocialAssetLibraryAssetStatus.DELETED);
        asset.setUpdatedBy(userId);
        socialAssetLibraryDBService.saveAndFlush(asset);

        publishAssetActionEvent(assetId, assetActionRequest, operation, action);
        createAudit(assetId, convert(userId, asset), null, operation, action);

        // Evict the cache for cleaning up any orphan caching
        evictAssetToParentFolderCache(assetId);

        // This is done so that we ensure that any new incoming asset
        // creation/renaming/moving takes place on a unique asset name itself.
        evictAssetNameCache(assetActionRequest.getAccountId(), asset.getParentId(), asset.getType());

        return new SocialAssetLibraryAssetActionResponse(assetId, true);
    }

    @Override
    public void performBulkAction(SocialAssetLibraryAssetBulkActionRequest assetBulkActionRequest,
                                  SocialAssetLibraryOperation operation, SocialAssetLibraryAction action) {
        super.publishAssetBulkActionAtomicEvents(assetBulkActionRequest, operation, action);
    }

    @Override
    public SocialPostAssetLibraryAssetActionResponse performPostAssetActionTask(Long assetId, SocialAssetLibraryActionEvent socialAssetLibraryActionEvent,
                                                                                SocialAssetLibraryOperation operation, SocialAssetLibraryAction action) {
        // After the asset is soft-deleted, check if it is a folder, if yes, then
        // delete all its children asset(s) too
        Optional<SocialAssetLibrary> softDeletedAssetO = socialAssetLibraryDBService.findSoftDeletedAssetByAssetId(assetId);
        if (!softDeletedAssetO.isPresent()) {
            log.error("[SocialAssetLibraryAssetDeleteActionService] performPostAssetActionTask cannot be performed for a non-existent or non-soft-deleted asset:{}", assetId);
            throw new BirdeyeSocialException(ErrorCodes.ASSET_LIB_INVALID_INPUT, "Soft Delete cannot be performed for a non-existent or non-soft-deleted asset");
        }

        SocialAssetLibrary softDeletedAsset = softDeletedAssetO.get();
        boolean isActionExecuted = false;

        // If the assetType is FOLDER then we need to delete its children
        // Else it is already been soft-deleted, hence, no other action required for a
        // non-folder type asset
        if (isFolderAsset(softDeletedAsset.getType())) {
            isActionExecuted = performChildrenAssetSoftDelete(assetId, socialAssetLibraryActionEvent);
        }
        return new SocialPostAssetLibraryAssetActionResponse(assetId, isActionExecuted);
    }

    private void evictAssetToParentFolderCache(Long assetId) {
        cacheService.clearCacheByKey(SocialAssetLibraryConstants.ASSET_TO_PARENT_FOLDER_CACHE, String.valueOf(assetId));
    }

    private boolean performChildrenAssetSoftDelete(Long parentFolderId, SocialAssetLibraryActionEvent socialAssetLibraryActionEvent) {
        // Populate only the ACTIVE asset(s) under this parent to be deleted
        List<SocialAssetLibraryLiteInfo> childrenAssetByParentInfo =
                socialAssetLibraryDBService.findActiveAssetInfoByParentIds(Collections.singletonList(parentFolderId));
        if (CollectionUtils.isEmpty(childrenAssetByParentInfo)) {
            log.info("[SocialAssetLibraryAssetDeleteActionService] performChildrenAssetSoftDelete no active children exists for parentFolderId:{}", parentFolderId);
            return false;
        } else {
            childrenAssetByParentInfo.forEach(childAsset -> {
                Long childAssetId = childAsset.getAssetId();
                SocialAssetLibraryPostAssetActionChildEvent postAssetActionChildEvent = convert(socialAssetLibraryActionEvent);
                postAssetActionChildEvent.setAssetId(childAssetId);
                postAssetActionChildEvent.setAssetType(childAsset.getType());
                publishPostAssetActionsTaskChildEvent(childAssetId, postAssetActionChildEvent);
            });
        }
        return true;
    }
}
