package com.birdeye.social.assetlibrary;

import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryAction;
import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryAssetType;
import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryConstants;
import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryOperation;
import com.birdeye.social.entities.assetlibrary.SocialAssetLibrary;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.service.CacheService;
import com.birdeye.social.sro.assetlibrary.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> on 19/10/23
 */
@Service
@Slf4j
public class SocialAssetLibraryAssetMoveActionService extends AbstractSocialAssetLibraryAssetActionService {

    @Autowired
    private SocialAssetLibraryDBService socialAssetLibraryDBService;

    @Autowired
    private CacheService cacheService;

    @Override
    public SocialAssetLibraryAssetActionResponse performAction(Long assetId, SocialAssetLibraryAssetActionRequest assetActionRequest, SocialAssetLibraryOperation operation,
                                                               SocialAssetLibraryAction action) {
        if (Objects.isNull(assetId)) {
            log.error("[SocialAssetLibraryAssetActionResponse] cannot proceed for a NULL assetId");
            throw new BirdeyeSocialException(ErrorCodes.ASSET_LIB_INVALID_INPUT, "Non existent asset cannot be moved");
        }
        Optional<SocialAssetLibrary> assetO = socialAssetLibraryDBService.findActiveAssetByAssetId(assetId);
        if (!assetO.isPresent()) {
            throw new BirdeyeSocialException(ErrorCodes.ASSET_LIB_ASSET_DNE, "This asset does not exists.");
        }
        SocialAssetLibrary asset = assetO.get();

        // Validate if the MOVE request is eligible
        validateMoveRequest(asset, assetActionRequest);

        Integer accountId = assetActionRequest.getAccountId();
        Long destinationParentFolderId = assetActionRequest.getParentFolderId();

        // Getting the asset name in the thus stated destination folder. If duplicate,
        // create a non-duplicate name for the same
        String formerAssetName = asset.getName();
        SocialAssetLibraryAssetType assetType = asset.getType();
        String newAssetName = getUniqueAssetName(formerAssetName, accountId, destinationParentFolderId, assetType);
        boolean isAssetRenamed = !StringUtils.equals(formerAssetName, newAssetName);

        Long userId = assetActionRequest.getUserId();
        SocialAssetLibraryAuditPersistenceDetail formerState = new SocialAssetLibraryAuditPersistenceDetail(asset.getAccountId(), userId);
        SocialAssetLibraryAuditPersistenceDetail currentState = new SocialAssetLibraryAuditPersistenceDetail(accountId, userId);

        formerState.setParentFolderId(asset.getParentId());
        currentState.setParentFolderId(destinationParentFolderId);

        // If the name is changed for the moved asset, audit the same as well.
        if (!isAssetRenamed) {
            formerState.setName(formerAssetName);
            currentState.setName(newAssetName);
        }

        // Update the parent
        asset.setName(newAssetName);
        asset.setParentId(destinationParentFolderId);
        asset.setUpdatedBy(userId);
        socialAssetLibraryDBService.saveAndFlush(asset);

        publishAssetActionEvent(assetId, assetActionRequest, operation, action);
        createAudit(assetId, formerState, currentState, operation, action);

        // Update the mapping for the moved asset
        cacheService.putInCache(SocialAssetLibraryConstants.ASSET_TO_PARENT_FOLDER_CACHE,
                                String.valueOf(assetId), new SocialAssetLibraryAssetParentCacheableInfo(destinationParentFolderId));

        // If asset got renamed while move operation is performed, clear the cache so
        // that the upcoming assets are not duplicated with this asset name
        if (isAssetRenamed) {
            evictAssetNameCache(accountId, destinationParentFolderId, assetType);
        }

        return new SocialAssetLibraryAssetActionResponse(assetId, newAssetName, true);
    }

    @Override
    public void performBulkAction(SocialAssetLibraryAssetBulkActionRequest assetBulkActionRequest,
                                  SocialAssetLibraryOperation operation, SocialAssetLibraryAction action) {
        if (Objects.isNull(assetBulkActionRequest.getDestinationFolderId())) {
            throw new BirdeyeSocialException(ErrorCodes.ASSET_LIB_INVALID_INPUT, "No valid destination is defined to execute move operation");
        }
        super.publishAssetBulkActionAtomicEvents(assetBulkActionRequest, operation, action);
    }

    private void validateMoveRequest(SocialAssetLibrary asset, SocialAssetLibraryAssetActionRequest assetActionRequest) {
        Long destinationParentFolderId = assetActionRequest.getParentFolderId();
        if (Objects.equals(asset.getParentId(), destinationParentFolderId)) {
            log.info("[SocialAssetLibraryAssetMoveActionService] source and destination folders cannot be same :{}", destinationParentFolderId);
            throw new BirdeyeSocialException(ErrorCodes.ASSET_LIB_INVALID_INPUT, "Source and destination Folders cannot be same");
        }
        Integer formerAccountId = asset.getAccountId();
        Integer destinationAccountId = assetActionRequest.getAccountId();
        if (!Objects.equals(formerAccountId, destinationAccountId)) {
            log.info("[SocialAssetLibraryAssetMoveActionService] cannot perform MOVE on assets from 2 different accounts. Source:{}, Destination:{}",
                     formerAccountId, destinationAccountId);
            throw new BirdeyeSocialException(ErrorCodes.ASSET_LIB_INVALID_INPUT, "Source and Destination accounts cannot be different");
        }
    }
}
