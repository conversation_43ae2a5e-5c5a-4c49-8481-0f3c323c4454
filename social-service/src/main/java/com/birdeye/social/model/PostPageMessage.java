package com.birdeye.social.model;

import lombok.ToString;

@ToString
public class PostPageMessage {
    private String pageName;
    private String postUrl;
    private String publishState;
    private String failureComment;
    private String pageId;
    private Integer isDisabled;
    private Integer sourceId;
    private String profilePictureUrl;
    private String locationName;
    private Integer locationId;
    private Boolean disconnected;
    private Integer failedBucket;
    private String childPostStatus;
    public String getChildPostStatus() {
        return childPostStatus;
    }
    public void setChildPostStatus(String childPostStatus) {
        this.childPostStatus = childPostStatus;
    }
    public Integer getLocationId() {
        return locationId;
    }

    public void setLocationId(Integer locationId) {
        this.locationId = locationId;
    }

    private String applePublishState;

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public String getPageName() {
        return pageName;
    }

    public void setPageName(String pageName) {
        this.pageName = pageName;
    }

    public String getPostUrl() {
        return postUrl;
    }

    public void setPostUrl(String postUrl) {
        this.postUrl = postUrl;
    }

    public String getPublishState() {
        return publishState;
    }

    public void setPublishState(String publishState) {
        this.publishState = publishState;
    }

    public String getFailureComment() {
        return failureComment;
    }

    public void setFailureComment(String failureComment) {
        this.failureComment = failureComment;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public Integer getIsDisabled() {
        return isDisabled;
    }

    public void setIsDisabled(Integer isDisabled) {
        this.isDisabled = isDisabled;
    }

    public String getProfilePictureUrl() {
        return profilePictureUrl;
    }

    public void setProfilePictureUrl(String profilePictureUrl) {
        this.profilePictureUrl = profilePictureUrl;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getApplePublishState() {
        return applePublishState;
    }

    public void setApplePublishState(String applePublishState) {
        this.applePublishState = applePublishState;
    }

    public Boolean getDisconnected() {
        return disconnected;
    }

    public void setDisconnected(Boolean disconnected) {
        this.disconnected = disconnected;
    }

    public Integer getFailedBucket() {
        return failedBucket;
    }

    public void setFailedBucket(Integer failedBucket) {
        this.failedBucket = failedBucket;
    }
}
