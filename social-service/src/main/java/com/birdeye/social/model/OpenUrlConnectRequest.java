package com.birdeye.social.model;

import com.birdeye.social.sro.PageRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OpenUrlConnectRequest {
    private String firebaseKey;
    private List<PageRequest> pageRequests;

    public String getFirebaseKey() {
        return firebaseKey;
    }

    public void setFirebaseKey(String firebaseKey) {
        this.firebaseKey = firebaseKey;
    }

    public List<PageRequest> getPageRequests() {
        return pageRequests;
    }

    public void setPageRequests(List<PageRequest> pageRequests) {
        this.pageRequests = pageRequests;
    }
}
