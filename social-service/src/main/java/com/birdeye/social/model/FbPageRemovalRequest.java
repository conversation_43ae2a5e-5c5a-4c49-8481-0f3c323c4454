package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FbPageRemovalRequest {

    public static class FbPageLiteDTO {
        private String pageId;
        private Integer businessId;

        public FbPageLiteDTO(String pageId, Integer businessId) {
            this.pageId = pageId;
            this.businessId = businessId;
        }

        public String getPageId() {
            return pageId;
        }

        public void setPageId(String pageId) {
            this.pageId = pageId;
        }

        public Integer getBusinessId() {
            return businessId;
        }

        public void setBusinessId(Integer businessId) {
            this.businessId = businessId;
        }
    }

    List<FbPageLiteDTO> fbPagesList;

    public List<FbPageLiteDTO> getFbPagesList() {
        return fbPagesList;
    }

    public void setFbPagesList(List<FbPageLiteDTO> fbPagesList) {
        this.fbPagesList = fbPagesList;
    }
}
