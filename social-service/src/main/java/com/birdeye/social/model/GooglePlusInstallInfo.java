package com.birdeye.social.model;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 *
 * <AUTHOR>
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class GooglePlusInstallInfo implements Serializable{

    /**
	 * 
	 */
	private static final long serialVersionUID = -8267502746803399308L;
	private List<GooglePlusUserPage> pages;
    private String nextPageToken;
    private Integer refreshTokenId;

    public GooglePlusInstallInfo() {
    }

    public GooglePlusInstallInfo(List<GooglePlusUserPage> pages) {
        this.pages = pages;
    }

    public List<GooglePlusUserPage> getPages() {
        return pages;
    }

    public void setPages(List<GooglePlusUserPage> pages) {
        this.pages = pages;
    }

	public String getNextPageToken() {
		return nextPageToken;
	}

	public void setNextPageToken(String nextPageToken) {
		this.nextPageToken = nextPageToken;
	}

	public Integer getRefreshTokenId() {
		return refreshTokenId;
	}

	public void setRefreshTokenId(Integer refreshTokenId) {
		this.refreshTokenId = refreshTokenId;
	}

	@Override
	public String toString() {
		return "GooglePlusInstallInfo [pages=" + pages + ", nextPageToken=" + nextPageToken + ", refreshTokenId="
				+ refreshTokenId + "]";
	}

}
