package com.birdeye.social.model.engageV2.message;

import com.birdeye.social.constant.EngageActionsEnum;
import com.birdeye.social.constant.Status;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class EventUpdateRequest implements Serializable {

    @NotNull
    private String eventId;

    private Integer sourceId;
    private String targetEventId; // used for block and unblock sync
    @NotNull
    private EngageActionsEnum eventType;
    private String from;
    private Status status;
}
