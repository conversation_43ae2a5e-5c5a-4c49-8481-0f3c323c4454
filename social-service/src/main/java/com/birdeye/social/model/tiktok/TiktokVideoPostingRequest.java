package com.birdeye.social.model.tiktok;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class TiktokVideoPostingRequest {

    @JsonProperty(value = "post_info")
    private TiktokPostInfo postInfo;

    @JsonProperty(value = "business_id")
    private String businessId;

    @JsonProperty(value = "video_url")
    private String videoUrl;
}
