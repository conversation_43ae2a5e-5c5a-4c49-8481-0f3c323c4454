package com.birdeye.social.model;

import com.birdeye.social.constant.RateLimitLevel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.time.temporal.ChronoUnit;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class APIRateLimit {

    private Integer id;
    private String apiMethod;
    private String sharedLimit;
    private String serviceLimitSync;
    private String serviceLimitAsync;
    private String sharedLimitAccess ;
    private String serviceName ;
    private String apiUrl ;
    private String channelName;
    private RateLimitLevel level;
    private Integer refillTime;
    private ChronoUnit timeUnit;
    private String groupQuota ;
    private String apiIdentifier;
}
