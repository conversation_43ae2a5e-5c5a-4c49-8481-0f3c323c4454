package com.birdeye.social.sro;

import java.util.ArrayList;
import java.util.List;

public class FbLocationsWithPermissions {

    private List<Integer> locationsMissingPermission = new ArrayList<>();
    private List<Integer> locationsHavingPermission = new ArrayList<>();
    private Integer missingPermissionCount;
    private Integer havingPermissionCount;
    private Integer totalLocations;

    public List<Integer> getLocationsMissingPermission() {
        return locationsMissingPermission;
    }

    public void setLocationsMissingPermission(List<Integer> locationsMissingPermission) {
        this.locationsMissingPermission = locationsMissingPermission;
    }

    public List<Integer> getLocationsHavingPermission() {
        return locationsHavingPermission;
    }

    public void setLocationsHavingPermission(List<Integer> locationsHavingPermission) {
        this.locationsHavingPermission = locationsHavingPermission;
    }

    public Integer getMissingPermissionCount() {
        return missingPermissionCount;
    }

    public void setMissingPermissionCount(Integer missingPermissionCount) {
        this.missingPermissionCount = missingPermissionCount;
    }

    public Integer getHavingPermissionCount() {
        return havingPermissionCount;
    }

    public void setHavingPermissionCount(Integer havingPermissionCount) {
        this.havingPermissionCount = havingPermissionCount;
    }

    public Integer getTotalLocations() {
        return totalLocations;
    }

    public void setTotalLocations(Integer totalLocations) {
        this.totalLocations = totalLocations;
    }
}
