package com.birdeye.social.sro;

public class ChannelPageReconnectRequest extends SocialRequest
{
	private static final long serialVersionUID = 6393998040794224777L;
	private String accessToken; // this will be temp access token for FB and code for GMB/Gplus
	private String pageId;      // this will be pageId for a channel, profileId for twitter
	private Boolean redirectToSetup; // This is for google. //TODO need to check why we need this.
	
	private String				authURL;					// twitter
	private String				requestToken;				// twitter
	private String				requestSecret;				// twitter
	private String				oauthVerifier;				// twitter
	
	// Any new Param for other channel will be added here
	/**
	 * @return the accessToken
	 */
	public String getAccessToken() {
		return accessToken;
	}
	/**
	 * @param accessToken the accessToken to set
	 */
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}
	/**
	 * @return the pageId
	 */
	public String getPageId() {
		return pageId;
	}
	/**
	 * @param pageId the pageId to set
	 */
	public void setPageId(String pageId) {
		this.pageId = pageId;
	}
	
	/**
	 * @return the redirectToSetup
	 */
	public Boolean getRedirectToSetup() {
		return redirectToSetup;
	}
	/**
	 * @param redirectToSetup the redirectToSetup to set
	 */
	public void setRedirectToSetup(Boolean redirectToSetup) {
		this.redirectToSetup = redirectToSetup;
	}
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("ChannelPageReconnectRequest [accessToken=");
		builder.append(accessToken);
		builder.append(", pageId=");
		builder.append(pageId);
		builder.append(", redirectToSetup=");
		builder.append(redirectToSetup);
		builder.append("]");
		return builder.toString();
	}
	/**
	 * @return the authURL
	 */
	public String getAuthURL() {
		return authURL;
	}
	/**
	 * @param authURL the authURL to set
	 */
	public void setAuthURL(String authURL) {
		this.authURL = authURL;
	}
	/**
	 * @return the requestToken
	 */
	public String getRequestToken() {
		return requestToken;
	}
	/**
	 * @param requestToken the requestToken to set
	 */
	public void setRequestToken(String requestToken) {
		this.requestToken = requestToken;
	}
	/**
	 * @return the requestSecret
	 */
	public String getRequestSecret() {
		return requestSecret;
	}
	/**
	 * @param requestSecret the requestSecret to set
	 */
	public void setRequestSecret(String requestSecret) {
		this.requestSecret = requestSecret;
	}
	/**
	 * @return the oauthVerifier
	 */
	public String getOauthVerifier() {
		return oauthVerifier;
	}
	/**
	 * @param oauthVerifier the oauthVerifier to set
	 */
	public void setOauthVerifier(String oauthVerifier) {
		this.oauthVerifier = oauthVerifier;
	}
	
}
