package com.birdeye.social.utils;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SocialFileHandlingUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(SocialFileHandlingUtils.class);

    public static List<File> getFilesToBeUploaded(List<String> imageUrls) {
        List<File> filesToBeUploaded = new ArrayList<>();
        File tempFile;
        for (String image : imageUrls) {
            try {
                URL website = new URL(image);
                LOGGER.info("website: {}", website.getPath());
                HttpURLConnection conn = (HttpURLConnection) website.openConnection();
                LOGGER.info("connection established with given url");
                tempFile = File.createTempFile(System.currentTimeMillis() + "", ".tmp");
                LOGGER.info("created temp file: {}", tempFile.getName());
                try {
                    LOGGER.info("making output stream");
                    FileOutputStream out = new FileOutputStream(tempFile);
                    LOGGER.info("created output stream");
                    IOUtils.copy(conn.getInputStream(), out);
                    LOGGER.info("copied data in output stream");
                } catch (IOException ex) {
                    LOGGER.warn("Error in writing temp file for image upload adding it to the list {} and error: {}", image, ex.getMessage());
                }
                LOGGER.info("adding temp file: {} to return list", tempFile.getName());
                filesToBeUploaded.add(tempFile);
            } catch (IOException ex) {
                LOGGER.warn("Error in writing temp file for image upload {} with error: {}", image, ex.getMessage());
            }
        }
        //TODO: DeleteOnExit or purge job on temp directory?
        return filesToBeUploaded;
    }
}
