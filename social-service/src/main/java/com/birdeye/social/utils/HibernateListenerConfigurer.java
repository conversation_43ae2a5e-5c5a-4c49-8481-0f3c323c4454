package com.birdeye.social.utils;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.listeners.EntityListener;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.EventType;
import org.hibernate.internal.SessionFactoryImpl;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManagerFactory;
import javax.persistence.PersistenceUnit;

@Component
public class HibernateListenerConfigurer {

    @PersistenceUnit
    private EntityManagerFactory emf;

    @PersistenceUnit(unitName = "socialPersistenceUnit")
    private EntityManagerFactory socialEmf;

    @PostConstruct
    protected void init() {
        if (CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBooleanProperty("enable.social.setup.audit", false)) {
            SessionFactoryImpl sessionFactory = emf.unwrap(SessionFactoryImpl.class);
            EventListenerRegistry registry = sessionFactory.getServiceRegistry().getService(EventListenerRegistry.class);

            registry.getEventListenerGroup(EventType.PERSIST).appendListener(EntityListener.INSTANCE);
            registry.getEventListenerGroup(EventType.UPDATE).appendListener(EntityListener.INSTANCE);
            registry.getEventListenerGroup(EventType.DELETE).appendListener(EntityListener.INSTANCE);
            registry.getEventListenerGroup(EventType.FLUSH_ENTITY).appendListener(EntityListener.INSTANCE);

            SessionFactoryImpl socialSessionFactory = socialEmf.unwrap(SessionFactoryImpl.class);
            EventListenerRegistry socialRegistry = socialSessionFactory.getServiceRegistry().getService(EventListenerRegistry.class);

            socialRegistry.getEventListenerGroup(EventType.PERSIST).appendListener(EntityListener.INSTANCE);
            socialRegistry.getEventListenerGroup(EventType.UPDATE).appendListener(EntityListener.INSTANCE);
            socialRegistry.getEventListenerGroup(EventType.DELETE).appendListener(EntityListener.INSTANCE);
            socialRegistry.getEventListenerGroup(EventType.FLUSH_ENTITY).appendListener(EntityListener.INSTANCE);
            socialRegistry.getEventListenerGroup(EventType.MERGE).appendListener(EntityListener.INSTANCE);
        }
    }
}
