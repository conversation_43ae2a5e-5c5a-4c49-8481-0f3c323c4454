/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.birdeye.social.platform.messages;

import twitter4j.auth.RequestToken;

/**
 *
 * <AUTHOR>
 */
public class OAuthRequestMessage {
    private String authURL;
    private String requestToken;
    private String requestSecret;
    private String oauthVerifier;

    public OAuthRequestMessage() {
    }

    public OAuthRequestMessage(RequestToken requestToken) {
        this.authURL = requestToken.getAuthenticationURL().replaceAll("http:", "https:");
        this.requestToken = requestToken.getToken();
        this.requestSecret = requestToken.getTokenSecret();
    }

    public String getAuthURL() {
        return authURL;
    }

    public void setAuthURL(String authURL) {
        this.authURL = authURL;
    }

    public String getOauthVerifier() {
        return oauthVerifier;
    }

    public void setOauthVerifier(String oauthVerifier) {
        this.oauthVerifier = oauthVerifier;
    }

    public String getRequestSecret() {
        return requestSecret;
    }

    public void setRequestSecret(String requestSecret) {
        this.requestSecret = requestSecret;
    }

    public String getRequestToken() {
        return requestToken;
    }

    public void setRequestToken(String requestToken) {
        this.requestToken = requestToken;
    }    
}
