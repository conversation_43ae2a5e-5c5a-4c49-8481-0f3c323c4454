package com.birdeye.social.service.SocialUnSubscribeService;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.SocialSetupAuditEnum;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.dao.SocialSetupAuditRepository;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.entities.SocialSetupAudit;
import com.birdeye.social.instagram.InstagramExternalService;
import com.birdeye.social.service.SocialUnSubscribeService.SocialUnSubscribe;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class IGUnSubscribe implements SocialUnSubscribe {
    @Autowired
    private InstagramExternalService instagramExternalService;
    @Autowired
    private SocialFBPageRepository fbPageRepository;
    @Autowired
    private SocialSetupAuditRepository auditRepository;
    @Autowired
    private BusinessInstagramAccountRepository instagramAccountRepository;
    private static final Logger LOGGER = LoggerFactory.getLogger(IGUnSubscribe.class);
    @Override
    public String channelName() {
        return SocialChannel.INSTAGRAM.getName();
    }

    @Override
    public void unsubscribeNotification(SocialSetupAudit socialSetupAudit) throws Exception {
        BusinessInstagramAccount instagramAccount = JSONUtils.fromJSON(socialSetupAudit.getEntity(),BusinessInstagramAccount.class);
        if(Objects.nonNull(instagramAccount)) {
            List<BusinessFBPage> fbPages = fbPageRepository.findByFacebookPageIdAndBusinessIdIsNotNull(instagramAccount.getFacebookPageId());
            if(CollectionUtils.isEmpty(fbPages)) {
                instagramExternalService.unSubscribeToFbApp(instagramAccount.getPageAccessToken());
                LOGGER.info("IG page unsubscribed for pageId: {}",instagramAccount.getInstagramAccountId());
                return;
            }
            LOGGER.info("Cannot unsubscribe IG page with pageId: {}, as parent fb account found",instagramAccount.getInstagramAccountId());
        }

    }

    @Override
    public List<SocialSetupAudit> getSocialSetupAuditIds(Date fromDate, SocialSetupAuditEnum action) {
        List<SocialSetupAudit> auditList = auditRepository.findByChannelAndActionAndCreatedGreaterThanEqualOrderByIdDesc(SocialChannel.INSTAGRAM.getName(), action.name(),fromDate);
        LOGGER.info("Entries found in Social setup audit: {}",auditList.size());
        if(CollectionUtils.isNotEmpty(auditList)) {
            List<String> pageIds = auditList.stream().map(SocialSetupAudit::getIntegrationId).collect(Collectors.toList());
            List<BusinessInstagramAccount> businessInstagramAccounts = new ArrayList<>();
            if(action.equals(SocialSetupAuditEnum.REMOVE_PAGE)) {
                businessInstagramAccounts = instagramAccountRepository.findByInstagramAccountIdIn(pageIds);
            } else if(action.equals(SocialSetupAuditEnum.REMOVE_MAPPING)) {
                businessInstagramAccounts = instagramAccountRepository.findByInstagramAccountIdInAndBusinessIdIsNotNull(pageIds);
            }
            Set<String> existingPageIds = businessInstagramAccounts.stream().map(BusinessInstagramAccount::getInstagramAccountId).collect(Collectors.toSet());
            LOGGER.info("Existing page count: {}", existingPageIds.size());
            auditList.removeIf(audit -> existingPageIds.contains(audit.getIntegrationId()));
            LOGGER.info("Final setup audit list size: {}", auditList.size());
        }
        return auditList;
    }
}
