package com.birdeye.social.service.SocialPostOperationService.Youtube;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.service.SocialPostOperationService.PostOperationUtil;
import com.birdeye.social.service.Youtube.YoutubeVideoUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class YoutubePostOperationServiceImpl implements YoutubePostOperationService{
    private static final Logger LOGGER = LoggerFactory.getLogger(YoutubePostOperationServiceImpl.class);
    @Autowired
    private YoutubeVideoUpload youtubeVideoUpload;
    @Autowired
    private SocialPostInfoRepository publishInfoRepository;
    @Autowired
    private PostOperationUtil postOperationUtil;

    @Override
    public void editPost(SocialPostPublishInfo publishInfo) {
        youtubeVideoUpload.editYoutubeVideoText(publishInfo);
        postOperationUtil.markOriginalPost(publishInfo);
    }

    @Override
    public void deletePost(SocialPostPublishInfo publishedPost) throws Exception {
        try {
            boolean success = youtubeVideoUpload.deleteVideo(publishedPost);
            if(!success) {
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_DELETE_YOUTUBE_OBJECT, "Post delete failed on youtube");
            }
            LOGGER.info("Post with publish info id: {} is deleted successfully from Youtube", publishedPost.getId());
        } catch (BirdeyeSocialException e) {
            if(e.getCode() == ErrorCodes.CLIENT_ERROR_404.value()) {
                LOGGER.info("Seems like {} post with publish info id: {} is already deleted, marking it as deleted in the system", SocialChannel.YOUTUBE.getName(), publishedPost.getId());
            } else {
                throw e;
            }
        }
    }
}
