package com.birdeye.social.service;

import com.birdeye.social.dto.SocialBusinessPageInfo;
import com.birdeye.social.model.ChannelSetupStatus;
import com.birdeye.social.model.SocialPageListInfo;
import com.birdeye.social.sro.AppleBrandMappingList;
import com.birdeye.social.sro.LocationMappingRequest;
import com.birdeye.social.sro.LocationPageMapping;
import com.birdeye.social.sro.LocationPageMappingRequest;

import java.util.List;

public interface AppleAccountService {

    void saveLocationPageMapping(Integer locationId, String fbPageId, Integer userId) throws Exception;

    ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId);

    public ChannelSetupStatus.PageSetupStatus getPageConnectionSetupStatus(Long enterpriseId);

    void removeLocationChannelMapping(List<LocationPageMappingRequest> locationPageMappings);

    LocationPageMapping getLocationMappingPages(LocationMappingRequest request);

    List<SocialPageListInfo> getUnmappedAppleLocationsByEnterpriseId(Long enterpriseId);

    AppleBrandMappingList getBrandMappingPages(String brandName, LocationMappingRequest request);

    void removeAppleLocation(List<String> locationIds);

    List<String> findByBusinessIdIn(List<Integer> businessIds);

    List<SocialBusinessPageInfo> findByBusinessIds(List<Integer> businessIds);
}
