package com.birdeye.social.service.SocialReportService.GMB;

import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.insights.PageInsights;
import com.birdeye.social.insights.PostData;
import com.birdeye.social.insights.Facebook.PostDataAndInsightResponse;
import com.birdeye.social.insights.InsightsRequest;
import com.birdeye.social.insights.PageInsightsResponse;
import com.birdeye.social.model.GMBReportInsightsResponse;

public interface GmbInsights {

    void getPostInsights(BusinessPosts businessPosts, Boolean isFreshRequest);

    void getPageInsightsFromGmb(SocialScanEventDTO getPageInsights);

    void postGmbPageInsightToES(PageInsights pageInsights);

    void postGmbPostInsightsToEs(PostData postData);
    PageInsightsResponse getGmbInsightsForPage(InsightsRequest insights) throws Exception;

    PostDataAndInsightResponse getGmbInsightsForPost(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam,
                                                     String sortOrder, boolean excelDownload);

    void startScanForPosts(String pageId);

    void saveCDNPostToES(BusinessPosts businessPosts);

    PostData createPostData(BusinessPosts businessPosts, GMBReportInsightsResponse reportInsightsResponse) throws Exception;

    PostDataAndInsightResponse getGmbInsightsForTopPost(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam,
                                                     String sortOrder, boolean excelDownload);
}
