package com.birdeye.social.service;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SocialAppCredsInfo implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7113424000016522383L;

	private String channel;

	private Long domainId;

	private String domainName;

	private String socialCredsId;

	private String channelClientId;

	private String channelClientSecret;
	
	private String channelAccessToken;

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public Long getDomainId() {
		return domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public String getSocialCredsId() {
		return socialCredsId;
	}

	public void setSocialCredsId(String googleCredsId) {
		this.socialCredsId = googleCredsId;
	}

	public String getChannelClientId() {
		return channelClientId;
	}

	public void setChannelClientId(String googleClientId) {
		this.channelClientId = googleClientId;
	}

	public String getChannelClientSecret() {
		return channelClientSecret;
	}

	public void setChannelClientSecret(String googleClientSecret) {
		this.channelClientSecret = googleClientSecret;
	}

	public String getChannelAccessToken() {
		return channelAccessToken;
	}

	public void setChannelAccessToken(String channelAccessToken) {
		this.channelAccessToken = channelAccessToken;
	}

	@Override
	public String toString() {
		return "SocialAppCredsInfo{" +
				"channel='" + channel + '\'' +
				", domainId=" + domainId +
				", domainName='" + domainName + '\'' +
				", socialCredsId='" + socialCredsId + '\'' +
				", channelClientId='" + channelClientId + '\'' +
				", channelClientSecret='" + channelClientSecret + '\'' +
				", channelAccessToken='" + channelAccessToken + '\'' +
				'}';
	}
}
