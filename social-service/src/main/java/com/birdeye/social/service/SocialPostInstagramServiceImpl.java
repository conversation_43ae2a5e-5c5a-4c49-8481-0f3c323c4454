package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.dao.SocialReservedAccountsRepo;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.SamayScheduleEventRequest;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.exception.TooManyRequestException;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.service.InstagramPostService;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.instagram.exception.InstagramContainerException;
import com.birdeye.social.instagram.response.BusinessDiscovery;
import com.birdeye.social.instagram.response.InstagramBaseResponse;
import com.birdeye.social.instagram.response.InstagramCompetitorProfile;
import com.birdeye.social.instagram.response.InstagramErrorResponse;
import com.birdeye.social.model.*;
import com.birdeye.social.model.linkinbio.LinkInfoEventRequest;
import com.birdeye.social.model.IgPostMetadata;
import com.birdeye.social.model.IgStoryEventRequest;
import com.birdeye.social.model.SocialPostSchedulerMetadata;
import com.birdeye.social.service.instagram.impl.IInstagramService;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.SamayUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.birdeye.social.constant.KafkaTopicEnum.SOCIAL_SYNC_BUSINESS_POSTS;

@Service
public class SocialPostInstagramServiceImpl implements SocialPostInstagramService{

    private static final Logger LOGGER = LoggerFactory.getLogger(SocialPostInstagramServiceImpl.class);

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private ISocialPostsAssetService socialPostsAssetService;

    @Autowired
    private SocialPostInfoRepository socialPostPublishInfoRepository;

    @Autowired
    private BusinessInstagramAccountRepository instagramAccountRepository;

    @Autowired
    private InstagramPostService instagramPostService;
    @Autowired
    private KafkaExternalService kafkaExternalService;

    @Autowired
    private IPermissionMappingService permissionMappingService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private IInstagramService instagramService;
    @Autowired
    private SamayService samayService;

    @Autowired
    private SocialReservedAccountsRepo reservedAccountsRepo;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    private static final String IG_STORY_EVENT = "ig-story-event";


    private void processingPostAudit(SocialPostPublishInfo publishInfo, String failureReason, Integer code, String creationId, List<String> mediaId, Integer bucketId) {
        String carousalContainer = CollectionUtils.isEmpty(mediaId)?null:String.join(",",mediaId);
        publishInfo.setIsPublished(SocialPostStatusEnum.PROCESSING.getId());
        publishInfo.setFailureReason(failureReason);
        publishInfo.setFailureCode(code);
        publishInfo.setPostId(Objects.isNull(creationId)?carousalContainer:creationId);
        publishInfo.setBucket(bucketId);
        socialPostPublishInfoRepository.saveAndFlush(publishInfo);
        kafkaExternalService.publishSocialPostEvent(publishInfo);
    }
    private void failedPostAudit(SocialPostPublishInfo publishInfo, String failureReason, Integer code, Integer bucketId) {

        if(Objects.isNull(bucketId)) {
            PermissionMapping permissionMappingUnknown = permissionMappingService.getDataByChannelAndActualErrorText(Constants.INSTAGRAM, failureReason);
            if(Objects.isNull(permissionMappingUnknown)){
                permissionMappingUnknown = permissionMappingService.getDataByChannelAndPermissionCode(Constants.INSTAGRAM, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
            }
            publishInfo.setFailureReason(permissionMappingUnknown.getErrorMessage());
            publishInfo.setBucket(permissionMappingUnknown.getBucket());
        } else {
            publishInfo.setFailureReason(failureReason);
            publishInfo.setFailureCode(code);
            publishInfo.setBucket(bucketId);
        }
        publishInfo.setFailureCode(code);
        publishInfo.setIsPublished(SocialPostStatusEnum.FAILED.getId());
        socialPostPublishInfoRepository.saveAndFlush(publishInfo);
        kafkaExternalService.publishSocialPostEvent(publishInfo);
    }

    private List<String> getMediaUrl(SocialPost socialPost, Long businessNumber) {
        List<String> mediaUrls = new ArrayList<>();

        if (Objects.nonNull(socialPost.getImageIds())) {
            List<Integer> imageIds = Stream.of(socialPost.getImageIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            mediaUrls = socialPostsAssetService.findImageUrlsByIds(imageIds, Long.toString(businessNumber));
        }

        if (Objects.nonNull(socialPost.getVideoIds())) {
            List<Integer> videoIds = Stream.of(socialPost.getVideoIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            mediaUrls = socialPostsAssetService.findVideoUrlsByIds(videoIds, Long.toString(businessNumber));
        }
        return org.apache.commons.collections4.CollectionUtils.isNotEmpty(mediaUrls)?mediaUrls:new ArrayList<>();
    }

    private List<String> getMediaRequest(String mediaIds, Integer businessId, boolean isVideo, BusinessLiteDTO businessLiteDTO) {
        List<String> mediaUrl = new ArrayList<>();
        if(StringUtils.isEmpty(mediaIds))
            return mediaUrl;

        String[] mediaIdsConversion = mediaIds.split(",");
        List<Integer> ids = new ArrayList<>();
        for (String mediaId : mediaIdsConversion) {
            ids.add(Integer.parseInt(mediaId));
        }

        return isVideo
                ? socialPostsAssetService.findVideoUrlsByIds(ids, businessLiteDTO.getBusinessNumber().toString())
                : socialPostsAssetService.findImageUrlsByIds(ids, businessLiteDTO.getBusinessNumber().toString());
    }


    private String createContainer(String accountId, String accessToken, String contentUrl, boolean isVideo, boolean isCarouselItem, boolean isCarousel,
                                  List<String> mediaId, String text, IgPostMetadata igPostMetadata, String coverUrl) {
        String url = accountId+"/media";
        MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
        parametersMap.set("access_token",accessToken);

        if(isCarousel) {
            parametersMap.set("media_type", "CAROUSEL");
            parametersMap.set("children", mediaId.toString());
            parametersMap.set("caption",text);
        } else if(Objects.nonNull(igPostMetadata) && igPostMetadata.getType().equalsIgnoreCase("story")) {
            if(isVideo) {
                parametersMap.set("video_url",contentUrl);
            }else {
                parametersMap.set("image_url",contentUrl);
            }
            parametersMap.set("media_type","STORIES");
        } else if(Objects.nonNull(igPostMetadata) && igPostMetadata.getType().equalsIgnoreCase("reel")) {
            parametersMap.set("video_url",contentUrl);
            parametersMap.set("media_type","REELS");
            parametersMap.set("caption",text);
            parametersMap.set("share_to_feed", String.valueOf(igPostMetadata.getShareOnFeed()));
            parametersMap.set("cover_url",coverUrl);
        } else {
            if(isVideo) {
                parametersMap.set("video_url",contentUrl);
                parametersMap.set("media_type","REELS");
            }else {
                parametersMap.set("image_url",contentUrl);
            }
            if(isCarouselItem){
                parametersMap.set("is_carousel_item","true");
            }else{
                parametersMap.set("caption",text);
            }
        }
        return instagramPostService.uploadContent(url,parametersMap);
    }

    private void postMedia(SocialPostPublishInfo publishInfo, String igAccountId, String accessToken, String creationId) throws Exception {
        String postId = instagramPostService.postContent(igAccountId, accessToken, creationId);
        String postURL = instagramPostService.getMediaPostURL(postId,accessToken);
        LOGGER.info("POST URL: {}",postURL);
        publishInfo.setIsPublished(SocialPostStatusEnum.PUBLISHED.getId());
        publishInfo.setPostId(postId);
        publishInfo.setPostUrl(postURL);
        publishInfo.setFailureCode(null);
        publishInfo.setFailureReason(null);
        publishInfo.setBucket(null);
        socialPostPublishInfoRepository.saveAndFlush(publishInfo);
        if(SocialPostStatusEnum.PUBLISHED.getId().equals(publishInfo.getIsPublished())
                && commonService.isBusinessAllowedToSyncBusinessPosts(publishInfo.getEnterpriseId())) {
            kafkaProducerService.sendObjectV1(SOCIAL_SYNC_BUSINESS_POSTS.getName(), SocialPostPublishInfoRequest.builder().id(publishInfo.getId()).build());
        }
        kafkaExternalService.publishSocialPostEvent(publishInfo);
    }

    private void retryProcessingPost(SocialPostPublishInfo socialPostPublishInfo,List<String> creationIds, String accessToken, String igAccountId) throws IOException {
        String creationId = null;
        try {
            if (creationIds.size() == 1) { //Non-carousel
                instagramPostService.checkContainerStatus(creationIds.get(0), accessToken);
                creationId = creationIds.get(0);
            } else { //carousel
                for (String containerId : creationIds) {
                    String containerStatus = instagramPostService.checkContainerStatus(containerId, accessToken);
                    if (!containerStatus.equalsIgnoreCase("FINISHED")) {
                        LOGGER.info("[IG retry]Failed to create container::{} ",containerStatus);
                        throw new InstagramContainerException(ExternalAPIErrorCode.FAILED_TO_CREATE_CONTAINER.getDescription(), ErrorCodes.FAILED_TO_CREATE_CONTAINER.value());
                    }
                }
                String text = socialPostPublishInfo.getSocialPost().getPostText();
                creationId = createContainer(igAccountId, accessToken, null, false, false, true, creationIds, text,null,null);
                instagramPostService.checkContainerStatus(creationId, accessToken);
            }
            postMedia(socialPostPublishInfo, igAccountId, accessToken, creationId);
        }
        catch (HttpStatusCodeException ex) {
            ObjectMapper mapper = new ObjectMapper();
            InstagramBaseResponse instagramBaseResponse = mapper.readValue(ex.getResponseBodyAsString(), InstagramBaseResponse.class);
            InstagramErrorResponse instagramErrorResponse = instagramBaseResponse != null ? instagramBaseResponse.getError() : new InstagramErrorResponse();
            LOGGER.info("[IG retry] IG error from graph api for business id:{} : {}",socialPostPublishInfo.getBusinessId(),instagramErrorResponse);
            String message;
            if(Objects.nonNull(instagramErrorResponse.getError_user_msg())&&!instagramErrorResponse.getError_user_msg().isEmpty()) {
                message = instagramErrorResponse.getError_user_msg();
            }
            else {
                message = instagramErrorResponse.getMessage();
            }
            PermissionMapping pm = errorHandlerForInstagramService(instagramErrorResponse.getCode(), instagramErrorResponse.getError_subcode(), message);
            if(Objects.nonNull(pm) && pm.getMarkInvalid() == 1) {
                kafkaExternalService.markPageInvalid(SocialChannel.INSTAGRAM.getName(), socialPostPublishInfo.getExternalPageId());
            }

            if(Objects.isNull(pm)) {
                failedPostAudit(socialPostPublishInfo,message,instagramErrorResponse.getCode(), null);
            } else {
                failedPostAudit(socialPostPublishInfo,pm.getErrorMessage(),instagramErrorResponse.getCode(), pm.getBucket());
            }
        }
        catch (InstagramContainerException ex) {
            LOGGER.info("[IG retry] IG error in container status for business id: {}, message: {}",socialPostPublishInfo.getBusinessId(),ex.getFailureReason());
            if(ex.getFailureReason().contains("IN_PROGRESS")) {
                LOGGER.info("[IG retry] Container is still IN_PROGRESS for post: {}",socialPostPublishInfo);
               // processingPostAudit(socialPostPublishInfo,ex.getFailureReason(),ex.getFailureCode(),creationId,creationIds);
                PermissionMapping pm = errorHandlerForInstagramService(ex.getFailureCode(), null, ex.getFailureReason());
                if(Objects.nonNull(pm) && pm.getMarkInvalid() == 1) {
                    kafkaExternalService.markPageInvalid(SocialChannel.INSTAGRAM.getName(), socialPostPublishInfo.getExternalPageId());
                }
                processingPostAudit(socialPostPublishInfo, pm.getErrorMessage(),ex.getFailureCode(),creationId,creationIds, pm.getBucket());
            } else {
                PermissionMapping pm = errorHandlerForInstagramService(ex.getFailureCode(), null, ex.getFailureReason());
                if(Objects.nonNull(pm) && pm.getMarkInvalid() == 1) {
                    kafkaExternalService.markPageInvalid(SocialChannel.INSTAGRAM.getName(), socialPostPublishInfo.getExternalPageId());
                }
                failedPostAudit(socialPostPublishInfo, pm.getErrorMessage(), ex.getFailureCode(), pm.getBucket());
            }
        }
        catch (Exception e) {
            LOGGER.info("[IG retry] IG error for business id: {} : {}",socialPostPublishInfo.getBusinessId(),e.getMessage());
            failedPostAudit(socialPostPublishInfo,e.getMessage(),null, null);
        }
    }

    private void containerStatusCheckAsync(String igAccountId, String accessToken, Integer publishInfoId, String media, Boolean isStory,
                                           IgPostMetadata igPostMetadata, List<String> containerIds, String postText) {
        IgContainerCheckDTO igContainerCheckDTO = IgContainerCheckDTO.builder()
                .igAccountId(igAccountId)
                .publishInfoId(publishInfoId)
                .containerIds(containerIds)
                .accessToken(accessToken)
                .isStory(isStory)
                .igPostMetadata(igPostMetadata)
                .text(postText)
                .media(media).build();
        kafkaExternalService.pushIgContainerCheck(igContainerCheckDTO);
    }

    @Override
    public void postOnInstagram(SocialPostPublishInfo publishInfo) throws Exception {
        LOGGER.info("Instagram posting started for post: {}",publishInfo);
        BusinessInstagramAccount businessInstagramAccount = instagramAccountRepository.findById(publishInfo.getPageId());
        if (Objects.isNull(businessInstagramAccount)) {
            LOGGER.info("Instagram page not found for post:{}", publishInfo);
            //  errorHandlerForInstagramService(400, 1, ""); custom internal handler
            PermissionMapping pm = errorHandlerForInstagramService(ErrorCodes.NO_PROFILE_MAPPED_BUSINESS.value(), -1, ExternalAPIErrorCode.NO_PROFILE_MAPPED_BUSINESS.getDescription());
            if(Objects.nonNull(pm) && pm.getMarkInvalid() == 1) {
                kafkaExternalService.markPageInvalid(SocialChannel.INSTAGRAM.getName(), publishInfo.getExternalPageId());
            }
            failedPostAudit(publishInfo, pm.getErrorMessage(),null, pm.getBucket());
           // failedPostAudit(publishInfo,"No profile mapped to the business",null);
            return;
        }
        String creationId = null;
        List<String> mediaId = null;
        try {
            String accessToken = businessInstagramAccount.getPageAccessToken();
            String text = publishInfo.getSocialPost().getPostText();
            BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(publishInfo.getEnterpriseId(), false);
            List<String> imageMediaRequests = getMediaRequest(publishInfo.getSocialPost().getImageIds(), publishInfo.getEnterpriseId(),false, businessLiteDTO);
            List<String> videoMediaRequests = getMediaRequest(publishInfo.getSocialPost().getVideoIds(), publishInfo.getEnterpriseId(),true, businessLiteDTO);

            if(CollectionUtils.isEmpty(imageMediaRequests) && CollectionUtils.isEmpty(videoMediaRequests)) {
                LOGGER.info("Can not post to Instagram without any media for post:{}", publishInfo);
//                errorHandlerForInstagramService(400, 1, ""); custom internal handler
               // failedPostAudit(publishInfo,"No media found for posting",null);
                PermissionMapping pm = errorHandlerForInstagramService(ErrorCodes.NO_MEDIA_FOUND_POSTING.value(), -1, ExternalAPIErrorCode.NO_MEDIA_FOUND_POSTING.getDescription());
                        failedPostAudit(publishInfo, pm.getErrorMessage(),null, pm.getBucket());
                if(Objects.nonNull(pm) && pm.getMarkInvalid() == 1) {
                    kafkaExternalService.markPageInvalid(SocialChannel.INSTAGRAM.getName(), publishInfo.getExternalPageId());
                }
                return;
            }

            String igAccountId = businessInstagramAccount.getInstagramAccountId();
            String media = null;
            IgPostMetadata igPostMetadata = null;
            if(Objects.nonNull(publishInfo.getSocialPost().getPostMetadata())){
                SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(publishInfo.getSocialPost().getPostMetadata(),SocialPostSchedulerMetadata.class);
                if(Objects.nonNull(metadata) && Objects.nonNull(metadata.getIgPostMetadata())) {
                    igPostMetadata = JSONUtils.fromJSON(metadata.getIgPostMetadata(), IgPostMetadata.class);
                }
            }

            boolean isStory = false;
            String coverUrl = null;
            boolean checkIgContainerAsyncFlag = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIgContainerCheckAsync();
            if(StringUtils.isNotEmpty(publishInfo.getSocialPost().getVideoIds())) {
                List<Integer> ids = Arrays.stream(publishInfo.getSocialPost().getVideoIds().split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                coverUrl = socialPostsAssetService.findThumbnailById(ids.get(0));
                media = coverUrl;
            }
            if(Objects.nonNull(igPostMetadata) && igPostMetadata.getType().equalsIgnoreCase("reel")) {
                LOGGER.info("[IG posting] Reel posting for user: {}",igAccountId);
                creationId = createContainer(igAccountId,accessToken,videoMediaRequests.get(0),true,false,false,null,text,igPostMetadata,coverUrl);
            } else if(Objects.nonNull(igPostMetadata) && igPostMetadata.getType().equalsIgnoreCase("story")) {
                isStory = true;
                LOGGER.info("[IG posting] Story posting for user: {}",igAccountId);
                if(!CollectionUtils.isEmpty(imageMediaRequests) && CollectionUtils.isEmpty(videoMediaRequests)) {
                    creationId = createContainer(igAccountId,accessToken,imageMediaRequests.get(0),false,false,false,null,text,igPostMetadata,coverUrl);
                    media = imageMediaRequests.get(0);
                } else if(CollectionUtils.isEmpty(imageMediaRequests) && !CollectionUtils.isEmpty(videoMediaRequests)) {
                    creationId = createContainer(igAccountId,accessToken,videoMediaRequests.get(0),true,false,false,null,text,igPostMetadata,coverUrl);
                } else {
                    LOGGER.info("[IG posting] There can be only 1 media in story, terminating the flow for id: {}",publishInfo.getId());
                    return;
                }
            } else if (imageMediaRequests.size() == 1 && CollectionUtils.isEmpty(videoMediaRequests)) { //single image
                LOGGER.info("[IG posting] Single image posting for user: {}",igAccountId);
                creationId = createContainer(igAccountId,accessToken,imageMediaRequests.get(0),false,false,false,null,text,igPostMetadata,coverUrl);
                media = imageMediaRequests.get(0);
            } else if (CollectionUtils.isEmpty(imageMediaRequests) && videoMediaRequests.size()==1) { //single video
                LOGGER.info("[IG posting] Single video posting for user: {}",igAccountId);
                creationId = createContainer(igAccountId,accessToken,videoMediaRequests.get(0),true,false,false,null,text,igPostMetadata,coverUrl);
            } else { //carousel
                LOGGER.info("[IG posting] Carousel posting for user: {}",igAccountId);
                List<String> mediaSequence = new ArrayList<>();
                if(Objects.nonNull(publishInfo.getSocialPost().getPostMetadata())) {
                    //Map<?, ?> m = new ObjectMapper().readValue(publishInfo.getSocialPost().getPostMetadata(), Map.class);
                    //mediaSequence = new ObjectMapper().readValue((String) m.get("mediaSequence"), List.class);
                    mediaSequence = getMediaUrl(publishInfo.getSocialPost(), businessLiteDTO.getBusinessNumber());
                    if(!CollectionUtils.isEmpty(mediaSequence)) {
                        media = mediaSequence.get(0);
                    }
                }
                mediaId = new ArrayList<>();
                for(String mediaUrl : mediaSequence) {
                    LOGGER.info("mediaUrl: {}",mediaUrl);
                    if(imageMediaRequests.contains(mediaUrl)) {
                        LOGGER.info("Image: {}",mediaUrl);
                        mediaId.add(createContainer(igAccountId,accessToken,mediaUrl,false,true,false,null,null,igPostMetadata,coverUrl));
                    } else {
                        LOGGER.info("Video: {}",mediaUrl);
                        mediaId.add(createContainer(igAccountId,accessToken,mediaUrl,true,true,false,null,null,igPostMetadata,coverUrl));
                    }
                }
                if(checkIgContainerAsyncFlag) {
                    LOGGER.info("Pushing to kafka for ig container status check for Carousel!");
                    containerStatusCheckAsync(businessInstagramAccount.getInstagramAccountId(),businessInstagramAccount.getPageAccessToken(),publishInfo.getId(),media,isStory,igPostMetadata, mediaId, text);
                    return;
                }
                for (String containerId : mediaId) {
                    String containerStatus = instagramPostService.checkContainerStatus(containerId, accessToken);
                    if(!containerStatus.equalsIgnoreCase("FINISHED")) {
                        throw new InstagramContainerException("Failed to create container: "+containerStatus,null);
                    }
                }
                creationId = createContainer(igAccountId, accessToken,null,false,false,true, mediaId, text,igPostMetadata,coverUrl);
            }
            if(checkIgContainerAsyncFlag) {
                LOGGER.info("Pushing to kafka for ig container status check!");
                containerStatusCheckAsync(businessInstagramAccount.getInstagramAccountId(),businessInstagramAccount.getPageAccessToken(),publishInfo.getId(),media,isStory,igPostMetadata, Collections.singletonList(creationId),text);
            } else {
                LOGGER.info("Posting on IG through regular flow!!!");
                instagramPostService.checkContainerStatus(creationId, accessToken);
                postMedia(publishInfo, igAccountId, accessToken, creationId);
                additionalPostingSteps(publishInfo, businessInstagramAccount, accessToken, igAccountId, media, igPostMetadata, isStory);
            }
        }
        catch (HttpStatusCodeException | TooManyRequestException ex) {
            ObjectMapper mapper = new ObjectMapper();
            BirdeyeSocialException birdeyeSocialException;
            InstagramBaseResponse instagramBaseResponse = null;
            if (ex instanceof HttpStatusCodeException) {
                try {
                    instagramBaseResponse =mapper.readValue(((HttpStatusCodeException)ex).getResponseBodyAsString(), InstagramBaseResponse.class);
                } catch (Exception e) {
                    LOGGER.error("error parsing HttpStatusCodeException to instagramBaseResponse");
                }
                birdeyeSocialException = convertToBirdeyeException((HttpStatusCodeException) ex, instagramBaseResponse);
            } else {
                birdeyeSocialException = new BirdeyeSocialException(ErrorCodes.TOO_MANY_REQUESTS, ex.getMessage(), new HashMap<>());
            }

            boolean isEligibleForRetry = commonService.retryPostIfEligible(birdeyeSocialException, publishInfo);
            if(!isEligibleForRetry) {
                InstagramErrorResponse instagramErrorResponse = instagramBaseResponse != null ? instagramBaseResponse.getError() : new InstagramErrorResponse();
                LOGGER.info("[social post] IG error from graph api for business id:{} : {}",publishInfo.getBusinessId(),instagramErrorResponse);
                String errorMessage = null;
                if(Objects.nonNull(instagramErrorResponse.getError_user_msg())&&!instagramErrorResponse.getError_user_msg().isEmpty()) {
                    errorMessage = instagramErrorResponse.getError_user_msg();
                }
                else {
                    errorMessage = instagramErrorResponse.getMessage();
                }
                PermissionMapping pm = errorHandlerForInstagramService(instagramErrorResponse.getCode(), instagramErrorResponse.getError_subcode(), errorMessage);
                if(Objects.nonNull(pm) && pm.getMarkInvalid() == 1) {
                    kafkaExternalService.markPageInvalid(SocialChannel.INSTAGRAM.getName(), publishInfo.getExternalPageId());
                }
                if(Objects.isNull(pm)) {
                    failedPostAudit(publishInfo,null,instagramErrorResponse.getCode(), null);
                } else {
                    failedPostAudit(publishInfo,pm.getErrorMessage(),instagramErrorResponse.getCode(), pm.getBucket());
                }
            }
        }
        catch (InstagramContainerException ex) {
            LOGGER.info("[social post] IG error in container status for business id: {}, message: {}",publishInfo.getBusinessId(),ex.getFailureReason());
            if(ex.getFailureReason().contains("IN_PROGRESS")) {
                PermissionMapping pm = errorHandlerForInstagramService(ex.getFailureCode(), null, ex.getFailureReason());
                if(Objects.nonNull(pm) && pm.getMarkInvalid() == 1) {
                    kafkaExternalService.markPageInvalid(SocialChannel.INSTAGRAM.getName(), publishInfo.getExternalPageId());
                }
                processingPostAudit(publishInfo, pm.getErrorMessage(),ex.getFailureCode(),creationId,mediaId, pm.getBucket());
            } else {
                PermissionMapping pm = errorHandlerForInstagramService(ex.getFailureCode(), null, ex.getFailureReason());
                if(Objects.nonNull(pm) && pm.getMarkInvalid() == 1) {
                    kafkaExternalService.markPageInvalid(SocialChannel.INSTAGRAM.getName(), publishInfo.getExternalPageId());
                }
                failedPostAudit(publishInfo, pm.getErrorMessage(), ex.getFailureCode(), pm.getBucket());
            }
        }
        catch (Exception e) {
            LOGGER.info("[social post] IG error for business id: {} : {}",publishInfo.getBusinessId(),e.getMessage());
            failedPostAudit(publishInfo,e.getMessage(),null, null);
        }

    }

    private void additionalPostingSteps(SocialPostPublishInfo publishInfo, BusinessInstagramAccount businessInstagramAccount, String accessToken, String igAccountId, String media, IgPostMetadata igPostMetadata, boolean isStory) {
        if(isStory) {
            IgStoryEventRequest igStoryEventRequest = new IgStoryEventRequest();
            igStoryEventRequest.setIgAccountId(igAccountId);
            Date date = DateUtils.addMinutes(new Date(),15);
            long scheduleDateToEpoch = date.toInstant().toEpochMilli();
            SamayScheduleEventRequest samayRequest = SamayUtils.getSamayScheduleEventRequest(JSONUtils.toJSON(igStoryEventRequest), Long.valueOf(businessInstagramAccount.getId()),
                    Long.valueOf(businessInstagramAccount.getBusinessId()), 0, 0, SamayRetryTypeEnum.SIMPLE.name(), scheduleDateToEpoch, SamayEventTypeEnum.MESSENGER.getType(),
                    IG_STORY_EVENT,true);


            LOGGER.info("IG story: Submitting request to samay for request: {}", samayRequest);
            samayService.pushMessageToSamayScheduler(samayRequest);
        }
        try {
            //logic for first comment
            if (Objects.nonNull(igPostMetadata) && StringUtils.isNotEmpty(igPostMetadata.getFirstComment())) {
                LOGGER.info("Posting first comment for IG post id: {}, comment: {}", publishInfo.getPostId(), igPostMetadata.getFirstComment());
                instagramService.postCommentOnAnInstagramPost(accessToken, publishInfo.getPostId(), igPostMetadata.getFirstComment());
            }
        } catch (Exception e) {
            LOGGER.info("Failed to post first comment with error: {}",e.getMessage());
        }
        LOGGER.info("[social post] IG posting complete for post: {}", publishInfo);
        pushToPostLinkInBio(publishInfo, media);
    }

    @Cacheable(value = "createLinkInBio", key = "#publishInfo.postId", unless = "#result == null")
    private void pushToPostLinkInBio(SocialPostPublishInfo publishInfo,String media) {
        if(Objects.nonNull(publishInfo.getSocialPost())
                && Objects.nonNull(publishInfo.getSocialPost().getPostMetadata())
                && publishInfo.getSocialPost().getPostMetadata().contains("linkInBioDetails")){
            LinkInfoEventRequest linkInfoEventRequest = LinkInfoEventRequest.builder()
                    .postId(publishInfo.getSocialPostId())
                    .image(media)
                    .enterpriseId(publishInfo.getEnterpriseId())
                    .businessId(publishInfo.getBusinessId())
                    .build();
            kafkaExternalService.pushLinkInBio(linkInfoEventRequest);
        }
    }

    private BirdeyeSocialException convertToBirdeyeException(HttpStatusCodeException e, InstagramBaseResponse instagramBaseResponse) {
        Map<String, Object> errorMap = new HashMap<String, Object>();
        try {
            errorMap.put("http_response", e.getRawStatusCode());
            String errorMessage = null;
            if(Objects.nonNull(instagramBaseResponse)) {

                if (Objects.nonNull(instagramBaseResponse.getError().getCode())) {
                    errorMap.put("error_code", instagramBaseResponse.getError().getCode());
                }
                if (Objects.nonNull(instagramBaseResponse.getError().getError_subcode())) {
                    errorMap.put("error_sub_code", instagramBaseResponse.getError().getError_subcode());
                }
                if (Objects.nonNull(instagramBaseResponse.getError().getMessage())) {
                    errorMap.put("error_message", instagramBaseResponse.getError().getMessage());
                    errorMessage = instagramBaseResponse.getError().getMessage();
                }
            }

            return new BirdeyeSocialException(e.getRawStatusCode(), StringUtils.isNotEmpty(errorMessage)?errorMessage:e.getMessage(), errorMap);
        }
        catch(Exception e1){
            return new BirdeyeSocialException(e.getRawStatusCode(),e.getMessage(),errorMap);
        }
    }


    private PermissionMapping errorHandlerForInstagramService(Integer errorCode, Integer subErrorCode, String message) {
        List<PermissionMapping> permissionMappings =  new ArrayList<>();
        // 9007
        // IN_PROGRESS
        if(Objects.nonNull(errorCode) && Objects.nonNull(subErrorCode)) {
            permissionMappings = permissionMappingService.getDataByChannelAndParentErrorCodeAndPermissionCode(Constants.INSTAGRAM,
                    errorCode, subErrorCode);
        } else if(Objects.nonNull(errorCode)){
            //check with signed int when sub-code is not present
            permissionMappings = permissionMappingService.getDataByChannelAndParentErrorCodeAndPermissionCodeNull(Constants.INSTAGRAM,
                    errorCode, -1);
        } else {
            permissionMappings = permissionMappingService.getListOfDataByChannelAndPermissionCode(Constants.INSTAGRAM, subErrorCode);
        }
        if(CollectionUtils.isEmpty(permissionMappings)) {
            LOGGER.info("New error found for IG posting with errorCode: {} subcode: {} and message: {}", errorCode, subErrorCode, message);
            return permissionMappingService.getDataByChannelAndPermissionCode(SocialChannel.INSTAGRAM.getName(),
                    Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
        }
        if(permissionMappings.size() == 1) return permissionMappings.get(0);

        for(PermissionMapping pm: permissionMappings) {
            if(StringUtils.isNotEmpty(message) && message.contains(pm.geterrorActualMessage())){
                return pm;
            }
        }
        LOGGER.info("New error found for IG posting with errorCode: {} subcode: {} and message: {}", errorCode, subErrorCode, message);
        return permissionMappingService.getDataByChannelAndPermissionCode(SocialChannel.INSTAGRAM.getName(),
                Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
    }

    @Async
    @Override
    public void retryPosts(List<SocialPostPublishInfo> posts) throws Exception{

        for(SocialPostPublishInfo socialPostPublishInfo: posts) {
            Date date = DateUtils.addHours(new Date(),-12);
            String creationIdString = socialPostPublishInfo.getPostId();
            List<String> creationIds = Arrays.asList(creationIdString.split(","));
            BusinessInstagramAccount businessInstagramAccount = instagramAccountRepository.findById(socialPostPublishInfo.getPageId());
            if (CollectionUtils.isEmpty(creationIds)) {
                LOGGER.info("[IG retry] creation id is not present for post: {}", socialPostPublishInfo);
                PermissionMapping pm = errorHandlerForInstagramService(ErrorCodes.NO_CONTAINER_ID_FOUND_FOR_POSTING.value(), -1, ExternalAPIErrorCode.NO_CONTAINER_ID_FOUND_FOR_POSTING.getDescription());
                if(Objects.nonNull(pm) && pm.getMarkInvalid() == 1) {
                    kafkaExternalService.markPageInvalid(SocialChannel.INSTAGRAM.getName(), socialPostPublishInfo.getExternalPageId());
                }
                failedPostAudit(socialPostPublishInfo, pm.getErrorMessage(),null, pm.getBucket());
               // failedPostAudit(socialPostPublishInfo, "No container id found for posting", null);
            } else if (Objects.isNull(businessInstagramAccount)) {
                LOGGER.info("[IG retry] Instagram page not found for post:{}", socialPostPublishInfo);
                PermissionMapping pm = errorHandlerForInstagramService(ErrorCodes.NO_PROFILE_MAPPED_BUSINESS.value(), -1, ExternalAPIErrorCode.NO_PROFILE_MAPPED_BUSINESS.getDescription());
                if(Objects.nonNull(pm) && pm.getMarkInvalid() == 1) {
                    kafkaExternalService.markPageInvalid(SocialChannel.INSTAGRAM.getName(), socialPostPublishInfo.getExternalPageId());
                }
                failedPostAudit(socialPostPublishInfo, pm.getErrorMessage(),null, pm.getBucket());
                //failedPostAudit(socialPostPublishInfo, "No profile mapped to the business", null);
            } else if (Objects.nonNull(socialPostPublishInfo.getPublishDate()) && socialPostPublishInfo.getPublishDate().before(date)) {
                LOGGER.info("[IG retry] Failed to create container for post in 12 hour frame:{}", socialPostPublishInfo);
                PermissionMapping pm = errorHandlerForInstagramService(ErrorCodes.FAILED_TO_CREATE_CONTAINER.value(), -1, ExternalAPIErrorCode.FAILED_TO_CREATE_CONTAINER.getDescription());
                if(Objects.nonNull(pm) && pm.getMarkInvalid() == 1) {
                    kafkaExternalService.markPageInvalid(SocialChannel.INSTAGRAM.getName(), socialPostPublishInfo.getExternalPageId());
                }
                failedPostAudit(socialPostPublishInfo, pm.getErrorMessage(),null, pm.getBucket());
               // failedPostAudit(socialPostPublishInfo, "Failed to create container", null);
            } else {
                String accessToken = businessInstagramAccount.getPageAccessToken();
                String igAccountId = businessInstagramAccount.getInstagramAccountId();
                LOGGER.info("[IG retry] Retrying post:{}", socialPostPublishInfo);
                retryProcessingPost(socialPostPublishInfo,creationIds,accessToken,igAccountId);
            }
        }
    }

    @Override
    public void checkIgContainerStatus(IgContainerCheckDTO igContainerCheckDTO, boolean markFailed) throws BirdeyeSocialException {
        SocialPostPublishInfo publishInfo = socialPostPublishInfoRepository.findOne(igContainerCheckDTO.getPublishInfoId());
        if (Objects.isNull(publishInfo)) {
            LOGGER.info("No entry found in SocialPostPublishInfo for given id: {}", igContainerCheckDTO.getPublishInfoId());
            return;
        }
        if(markFailed) {
            BirdeyeSocialException birdeyeSocialException = new BirdeyeSocialException(ErrorCodes.INSTAGRAM_CONTAINER_RETRY_LIMIT_EXHAUST,
                    ErrorCodes.INSTAGRAM_CONTAINER_RETRY_LIMIT_EXHAUST.toString(), new HashMap<String, Object>());
            boolean isEligibleForRetry = commonService.retryPostIfEligible(birdeyeSocialException, publishInfo);
            if(!isEligibleForRetry) {
                PermissionMapping pm = errorHandlerForInstagramService(ErrorCodes.INSTAGRAM_CONTAINER_CREATION_ERROR.value(), null, null);
                failedPostAudit(publishInfo, pm.getErrorMessage(), ErrorCodes.INSTAGRAM_CONTAINER_CREATION_ERROR.value(), pm.getBucket());
            }
            return;
        }
        try {
            List<String> creationIds = igContainerCheckDTO.getContainerIds();
            if (creationIds.size() == 1) { //Non-carousel
                Map<String, Object> errorMap = instagramPostService.checkContainerStatusWithoutRetry(creationIds.get(0), igContainerCheckDTO.getAccessToken());

                if(Objects.nonNull(errorMap) && "PUBLISHED".equalsIgnoreCase(String.valueOf(errorMap.get("status_code")))) {
                    LOGGER.info("IG Post already published: {}", errorMap);
                    return;
                }
                if(Objects.nonNull(errorMap) && !"FINISHED".equalsIgnoreCase(String.valueOf(errorMap.get("status_code")))) {
                    LOGGER.error("[IG retry]Error creating container:: {}", errorMap);
                    BirdeyeSocialException birdeyeSocialException = new BirdeyeSocialException(ErrorCodes.INSTAGRAM_CONTAINER_CREATION_ERROR, errorMap.get("status").toString().trim(), errorMap);
                    boolean isEligibleForRetry = commonService.retryPostIfEligible(birdeyeSocialException, publishInfo);
                    if(!isEligibleForRetry) {
                        LOGGER.error("Cannot retry post");
                        throw  birdeyeSocialException;
                    }
                    return;
                }
                postMedia(publishInfo, igContainerCheckDTO.getIgAccountId(), igContainerCheckDTO.getAccessToken(), creationIds.get(0));
                BusinessInstagramAccount instagramAccount = instagramAccountRepository.findByInstagramAccountId(igContainerCheckDTO.getIgAccountId());
                if (Objects.isNull(instagramAccount)) {
                    LOGGER.info("IG account not found for accountId: {}", igContainerCheckDTO.getIgAccountId());
                    return;
                }
                additionalPostingSteps(publishInfo, instagramAccount, instagramAccount.getPageAccessToken(), instagramAccount.getInstagramAccountId(),
                        igContainerCheckDTO.getMedia(), igContainerCheckDTO.getIgPostMetadata(), igContainerCheckDTO.getIsStory());
            } else { //carousel
                for (String containerId : creationIds) {
                    Map<String, Object> errorMap = instagramPostService.checkContainerStatusWithoutRetry(containerId, igContainerCheckDTO.getAccessToken());

                    if(Objects.nonNull(errorMap) && "PUBLISHED".equalsIgnoreCase(String.valueOf(errorMap.get("status_code")))) {
                        LOGGER.info("IG Post already published: {}", errorMap);
                        return;
                    }
                    if(Objects.nonNull(errorMap) && !"FINISHED".equalsIgnoreCase(String.valueOf(errorMap.get("status_code")))) {
                        LOGGER.error("[IG retry]Error creating container:: {}", errorMap);
                        BirdeyeSocialException birdeyeSocialException = new BirdeyeSocialException(ErrorCodes.INSTAGRAM_CONTAINER_CREATION_ERROR, errorMap.get("status").toString().trim(), errorMap);
                        boolean isEligibleForRetry = commonService.retryPostIfEligible(birdeyeSocialException, publishInfo);
                        if(!isEligibleForRetry) {
                            LOGGER.error("Cannot retry post");
                            throw  birdeyeSocialException;
                        }
                        return;
                    }
                }
                String creationId = createContainer(igContainerCheckDTO.getIgAccountId(), igContainerCheckDTO.getAccessToken(), null, false, false, true, creationIds, igContainerCheckDTO.getText(), igContainerCheckDTO.getIgPostMetadata(), null);
                containerStatusCheckAsync(igContainerCheckDTO.getIgAccountId(), igContainerCheckDTO.getAccessToken(), igContainerCheckDTO.getPublishInfoId(),
                        igContainerCheckDTO.getMedia(), igContainerCheckDTO.getIsStory(), igContainerCheckDTO.getIgPostMetadata(), Collections.singletonList(creationId), igContainerCheckDTO.getText());
            }
        } catch (HttpStatusCodeException | TooManyRequestException ex) {
            handleHTTPException(publishInfo, ex);
        }
        catch (InstagramContainerException ex) {
            LOGGER.info("[social post] IG error in container status for business id: {}, message: {}", publishInfo.getBusinessId(), ex.getFailureReason());
            throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_CONTAINER_NOT_CREATED, ExternalAPIErrorCode.INSTAGRAM_CONTAINER_NOT_CREATED.getDescription());
        }
        catch (Exception e) {
            LOGGER.info("[social post] IG error for business id: {} : {}", publishInfo.getBusinessId(), e.getMessage());
            PermissionMapping pm = null;
            Integer subErrorCode = null;
            if(e instanceof BirdeyeSocialException) {
                BirdeyeSocialException birdeyeSocialException = (BirdeyeSocialException) e;
                try {
                    if(StringUtils.isNotEmpty(birdeyeSocialException.getMessage())) {
                        String[] messageArr = birdeyeSocialException.getMessage().split(" ");
                        subErrorCode = Integer.parseInt(messageArr[messageArr.length-1]);
                        pm = errorHandlerForInstagramService(null, subErrorCode, birdeyeSocialException.getMessage());
                    }
                } catch (Exception exception) {
                    LOGGER.info("Unable to parse exception message: {}", birdeyeSocialException.getMessage());
                }
            }

            if(Objects.nonNull(pm)) {
                failedPostAudit(publishInfo, pm.getErrorMessage(), subErrorCode, pm.getBucket());
            } else {
                failedPostAudit(publishInfo, e.getMessage(), subErrorCode, null);
            }
        }
    }

    private void handleHTTPException(SocialPostPublishInfo publishInfo, RuntimeException ex) {
        ObjectMapper mapper = new ObjectMapper();
        BirdeyeSocialException birdeyeSocialException;
        InstagramBaseResponse instagramBaseResponse = null;
        if (ex instanceof HttpStatusCodeException) {
            try {
                instagramBaseResponse =mapper.readValue(((HttpStatusCodeException) ex).getResponseBodyAsString(), InstagramBaseResponse.class);
            } catch (Exception e) {
                LOGGER.error("error parsing HttpStatusCodeException to instagramBaseResponse");
            }
            birdeyeSocialException = convertToBirdeyeException((HttpStatusCodeException) ex, instagramBaseResponse);
        } else {
            birdeyeSocialException = new BirdeyeSocialException(ErrorCodes.TOO_MANY_REQUESTS, ex.getMessage(), new HashMap<>());
        }

        boolean isEligibleForRetry = commonService.retryPostIfEligible(birdeyeSocialException, publishInfo);
        if(!isEligibleForRetry) {
            InstagramErrorResponse instagramErrorResponse = instagramBaseResponse != null ? instagramBaseResponse.getError() : new InstagramErrorResponse();
            LOGGER.info("[social post] IG error from graph api for business id:{} : {}", publishInfo.getBusinessId(),instagramErrorResponse);
            String errorMessage = null;
            if(Objects.nonNull(instagramErrorResponse.getError_user_msg())&&!instagramErrorResponse.getError_user_msg().isEmpty()) {
                errorMessage = instagramErrorResponse.getError_user_msg();
            }
            else {
                errorMessage = instagramErrorResponse.getMessage();
            }
            PermissionMapping pm = errorHandlerForInstagramService(instagramErrorResponse.getCode(), instagramErrorResponse.getError_subcode(), errorMessage);
            if(Objects.isNull(pm)) {
                failedPostAudit(publishInfo,null,instagramErrorResponse.getCode(), null);
            } else {
                failedPostAudit(publishInfo,pm.getErrorMessage(),instagramErrorResponse.getCode(), pm.getBucket());
            }
        }
    }

    @Override
    public List<SocialMentionData> searchMentionData(String search, Integer businessId, Long enterpriseId) {
        List<SocialMentionData> socialMentionDataList = new ArrayList<>();
        try {
            LOGGER.info("mention search username: {} and business: {}", search, businessId);
            List<SocialReservedAccounts> reservedAccounts = reservedAccountsRepo.findBySourceId(SocialChannel.INSTAGRAM.getId());
            String igAccountId = null;
            String accessToken = null;
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(reservedAccounts)) {
                SocialReservedAccounts socialReservedAccount = reservedAccounts.get(0);
                igAccountId = socialReservedAccount.getPageId();
                accessToken = socialReservedAccount.getAccessToken();
            }

            InstagramCompetitorProfile instagramCompetitorProfile = instagramService.getCompetitorProfileData(igAccountId, accessToken, search, false, null);
            if(Objects.nonNull(instagramCompetitorProfile) && Objects.nonNull(instagramCompetitorProfile.getBusiness_discovery())) {
                BusinessDiscovery businessDiscovery = instagramCompetitorProfile.getBusiness_discovery();
                SocialMentionData socialMentionData = new SocialMentionData();
                socialMentionData.setId(businessDiscovery.getId());
                socialMentionData.setName(businessDiscovery.getName());
                socialMentionData.setUserName(businessDiscovery.getUsername());
                socialMentionData.setProfilePictureUrl(businessDiscovery.getProfile_picture_url());
                socialMentionData.setFollowerCount(businessDiscovery.getFollowers_count());
                socialMentionData.setProfileUrl("https://instagram.com/" + businessDiscovery.getUsername());
                socialMentionDataList.add(socialMentionData);
            }
            return socialMentionDataList;
        } catch (Exception e) {
            LOGGER.info("exception occured while fetching details for username: {} and business: {}", search, businessId);
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }
}
