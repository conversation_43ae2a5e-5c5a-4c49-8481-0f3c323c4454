package com.birdeye.social.service.impl;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.PermissionMappingRepo;
import com.birdeye.social.entities.PermissionMapping;
import com.birdeye.social.model.PermissionMappingRequest;
import com.birdeye.social.model.SocialMessageModule;
import com.birdeye.social.service.IPermissionMappingService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("PermissionMappingService")
public class PermissionMappingMappingServiceImpl implements IPermissionMappingService {

    @Autowired
    private PermissionMappingRepo permissionMappingRepo;

    private static final Logger logger = LoggerFactory.getLogger(PermissionMappingMappingServiceImpl.class);


    @Override
    @Cacheable(value = "permissionMappingByPermissionCode", key = "#channel+'_'+#permissionCode", unless = "#result == null")
    public PermissionMapping getDataByChannelAndPermissionCode(String channel, Integer permissionCode) {
        if(Objects.isNull(permissionCode)) {
            return permissionMappingRepo.findByChannelAndPermissionCode(channel,Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
        }
        PermissionMapping permissionMapping = permissionMappingRepo.findByChannelAndPermissionCode(channel,permissionCode);
        if(permissionMapping != null) {
            return permissionMapping;
        }  else {
            return permissionMappingRepo.findByChannelAndErrorCode(channel, Constants.INTEGRATION);
        }
    }

    @Override
    @Cacheable(value = "permissionMappingByPermissionCodeAndModule", key = "#channel+'_'+#permissionCode+'_'+#module", unless = "#result == null")
    public PermissionMapping getDataByChannelAndPermissionCodeAndModule(String channel, Integer permissionCode, String module) {
        if(Objects.isNull(permissionCode)) {
            return permissionMappingRepo.findByChannelAndPermissionCode(channel,Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
        }
        List<PermissionMapping> permissionMappings = permissionMappingRepo.findByChannelAndPermissionCodeAndModule(channel,permissionCode,module);
        if(CollectionUtils.isNotEmpty(permissionMappings)) {
            return permissionMappings.get(0);
        }  else {
            return permissionMappingRepo.findByChannelAndErrorCode(channel, Constants.INTEGRATION);
        }
    }

    @Override
    @Cacheable(value = "permissioMappingByChannel", key = "#channel", unless = "#result == null")
    public List<PermissionMapping> getDataByChannelAndPermissionNameNotNull(String channel) {
        return permissionMappingRepo.findByChannelAndPermissionNameIsNotNull(channel);
    }

    @Override
    @Cacheable(value = "permissionMappingByErrorCode", key = "#channel+'_'+#errorCode", unless = "#result == null")
    public PermissionMapping getDataByChannelAndErrorCode(String channel, String errorCode) {
        List<PermissionMapping> permissionMappings = permissionMappingRepo.findAllByChannelAndErrorCode(channel,errorCode);
        if(CollectionUtils.isNotEmpty(permissionMappings)) {
            return permissionMappings.get(0);
        }  else {
            return permissionMappingRepo.findByChannelAndErrorCode(channel, Constants.INTEGRATION);
        }
    }

    @Override
    @Cacheable(value = "permissionMappingByErrorCode", key = "#channel+'_'+#code", unless = "#result == null")
    public List<PermissionMapping> getListOfDataByChannelAndErrorCode(String channel, String code) {
        return permissionMappingRepo.findAllByChannelAndErrorCode(channel,code);
    }

    @Override
    @Cacheable(value = "permissionMappingByErrorCode", key = "#channel+'_'+#module+'_'+#errorCode", unless = "#result == null")
    public PermissionMapping getDataByChannelAndModuleAndErrorCode(String channel, String module, String errorCode) {
        List<PermissionMapping> permissionMappings = permissionMappingRepo.findAllByChannelAndModuleAndErrorCode(channel,module,errorCode);
        if(CollectionUtils.isNotEmpty(permissionMappings)) {
            return permissionMappings.get(0);
        }  else {
            return permissionMappingRepo.findByChannelAndErrorCode(channel, Constants.INTEGRATION);
        }
    }

    public  List<PermissionMapping> getDataByChannelandHttpResponseAndErrorCodeAndErrorSubCode(String channel, Integer httpResponse, Integer errorCode, Integer errorSubCode) {
        return permissionMappingRepo.findAllByChannelAndHttpResAndErrorCodeAndErrorSubCode(channel, httpResponse, errorCode,errorSubCode);
    }

    public  List<PermissionMapping> getDataByChannelandHttpResponseAndErrorCode(String channel, Integer httpResponse, Integer errorCode) {
        return permissionMappingRepo.findAllByChannelAndHttpResAndErrorCode(channel, httpResponse, errorCode);
    }

    public  List<PermissionMapping> getDataByChannelandHttpResponse(String channel, Integer httpResponse) {
        return permissionMappingRepo.findAllByChannelAndHttpRes(channel, httpResponse);
    }

    @Override
    @Cacheable(value = "permissionMappingByErrorCode", key = "#channel+'_'+#errorCode+'_'+#errorSubCode", unless = "#result == null")
    public List<PermissionMapping> getDataByChannelAndParentErrorCodeAndPermissionCode(String channel, Integer errorCode, Integer errorSubCode){
        List<PermissionMapping> permissionMapping = permissionMappingRepo.getDataByChannelAndParentErrorCodeAndPermissionCode(channel,errorCode,errorSubCode);
        logger.info("Permission mapping result for channel :{} error code : {} ,errorSubCode : {}, permission mapping : {}"
                ,channel,errorCode,errorSubCode, CollectionUtils.isEmpty(permissionMapping) ? null : permissionMapping);
        if(CollectionUtils.isNotEmpty(permissionMapping)) {
            return permissionMapping;
        }
        return Collections.singletonList(permissionMappingRepo.findByChannelAndPermissionCode(channel, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR));
    }

    @Override
    @Cacheable(value = "permissionMappingByErrorCode", key = "#channel+'_'+#errorCode+'_'+#errorSubCode", unless = "#result == null")
    public List<PermissionMapping> getDataByChannelAndParentErrorCodeAndPermissionCodeNull(String channel, Integer errorCode, Integer errorSubCode){
        List<PermissionMapping> permissionMapping = permissionMappingRepo.getDataByChannelAndParentErrorCodeAndPermissionCodeNull(channel,errorCode);
        if(permissionMapping != null) {
            return permissionMapping;
        }
        return Collections.singletonList(permissionMappingRepo.findByChannelAndPermissionCode(channel, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR));
    }

    @Override
    public List<PermissionMapping> getListOfDataByChannelAndPermissionCode(String channel, Integer errorSubCode) {
        List<PermissionMapping> permissionMapping = permissionMappingRepo.getDataByChannelAndPermissionCode(channel, errorSubCode);
        if(permissionMapping != null) {
            return permissionMapping;
        }
        return Collections.singletonList(permissionMappingRepo.findByChannelAndPermissionCode(channel, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR));
    }

    @Override
    public List<PermissionMapping> getDataByChannelAndHttpErrorResponse(String channel,Integer errorResponse) {
        return permissionMappingRepo.findByChannelAndHttpErrorResponse(channel,errorResponse);
    }

    @Override
    public PermissionMapping getDataByChannelAndActualErrorText(String channel, String message) {
        return permissionMappingRepo.findByChannelAndErrorActualMessage(channel, message);
    }

    @Override
    public PermissionMapping getDataByChannelAndParentCodeAndErrorCode(String channel, Integer parentCode, String message) {
        return permissionMappingRepo.findByChannelAndErrorParentCodeAndErrorCode(channel, parentCode, message);
    }

    @Override
    @CacheEvict(value = { "permissionMappingByPermissionCode", "permissionMappingByErrorCode" , "permissioMappingByChannel" }, allEntries = true)
    public void manualInsertUpdateDelete(PermissionMappingRequest request, String operation) {

        if(Objects.isNull(request)|| CollectionUtils.isEmpty(request.getPermission())){
            logger.info("Nothing to update in permission_mapping table :{}",request);
            return;
        }
        switch(operation.toLowerCase()){
            case "update":
                performUpdateOperation(request);
                break;
            case "insert":
                performInsertOperation(request);
                break;
            case "delete":
                performDeleteOperation(request);
                break;
        }

    }

    @Override
    @Cacheable(value = "permissionMappingByErrorCode", key = "#channel+'_'+#gmbMessaging+'_'+#googleAgentStatus", unless = "#result == null")
    public String getDataByChannelAndModuleAndPermissionName(String channel, SocialMessageModule gmbMessaging, String googleAgentStatus) {
        List<PermissionMapping> permissionMapping =  permissionMappingRepo.findByChannelAndModuleAndPermissionName(channel,gmbMessaging.name(),googleAgentStatus);
        if(CollectionUtils.isEmpty(permissionMapping)){
            return "Unknown Error Occurred";
        }
        return permissionMapping.get(0).getErrorMessage();
    }

    @Override
    @Cacheable(value = "permissionMappingByErrorCode", key = "#channel+'_'+#gmbMessaging+'_'+#googleAgentStatus", unless = "#result == null")
    public String getDataByChannelAndModuleAndPermissionCode(String channel, SocialMessageModule gmbMessaging, Integer permissionCode) {
        List<PermissionMapping> permissionMapping =  permissionMappingRepo.findByChannelAndModuleAndPermissionCode(channel,gmbMessaging.name(),permissionCode);
        if(CollectionUtils.isEmpty(permissionMapping)){
            return "Unknown Error Occurred";
        }
        return permissionMapping.get(0).getErrorMessage();
    }

    @Override
    @Cacheable(value = "permissionMappingByErrorCode" , key = "#channel+'_'+#module",unless = "#result == null")
    public List<PermissionMapping> getDataByChannelAndModuleAndPermissionNameNotNull(String channel, String module) {
        List<PermissionMapping> permissionMapping =  permissionMappingRepo.findByChannelAndModuleAndPermissionNameNotNull(channel,module.toLowerCase());
        if(CollectionUtils.isEmpty(permissionMapping)){
            return new ArrayList<>();
        }
        return permissionMapping;
    }

    private void performUpdateOperation(PermissionMappingRequest request) {
        request.getPermission().forEach(data -> {
            if(Objects.isNull(data.getId())) {
                logger.error("Update operation cannot be performed on :{}",data.getId());
                return;
            }
            PermissionMapping pm = permissionMappingRepo.findOne(data.getId());

            if(Objects.isNull(pm)) {
                logger.error("Update operation cannot be performed on :{}",data.getId());
                return;
            }

            if(Objects.nonNull(data.getErrorMessage())) {
                pm.setErrorMessage(data.getErrorMessage());
            }

            if(Objects.nonNull(data.getBucket())) {
                pm.setBucket(data.getBucket());
            }

            if(Objects.nonNull(data.getMarkInvalid())) {
                pm.setMarkInvalid(data.getMarkInvalid());
            }

            if(Objects.nonNull(data.getModule())) {
                pm.setModule(data.getModule());
            }

            permissionMappingRepo.saveAndFlush(pm);
        });
    }

    private void performInsertOperation(PermissionMappingRequest request) {

        List<PermissionMapping> permissionMappings =
                request.getPermission().stream().filter(data->StringUtils.isNotEmpty(data.getErrorMessage()))
                        .map(temp -> {
                                PermissionMapping permissionMapping = new PermissionMapping();
                                permissionMapping.setChannel(temp.getChannel());
                                permissionMapping.setErrorMessage(temp.getErrorMessage());
                                permissionMapping.seterrorActualMessagee(temp.getErrorActualMessage());
                                permissionMapping.setPermissionCode(temp.getPermissionCode());
                                permissionMapping.setErrorCode(temp.getErrorCode());
                                permissionMapping.seterrorParentCode(temp.getErrorParentCode());
                                permissionMapping.sethttpResponse(temp.getHttpResponse());
                                permissionMapping.setBucket(temp.getBucket());
                                permissionMapping.setMarkInvalid(temp.getMarkInvalid());
                                permissionMapping.setModule(temp.getModule());
                                return permissionMapping;
                        })
                        .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(permissionMappings)) {
            permissionMappingRepo.save(permissionMappings);
        }
        else{
            logger.info("No insert operation performed in permission_mapping table");
        }

    }
    private void performDeleteOperation(PermissionMappingRequest request) {

        List<PermissionMapping> permissionMappings =
                request.getPermission().stream().filter(data->Objects.nonNull(data.getId()))
                        .map(temp -> {
                            PermissionMapping permissionMapping = new PermissionMapping();
                            permissionMapping.setId(temp.getId());
                            return permissionMapping;
                        })
                        .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(permissionMappings)){
            permissionMappingRepo.deleteInBatch(permissionMappings);
        }
        else{
            logger.info("No delete operation performed in permission_mapping table");
        }


    }

    @Override
    @Cacheable(value = "permissionMappingByErrorCode", key = "#channel+'_'+#errorCode+'_'+#errorSubCode+'_'+#module", unless = "#result == null")
    public List<PermissionMapping> getDataByChannelAndParentErrorCodeAndPermissionCodeAndModule(String channel, Integer errorCode, Integer errorSubCode, SocialMessageModule module) {
        List<PermissionMapping> permissionMapping = permissionMappingRepo.findByChannelAndErrorParentCodeAndPermissionCodeAndModule(channel,errorCode,errorSubCode,module.name());
        logger.info("Permission mapping result for channel :{} error code : {} ,errorSubCode : {}, permission mapping : {}"
                ,channel,errorCode,errorSubCode, CollectionUtils.isEmpty(permissionMapping) ? null : permissionMapping);
        if(CollectionUtils.isNotEmpty(permissionMapping)) {
            return permissionMapping;
        }
        return Collections.singletonList(permissionMappingRepo.findByChannelAndPermissionCode(channel, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR));
    }

    @Override
    public List<PermissionMapping> getDataByChannelAndPermissionCodeInAndModule(String channel, Collection<Integer> permissionCode, String module) {
        List<PermissionMapping> permissionMappings = permissionMappingRepo.findByChannelAndPermissionCodeInAndModule(channel,permissionCode,module);
        if(CollectionUtils.isEmpty(permissionMappings)) {
            return new ArrayList<>();
        }  else {
            return permissionMappings;
        }
    }

	@Override
	@Cacheable(value = "permissionMappingByChannel", key = "#channel", unless = "#result == null")
	public List<PermissionMapping> getDataByChannel(String channel) {
		return permissionMappingRepo.findByChannel(channel);
	}
    @Override
    @Cacheable(value = "permissionMappingByErrorCode", key = "#permissionName+'_'+#value+'_'+#module", unless = "#result == null")
    public PermissionMapping getErrorMessageAndCode(String permissionName,Integer value, String module) {
        List<PermissionMapping> permissionMappingList = permissionMappingRepo.findByPermissionNameAndErrorParentCodeAndModule(permissionName,value,module);
        if(CollectionUtils.isNotEmpty(permissionMappingList)){
            return permissionMappingList.get(0);
        }
        return permissionMappingRepo.findByChannelAndPermissionCode(SocialChannel.LINKEDIN.getName(), Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
    }

    @Override
    @Cacheable(value = "permissionMappingByErrorCode", key = "#channel+'_'+#module+'_'+#errorCode", unless = "#result == null")
    public PermissionMapping getPostingErrorByChannelAndModuleAndErrorCode(String channel, String module, String errorCode) {
        List<PermissionMapping> permissionMappings = permissionMappingRepo.findAllByChannelAndModuleAndErrorCode(channel, module, errorCode);
        if (CollectionUtils.isNotEmpty(permissionMappings)) {
            return permissionMappings.get(0);
        }
        return permissionMappingRepo.findByChannelAndErrorCode(channel, module.toLowerCase());
    }

    @Override
    @Cacheable(value = "permissionMappingByPermissionCodeAndModule", key = "#channel+'_'+#permissionCode+'_'+#module", unless = "#result == null")
    public PermissionMapping getPostingErrorByChannelAndModuleAndPermissionCode(String channel, String module, Integer permissionCode) {
        List<PermissionMapping> permissionMappings = permissionMappingRepo.findByChannelAndPermissionCodeAndModule(channel, permissionCode, module);
        if (CollectionUtils.isNotEmpty(permissionMappings)) {
            return permissionMappings.get(0);
        }
        logger.info("PermissionMappingMappingServiceImpl#getPostingErrorByChannelAndModuleAndPermissionCode(): Error message is not configured in DB for the returned response code from Api side, setting generic error message for channel : {}, module : {}", channel, module.toLowerCase());
        return permissionMappingRepo.findByChannelAndErrorCode(channel, module.toLowerCase());
    }
}
