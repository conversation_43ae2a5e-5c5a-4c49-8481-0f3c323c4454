/**
 *
 *
 */
package com.birdeye.social.service;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.SocialSetupAuditEnum;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.platform.dao.BusinessGMBLocationRepository;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.platform.entities.BusinessGMBLocation;

/**
 * <AUTHOR>
 *
 */
@Service("googleAccessTokenCache")
public class GoogleAccessTokenCacheImpl implements GoogleAccessTokenCache {
	
	@Autowired
	private BusinessRepository				businessRepository;
	
	@Autowired
	private GoogleAuthenticationService		googleAuthenticationService;
	
	@Autowired
	private BusinessGMBLocationRepository	businessGMBRepo;
	
	@Autowired
	private GoogleMyBusinessPageService gmbPageService;

	@Autowired
	private GoogleSocialAccountService gmbBusinessService;

	@Autowired
	private IBrokenIntegrationService brokenIntegrationService;

	@Autowired
	private BusinessGMBLocationRawRepository	socialGMBRepo;

	@Autowired
	private CommonService commonService;
	
	private static final Logger				logger	= LoggerFactory.getLogger(GoogleAccessTokenCacheImpl.class);
	
	/**
	 * Generate google access token by GMBLocation and refresh token
	 *
	 * @param gmbLocation
	 * @param locationId
	 * @return
	 * @throws Exception
	 */
	@Cacheable(value = "accessTokenCache", key = "#gmbLocation.locationId", unless = "#result == null")
	public String getGoogleAccessToken(BusinessGMBLocation gmbLocation) {
		logger.info("[GMB Review] Fetching GoogleAccessToken for GMB location with locationId: {}", gmbLocation.getLocationId());
		try {
			Business business = businessRepository.findById(gmbLocation.getBusinessId());
			if (business == null) {
				logger.info("Error while fetching GoogleAccessToken for location id {} as business is null", gmbLocation.getLocationId());
				return null;
			}
			return generateGoogleAccessToken(gmbLocation);
		} catch (Exception e) {
			logger.error("Exception while fetching GoogleAccessToken for location id {} is {}", gmbLocation.getLocationId(), e);
		}
		return null;
	}

	@Cacheable(value = "accessTokenCache", key = "#gmbRawPage.locationId", unless = "#result == null")
	public String getGoogleAccessToken(BusinessGoogleMyBusinessLocation gmbRawPage) {
		logger.info("[GMB Review] Fetching GoogleAccessToken for GMB location with locationId: {}", gmbRawPage.getLocationId());
		try {
			return generateGoogleAccessToken(gmbRawPage);
		} catch (Exception e) {
			logger.error("Exception while fetching GoogleAccessToken for location id {} is {}", gmbRawPage.getLocationId(), e);
		}
		return null;
	}

	@Cacheable(value = "getGoogleAccessTokenCache", key = "#gmbRawPage.refreshTokenId.toString()", unless = "#result == null")
	public String getGoogleAccessTokenByRefreshToken(BusinessGoogleMyBusinessLocation gmbRawPage) {
		logger.info("[GMB Review] Fetching GoogleAccessToken for GMB location with locationId: {}", gmbRawPage.getLocationId());
		try {
			return generateGoogleAccessToken(gmbRawPage);
		} catch (Exception e) {
			logger.error("Exception while fetching GoogleAccessToken for location id {} is {}", gmbRawPage.getLocationId(), e);
		}
		return null;
	}

	/**
	 * Generate google access token
	 *
	 * @param appCreds
	 * @param refreshToken
	 * @param locationId 
	 * @return
	 * @throws IOException 
	 */
	private String generateGoogleAccessToken(BusinessGMBLocation gmbLocation) {
		String accessToken = null;
		try {
			accessToken = googleAuthenticationService.getGoogleAccessToken(gmbLocation.getRefreshTokenId());
		} catch (BirdeyeSocialException e) {
			if (e.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value()) {
				logger.error("Marking Location {} as invalid", gmbLocation.getLocationId());
				gmbLocation.setIsValid(0);
				gmbLocation.setUpdatedAt(new Date());
				gmbLocation.setErrorLog(e.getMessage());
				businessGMBRepo.saveAndFlush(gmbLocation);
				gmbPageService.updateGMBLocationIsValidStatus(gmbLocation.getLocationId(), 0);
			}
		}
		return accessToken;
	}

	private String generateGoogleAccessToken(BusinessGoogleMyBusinessLocation gmbRawPage) {
		String accessToken = null;
		try {
			accessToken = googleAuthenticationService.getGoogleAccessToken(gmbRawPage.getRefreshTokenId());
		} catch (BirdeyeSocialException e) {
			if (e.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value()) {
				logger.error("Marking Location {} as invalid", gmbRawPage.getLocationId());
				gmbRawPage.setIsValid(0);
				gmbRawPage.setUpdatedAt(new Date());
				socialGMBRepo.saveAndFlush(gmbRawPage);
				brokenIntegrationService.pushValidIntegrationStatus(gmbRawPage.getEnterpriseId(), SocialChannel.GMB.getName(), gmbRawPage.getId(),0,gmbRawPage.getLocationId());
				commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(gmbRawPage),
						gmbRawPage.getUserId(), gmbRawPage.getBusinessId(), gmbRawPage.getEnterpriseId());
			}
		}
		return accessToken;
	}

	/**
	 * Evict google access token by GMBLocationId from Redis cache
	 * @param gmbLocation
	 */
	@CacheEvict(value = "accessTokenCache", key = "#gmbLocation == null ? 'NULL' : #gmbLocation.locationId")
	public void evictGoogleAccessTokenCache(BusinessGoogleMyBusinessLocation gmbLocation) {
		logger.info("BusinessGoogleMyBusinessLocation evictGoogleAccessTokenCache for gmb location id {}", gmbLocation.getLocationId());
	}
	
	/**
	 * Update google access token in Redis cache in case of Reconnect
	 * @param gmbLocation
	 * @param accessToken
	 */
	@Cacheable(value = "accessTokenCache", key = "#gmbLocation == null ? 'NULL' : #gmbLocation.locationId", unless = "#result == null")
	public String updateGoogleAccessToken(BusinessGoogleMyBusinessLocation gmbLocation, String accessToken) {
		logger.info("BusinessGoogleMyBusinessLocation updateGoogleAccessToken for gmb location id {}", gmbLocation.getLocationId());
		return accessToken;
	}
	/**
	 * PlaceId cache for get Review by Id
	 * @param locationId
	 */
	@Cacheable(value = "placeIdCache", key = "#locationId", unless = "#result == null")
	public String getPlaceIdForLocationId(String locationId) {
		logger.info("Place Id from BusinessGoogleMyBusinessLocation :  getPlaceIdForLocationId for gmb location id {}", locationId);
		return socialGMBRepo.findPlaceIdByLocationIdAndIsValid(locationId, 1);
	}
}
