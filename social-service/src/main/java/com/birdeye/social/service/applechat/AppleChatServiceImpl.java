package com.birdeye.social.service.applechat;

import com.birdeye.social.apple.AppleSendRequest;
import com.birdeye.social.apple.AttachmentPreDownloadRequest;
import com.birdeye.social.apple.AttachmentPreUploadRequest;
import com.birdeye.social.apple.AttachmentPreUploadResponse;
import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.applechat.AppleBrandAccountPageRepository;
import com.birdeye.social.dao.applechat.BusinessAppleAccountRepository;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.BusinessLocationLiteDTO;
import com.birdeye.social.dto.BusinessLocationLiteEntity;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.model.notification.SocialNotificationAudit;
import com.birdeye.social.entities.applechat.AppleBrandAccountPage;
import com.birdeye.social.entities.applechat.BusinessAppleAccounts;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.request.business.BusinessLiteRequest;
import com.birdeye.social.external.service.AppleExternalService;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.service.SocialAPILogService;
import com.birdeye.social.service.SocialErrorMessagePageService;
import com.birdeye.social.sro.AppleAddLocationMappingRequest;
import com.birdeye.social.sro.AppleDisconnectRequest;
import com.birdeye.social.sro.AppleLocationPageMappingRequest;
import com.birdeye.social.sro.ChannelPageInfo;
import com.birdeye.social.sro.LocationMappingRequest;
import com.birdeye.social.sro.LocationPageMapping;
import com.birdeye.social.sro.LocationPageMappingRequest;
import com.birdeye.social.sro.applechat.*;
import com.birdeye.social.utils.*;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import static java.util.Comparator.nullsFirst;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.birdeye.social.sro.applechat.AppleConnectPageRequest;
import com.birdeye.social.sro.applechat.ConnectPageResponse;
import com.birdeye.social.utils.BusinessUtilsService;
import com.birdeye.social.utils.StringUtils;

@Service("appleChatService")
public class AppleChatServiceImpl implements AppleChatService {

	private static final Logger LOGGER = LoggerFactory.getLogger(AppleChatServiceImpl.class);

	@Autowired
	private BusinessAppleAccountRepository appleAccountRepository;

	@Autowired
	private AppleBrandAccountPageRepository applePageRepository;

	@Autowired
	private IBusinessCoreService businessCoreService;

	@Autowired
	private BusinessUtilsService businessUtilService;

	@Autowired
	private SocialAPILogService socialAPILogService;
	
	@Autowired
	private AppleExternalService appleExternalService;

	@Autowired
	private IBusinessGetPageService businessGetPageService;

	@Autowired
	private IRedisLockService redisService;

	@Autowired
	private SocialErrorMessagePageService socialErrorMessageService;

	@Autowired
	private KafkaProducerService kafkaProducerService;
	
	@Autowired
	@Qualifier("socialTransactionManager")
	private PlatformTransactionManager transactionManager;

	private TransactionTemplate transactionTemplate;

	@Override
	public ChannelPageInfo connectPage(AppleConnectPageRequest request) {
		LOGGER.info("[AppleChat] Connect Request received for enterprise {}", request.getEnterpriseId());

		ChannelPageInfo channelAccountInfo = new ChannelPageInfo();
		long enterpriseId = request.getEnterpriseId();
		if (appleAccountRepository.existsByBrandId(request.getBrandBusinessId())) {
			throw new BirdeyeSocialException(ErrorCodes.APPLE_MESSAGES_BRAND_ALREADY_CREATED,socialErrorMessageService.getMessage(SocialErrorMessagesEnum.BRAND_ALREADY_EXISTS.name(),SocialChannel.APPLE.getName()));
		}
		String key = SocialChannel.APPLE.getName().concat(String.valueOf(request.getEnterpriseId()));
		boolean lock = redisService.tryToAcquireLock(key);

		if (lock) {
			LOGGER.info("[AppleChat] Connect Request  enterprise {}", enterpriseId);
			BusinessGetPageRequest gmbRequest = createBusinessGetPagesRequest(request);
			try {

				// gmbRequest.setBirdeyeUserId(birdeyeUserId);
				transactionTemplate = new TransactionTemplate(transactionManager);

				transactionTemplate.execute(new TransactionCallbackWithoutResult() {
					@Override
					protected void doInTransactionWithoutResult(TransactionStatus status) {

						BusinessAppleAccounts businessAppleAccounts = saveBusinessAccountData(request);
						// we can directly set businessId for SMB by fetching businessID and business
						// type from platform
						List<AppleBrandAccountPage> appleBrandAccountPages = null;
						if (request.getIsSMB() == true) {
							LOGGER.info("[AppleChat] Connect Request  enterprise {} and request {} is SMB",
									enterpriseId, gmbRequest.getId());

							appleBrandAccountPages = Collections.singletonList(
									saveBusinessLocationData(businessAppleAccounts, request.getBusinessId()));
						}

						Map<String, List<ChannelAccountInfo>> accountMap = new HashMap<>();

						accountMap.put(SocialChannel.APPLE.getName(),
								prepareChannelAccountInfo(appleBrandAccountPages));
						channelAccountInfo.setPageTypes(accountMap);

						gmbRequest.setStatus(Status.COMPLETE.getName());

					}

				});

			} catch (Exception e) {
				LOGGER.error("[Redis] (Apple) Lock released for business {}, error", enterpriseId, e);
				/*
				 * gmbRequest = Objects.isNull(gmbRequest) ?
				 * businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(
				 * enterpriseId, SocialChannel.APPLE.getName(), Constants.CONNECT) : gmbRequest;
				 */
				if (gmbRequest != null && gmbRequest.getStatus().equalsIgnoreCase(Status.INITIAL.getName())) {
					gmbRequest.setStatus(Status.CANCEL.getName());
					if (e.getMessage() != null) {
						gmbRequest.setErrorLog(e.getMessage().substring(0, Math.min(e.getMessage().length(), 4000)));

					}

				}
			} finally {
				redisService.release(key);
				businessGetPageService.saveAndFlush(gmbRequest);
			}
		} else {
			LOGGER.info("Apple Connect: Lock is already acquired for business {}", enterpriseId);
			throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS,
					ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
		}
		return channelAccountInfo;

	}

	private BusinessGetPageRequest createBusinessGetPagesRequest(AppleConnectPageRequest request) {
		BusinessGetPageRequest gmbRequest = new BusinessGetPageRequest();
		gmbRequest.setChannel(SocialChannel.APPLE.getName());
		gmbRequest.setEnterpriseId(request.getEnterpriseId());
		gmbRequest.setStatus(Status.INITIAL.getName());
		gmbRequest.setPageCount(0);
		gmbRequest.setRequestType(Constants.CONNECT);
		return businessGetPageService.saveAndFlush(gmbRequest);
	}

	private List<ChannelAccountInfo> prepareChannelAccountInfo(List<AppleBrandAccountPage> appleLocations) {
		List<ChannelAccountInfo> channelAccountInfos = new ArrayList<>();
		if (CollectionUtils.isEmpty(appleLocations)) {
			return channelAccountInfos;
		}
		appleLocations.forEach(appleLocation -> {
			ChannelAccountInfo channelAccountInfo = new ChannelAccountInfo();
			channelAccountInfo.setPageName(appleLocation.getLocationName());
			channelAccountInfo.setAddress(appleLocation.getSingleLineAddress());
			channelAccountInfo.setId(appleLocation.getIntentId());
			channelAccountInfo.setLink(appleLocation.getMapLink());
			Validity validity = fetchValidityAndErrorMessage(appleLocation);
			channelAccountInfo.setValidType(validity.getValidType());
			channelAccountInfo.setErrorCode(validity.getErrorCode());
			channelAccountInfo.setErrorMessage(validity.getErrorMessage());
			// channelAccountInfo.setDisabled((appleLocation.getIsSelected() != null &&
			// appleLocation.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
			channelAccountInfos.add(channelAccountInfo);
		});
		return channelAccountInfos;
	}

	@Override
	public ConnectPageResponse getPages(Long enterpriseId,String type) {
		LOGGER.info("[AppleChat] Request received for enterprise {}", enterpriseId);
		try {
			List<AppleBrandAccountPage> brandAccountPages;
			if(type.equalsIgnoreCase(PageConnectionStatus.CONNECTED.getName())){
				brandAccountPages = applePageRepository.findAllByEnterpriseIdAndIsValid(enterpriseId,1);
			}else if(type.equalsIgnoreCase(PageConnectionStatus.DISCONNECTED.getName())){
				brandAccountPages = applePageRepository.findAllByEnterpriseIdAndIsValid(enterpriseId,0);
			}else{
				brandAccountPages = applePageRepository.findAllByEnterpriseId(enterpriseId);
			}
			if (CollectionUtils.isEmpty(brandAccountPages)) {
				LOGGER.info("[AppleChat] No page found for the enterprise {}",enterpriseId);
				return null;
			}
			LOGGER.info("[AppleChat] Size of pages {} for enterprise {}",brandAccountPages.size(),enterpriseId);
			ConnectPageResponse connectPageResponse = new ConnectPageResponse();
			connectPageResponse.setPages(brandAccountPages.stream().map(this::prepareChannelAccountInfo).collect(Collectors.toList()));
			return connectPageResponse;
		} catch (Exception e) {
			LOGGER.info("[AppleChat] Request for enterprise {} get pages failed:{}", enterpriseId, e.toString());
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST,String.format("Unable to process request for the enterprise id %s",enterpriseId));
		}
	}

	@Override
	@Transactional
	public void removePage(List<LocationPageMappingRequest> removeRequest, boolean isSMB, long enterpriseId) {
		try {
			List<Integer> businessIds = removeRequest.stream().map(LocationPageMappingRequest::getLocationId).collect(Collectors.toList());
			LOGGER.info("Remove Pages for enterprise id {} and business id {}",enterpriseId,businessIds);
			if(CollectionUtils.isEmpty(businessIds)) {
				LOGGER.info("No business id in input");
				return;
			}
			if (isSMB) {
				applePageRepository.removeAllByBusinessId(businessIds);
				disconnectAppleAccount(enterpriseId);
			} else {
				LOGGER.info("Delete page for enterprise id : {}",enterpriseId);
				applePageRepository.removeAllByBusinessId(businessIds);
			}
		} catch (Exception e) {
			LOGGER.info("[AppleChat] Request for enterprise {} remove pages failed:", e.toString());
		}
	}

	private void disconnectAppleAccount(long enterpriseId) {
		appleAccountRepository.deleteByEnterpriseId(enterpriseId);
	}

	private ChannelAccountInfo prepareChannelAccountInfo(AppleBrandAccountPage data) {
		ChannelAccountInfo accountInfo = new ChannelAccountInfo();
		accountInfo.setPageName(data.getLocationName());
		accountInfo.setLink(data.getMapLink());
		accountInfo.setId(data.getIntentId());
		accountInfo.setBusinessId(data.getBusinessId());
		Validity validity = fetchValidityAndErrorMessage(data);
		accountInfo.setValidType(validity.getValidType());
		accountInfo.setErrorCode(validity.getErrorCode());
		accountInfo.setErrorMessage(validity.getErrorMessage());
		accountInfo.setAddress(data.getSingleLineAddress());
		return accountInfo;
	}

	private BusinessAppleAccounts saveBusinessAccountData(AppleConnectPageRequest data) {
		BusinessAppleAccounts businessAppleAccounts = new BusinessAppleAccounts();
		businessAppleAccounts.setBrandId(data.getBrandBusinessId());
		businessAppleAccounts.setBrandAccountName(data.getBrandName());
		businessAppleAccounts.setEnterpriseId(data.getEnterpriseId());
		return appleAccountRepository.save(businessAppleAccounts);
	}

	private AppleBrandAccountPage saveBusinessLocationData(BusinessAppleAccounts data, Integer businessId) {
		BusinessLiteDTO business = businessCoreService.getBusinessLite(businessId, true);

		AppleBrandAccountPage appleBrandAccountPage = new AppleBrandAccountPage();
		appleBrandAccountPage.setEnterpriseId(data.getEnterpriseId());
		appleBrandAccountPage.setLocationName(
				business.getBusinessAlias() != null ? business.getBusinessAlias() : business.getBusinessName());
		appleBrandAccountPage.setSingleLineAddress(prepareBusinessAddress(business.getLocation()));
		appleBrandAccountPage.setIntentId(
				business.getBusinessAlias() != null ? business.getBusinessAlias() : business.getBusinessName());
		appleBrandAccountPage.setIsValid(1);
		appleBrandAccountPage.setBusinessId(businessId);

		return applePageRepository.save(appleBrandAccountPage);

	}

	@Transactional
	@Override
	public void removeAccount(AppleDisconnectRequest appleDisconnectRequest) {
		LOGGER.info("[AppleChat] Remove Request for enterprise {} remove pages  :", appleDisconnectRequest);
		try {
			applePageRepository.deleteByEnterpriseId(appleDisconnectRequest.getEnterpriseId());
			disconnectAppleAccount(appleDisconnectRequest.getEnterpriseId());
		} catch (Exception e) {
			LOGGER.info("[AppleChat] Request for enterprise {} remove pages failed:", e.toString());
		}

	}

	@Override
	public void removePageMappings(AppleLocationPageMappingRequest appleMappingRemoveRequest) {
		try {
			List<AppleBrandAccountPage> appleBrandAccountPageList = applePageRepository
					.findByEnterpriseIdAndSingleLineAddressIn(appleMappingRemoveRequest.getBusinessId(),
							appleMappingRemoveRequest.getPageIdList());
			appleBrandAccountPageList.forEach(appleLocation -> appleLocation.setBusinessId(null));
			applePageRepository.save(appleBrandAccountPageList);
		} catch (Exception e) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Remove Mapping error");
		}

	}

	@Override
	public AutoSuggesterPagesResponse findPagesWithMappingStatus(Long enterpriseId) {
		AutoSuggesterPagesResponse autoSuggesterPagesResponse = new AutoSuggesterPagesResponse();
		List<SocialPageListInfo> pages = new ArrayList<>();
		List<SocialPageListInfo> unmappedLocations = findPagesSocialList(enterpriseId);
		if (CollectionUtils.isEmpty(pages)) {
			pages = Collections.emptyList();
			autoSuggesterPagesResponse.setPages(pages);
			return autoSuggesterPagesResponse;
		}
		autoSuggesterPagesResponse.setPages(unmappedLocations);
		return autoSuggesterPagesResponse;
	}

	@Override
	public List<SocialPageListInfo> findPagesSocialList(Long enterpriseId) {
		List<AppleBrandAccountPage> appleBusinessLocations = applePageRepository.findByEnterpriseId(enterpriseId);
		List<SocialPageListInfo> unmappedLocations = appleBusinessLocations.stream()
				.map(appleLocation -> convertAppleLocationToLocationPageListInfo(appleLocation))
				.collect(Collectors.toList());

		unmappedLocations
				.sort(Comparator.comparing(SocialPageListInfo::getPageName, nullsFirst(Comparator.naturalOrder())));
		return unmappedLocations;
	}

	private SocialPageListInfo convertAppleLocationToLocationPageListInfo(AppleBrandAccountPage appleLocation) {

		SocialPageListInfo locationPageListInfo = new SocialPageListInfo();
		locationPageListInfo.setId(appleLocation.getIntentId());
		locationPageListInfo.setMapped(appleLocation.getBusinessId() != null ? Boolean.TRUE : Boolean.FALSE);
		locationPageListInfo.setPageName(appleLocation.getLocationName());
		locationPageListInfo.setLink(appleLocation.getMapLink());
		locationPageListInfo.setAddress(appleLocation.getSingleLineAddress());
		Validity validity = fetchValidityAndErrorMessage(appleLocation);
		locationPageListInfo.setValidType(validity.getValidType());
		locationPageListInfo.setErrorCode(validity.getErrorCode());
		locationPageListInfo.setErrorMessage(validity.getErrorMessage());
		locationPageListInfo.setConnectedInReseller(false);
		return locationPageListInfo;

	}

	private Validity fetchValidityAndErrorMessage(AppleBrandAccountPage appleLocation) {
		Validity validity = new Validity();
		if(appleLocation.getIsValid() == 1){
			validity.setValidType(ValidTypeEnum.VALID.getName());
		}
		return validity;
	}

	@Override
	public LocationPageMapping getLocationMappingPages(LocationMappingRequest request) {
		LocationPageMapping response = new LocationPageMapping();
		List<Integer> businessIds = request.getBusinessIds();
		Long enterpriseId = request.getBusinessId();
		List<String> status = request.getStatus();

		if (CollectionUtils.isEmpty(businessIds)) {
			BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
			businessLiteRequest.setKey("businessNumber");
			businessLiteRequest.setValue(enterpriseId);
			BusinessLiteDTO business = businessCoreService.getBusinessLite(businessLiteRequest);
			businessIds = businessUtilService.getBusinessLocationsForEnterpriseUsingLite(business, null);
			LOGGER.info("Fetched location Ids {}", enterpriseId);

		}

		if (CollectionUtils.isNotEmpty(businessIds)) {
			if (CollectionUtils.isEmpty(status)) {
				LOGGER.info("blank status received hence returning for business {}", enterpriseId);
				setDefaultResponse(response);
				return response;
			}
			Boolean toSearch = Objects.nonNull(request.getSearch()) && !request.getSearch().isEmpty();
			response.setTotalLocations(businessIds.size());
			List<AppleBrandAccountPage> pages = applePageRepository.findByBusinessIdIn(businessIds);
			response.setUnmapped(businessIds.size() - pages.size());
			if (CollectionUtils.isNotEmpty(businessIds) && status.size() > 1) {
				prepareLocationApplePageMapping(businessIds, response, status, request.getPage(),
						request.getSize(), request.getSearch(),toSearch,pages);
			} else if (status.size() == 1) {
				prepareMappedAndUnMappedData(enterpriseId, businessIds, response, status, request.getPage(),
						request.getSize(), request.getSearch(),toSearch,pages);
			} else {
				LOGGER.error("No business ids found for and enterpriseIds: {}", businessIds);
				throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "Business not found.");
			}
		}
		LOGGER.info("[Apple Setup] Response returned");
		return response;
	}

	private void setDefaultResponse(LocationPageMapping response) {
		response.setLocationList(new ArrayList<>());
		response.setDisconnectedCount(0);
		response.setAllPagesMapped(false);
		response.setPermissionIssuePageCount(0);
	}

	private void prepareLocationApplePageMapping(List<Integer> businessIds,
			LocationPageMapping response, List<String> status, Integer page, Integer size, String search,Boolean toSearch,List<AppleBrandAccountPage> pages) {

		Map<Integer, BusinessLocationLiteEntity> businessLocationsMap ;
		if(toSearch){
			businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(businessIds,page,size,search,response);
		}else{
			businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginated(businessIds,page,size);
		}
		List<AppleBrandAccountPage> applePages = pages.stream().filter(p -> businessLocationsMap.containsKey(p.getBusinessId())).collect(Collectors.toList());

			if (Objects.nonNull(businessLocationsMap)) {
				Map<Integer, AppleBrandAccountPage> businessApplePageMap = new HashMap<>();
				if (CollectionUtils.isNotEmpty(applePages)) {
					businessApplePageMap = applePages.stream().collect(Collectors.toMap(AppleBrandAccountPage::getBusinessId, Function.identity()));
				}
				List<ChannelLocationInfo> locationList = getChannelLocationInfoList(businessLocationsMap, businessApplePageMap);

				List<ChannelLocationInfo> locationListWithoutMapping = new LinkedList<>();
				List<ChannelLocationInfo> locationListWithMapping = new LinkedList<>();

				for (ChannelLocationInfo ll : locationList) {
					if (ll.getPageData() != null) {
						locationListWithMapping.add(ll);
					} else {
						locationListWithoutMapping.add(ll);
					}
				}

				locationListWithoutMapping.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
				locationListWithMapping.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));

				List<ChannelLocationInfo> finalList = new LinkedList<>();
				if (status.contains(LocationStatusEnum.UNMAPPED.getName())) {
					finalList.addAll(locationListWithoutMapping);
				}
				if (status.contains(LocationStatusEnum.MAPPED.getName())) {
					finalList.addAll(locationListWithMapping);
				}
				response.setLocationList(finalList);
			}

	}

	private void prepareMappedAndUnMappedData(Long enterpriseId, List<Integer> businessIds, LocationPageMapping response,
											  List<String> status, Integer page, Integer size,String search,Boolean toSearch,
											  List<AppleBrandAccountPage> appleBrandAccountPages) {
		LOGGER.info("Locations for apple pages size : {} and enterpriseId :{}",appleBrandAccountPages.size(),enterpriseId);
		Map<Integer, BusinessLocationLiteEntity> businessLocationsMap;
		List<Integer> filterBusinessIds = new LinkedList<>();
		appleBrandAccountPages.stream().filter(Objects::nonNull).forEach(pages -> filterBusinessIds.add(pages.getBusinessId()));
		businessIds.removeAll(filterBusinessIds);
		if(status.contains(LocationStatusEnum.UNMAPPED.getName())){
			LOGGER.info("Unmapped locations for apple pages size : {} and enterpriseId :{}",appleBrandAccountPages.size(),enterpriseId);
			if(toSearch){
				businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(businessIds,page,size,search,response);
			}else{
				businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginated(businessIds,page,size);
			}
		}else {
			if(toSearch){
				businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(filterBusinessIds,page,size,search,response);
			}else{
				businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginated(filterBusinessIds,page,size);
			}
		}
		Map<Integer,AppleBrandAccountPage> businessLinkedinPageMap = appleBrandAccountPages.stream().collect(Collectors.toMap(AppleBrandAccountPage::getBusinessId, Function.identity()));
		List<ChannelLocationInfo> locationList = getChannelLocationInfoList(businessLocationsMap, businessLinkedinPageMap);
		locationList.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
		response.setLocationList(locationList);
		response.setAllPagesMapped(businessIds.isEmpty());

	}

	private List<ChannelLocationInfo> getChannelLocationInfoList(
			Map<Integer, BusinessLocationLiteEntity> idTobusinessLocationsMap,
			Map<Integer, AppleBrandAccountPage> idToAppleLocationMap) {
		LOGGER.info("[Apple Setup] com.birdeye.social.Apple.AppleServiceImpl#getChannelLocationInfoList().");
		List<ChannelLocationInfo> locationList = new ArrayList<>();
		if(Objects.isNull(idTobusinessLocationsMap)){
			return locationList;
		}
		for (Map.Entry<Integer, BusinessLocationLiteEntity> entry : idTobusinessLocationsMap.entrySet()) {
			ChannelLocationInfo locInfo = new ChannelLocationInfo();
			locInfo.setLocationId(entry.getKey());
			locInfo.setLocationName(
					entry.getValue().getAlias1() != null ? entry.getValue().getAlias1() : entry.getValue().getName());
			locInfo.setAddress(prepareBusinessAddress(entry.getValue()));
			if (Objects.nonNull(idTobusinessLocationsMap) && idToAppleLocationMap.get(entry.getKey()) != null) {
				Map<String, LocationPageListInfo> pageInfoMap = new HashMap<>();
				LocationPageListInfo locationPageListInfo = preparePageData(idToAppleLocationMap.get(entry.getKey()));
				pageInfoMap.put(SocialChannel.APPLE.getName(), locationPageListInfo);
				locInfo.setPageData(pageInfoMap);
			}
			locationList.add(locInfo);
		}

		LOGGER.info("[Apple Setup] Exiting com.birdeye.social.Apple.AppleServiceImpl#getChannelLocationInfoList().");
		return locationList;
	}

	private String prepareBusinessAddress(BusinessLocationLiteEntity businessLocationLiteEntity) {
		StringBuilder address = new StringBuilder();
		if (org.apache.commons.lang3.StringUtils.isNotEmpty(businessLocationLiteEntity.getAddress1())) {
			address.append(businessLocationLiteEntity.getAddress1()).append(", ");

		}
		if (org.apache.commons.lang3.StringUtils.isNotEmpty(businessLocationLiteEntity.getAddress2())) {
			address.append(businessLocationLiteEntity.getAddress2()).append(", ");

		}
		if (org.apache.commons.lang3.StringUtils.isNotEmpty(businessLocationLiteEntity.getCity())) {
			address.append(businessLocationLiteEntity.getCity()).append(", ");

		}
		if (org.apache.commons.lang3.StringUtils.isNotEmpty(businessLocationLiteEntity.getState())) {
			address.append(businessLocationLiteEntity.getState()).append(" ");

		}
		// Zipcode will be always there
		if (StringUtils.isNotEmpty(businessLocationLiteEntity.getZip())) {
			address.append(businessLocationLiteEntity.getZip());

		}
		return address.toString();
	}

	private String prepareBusinessAddress(BusinessLocationLiteDTO businessLocationLiteEntity) {
		StringBuilder address = new StringBuilder();
		if (org.apache.commons.lang3.StringUtils.isNotEmpty(businessLocationLiteEntity.getAddress1())) {
			address.append(businessLocationLiteEntity.getAddress1()).append(", ");

		}

		if (org.apache.commons.lang3.StringUtils.isNotEmpty(businessLocationLiteEntity.getCity())) {
			address.append(businessLocationLiteEntity.getCity()).append(", ");

		}
		if (org.apache.commons.lang3.StringUtils.isNotEmpty(businessLocationLiteEntity.getState())) {
			address.append(businessLocationLiteEntity.getState()).append(" ");

		}
		// Zipcode will be always there
		if (StringUtils.isNotEmpty(businessLocationLiteEntity.getZip())) {
			address.append(businessLocationLiteEntity.getZip());

		}
		return address.toString();
	}

	private LocationPageListInfo preparePageData(AppleBrandAccountPage businessApplePage) {
		LocationPageListInfo pageInfo = new LocationPageListInfo();
		pageInfo.setId(businessApplePage.getIntentId());
		pageInfo.setPageName(businessApplePage.getLocationName());
		pageInfo.setAddress(businessApplePage.getSingleLineAddress());

		pageInfo.setLink(businessApplePage.getMapLink());
		Validity validity = fetchValidityAndErrorMessage(businessApplePage);
		pageInfo.setValidType(validity.getValidType());
		pageInfo.setErrorCode(validity.getErrorCode());
		pageInfo.setErrorMessage(validity.getErrorMessage());
		pageInfo.setConnectedInReseller(false);
		return pageInfo;
	}

	private List<AppleBrandAccountPage> getBusinessApplePages(List<AppleBrandAccountPage> ApplePages,
			Map<Integer, BusinessLocationLiteEntity> businessLocationsMap) {
		return ApplePages.stream()
				.filter(businessApplePage -> businessLocationsMap.keySet().contains(businessApplePage.getBusinessId()))
				.collect(Collectors.toList());
	}

	@Override
	public void saveLocationAndMapping(AppleAddLocationMappingRequest appleAddLocationMappingRequest,long enterpriseId) {
		AppleBrandAccountPage appleBrandAccountPage = new AppleBrandAccountPage();
		appleBrandAccountPage.setEnterpriseId(enterpriseId);
		appleBrandAccountPage.setSingleLineAddress(appleAddLocationMappingRequest.getSocialLocationAddress());
		applePageRepository.save(appleBrandAccountPage);

	}

	@Override
	public ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId) {
		List<AppleBrandAccountPage> appleLocations = applePageRepository.findByEnterpriseId(enterpriseId);
		if (appleLocations.isEmpty()) {
			return ChannelSetupStatus.PageSetupStatus.NO_PAGES_FOUND;
		} else {
			// We only need to check if there are pages which have broken mapping
			// We don't need to check for unmapped pages here
			Optional<AppleBrandAccountPage> result = appleLocations.stream()
					.filter(appleLocation -> (appleLocation.getBusinessId() != null && appleLocation.getIsValid() != 1))
					.findAny();

			if (result.isPresent()) {
				return ChannelSetupStatus.PageSetupStatus.DISCONNECTED_PAGES_FOUND;
			}
		}
		return ChannelSetupStatus.PageSetupStatus.OK;
	}

	@Override
	public ChannelSetupStatus.PageSetupStatus getPageConnectionSetupStatus(Long enterpriseId) {
		List<AppleBrandAccountPage> appleLocations = applePageRepository.findByEnterpriseId(enterpriseId);
		if (appleLocations.isEmpty()) {
			return ChannelSetupStatus.PageSetupStatus.INVALID_INTEGRATION;
		} else {
			// We only need to check if there are pages which have broken mapping
			// We don't need to check for unmapped pages here
			Optional<AppleBrandAccountPage> result = appleLocations.stream()
					.filter(appleLocation -> (appleLocation.getBusinessId() != null && appleLocation.getIsValid() == 1))
					.findAny();

			if (result.isPresent()) {
				return ChannelSetupStatus.PageSetupStatus.VALID_INTEGRATION;
			}
			return ChannelSetupStatus.PageSetupStatus.INVALID_INTEGRATION;
		}
	}

	@Override
	public SocialLocationResponse getAllMappedAndUnMappedLocations(Long enterpriseId) throws Exception {
		SocialLocationResponse response = new SocialLocationResponse();
		LOGGER.info("Get all locations for enterprise id : {}",enterpriseId);
		List<AppleDoupDTO> appleDoupDTOS = new ArrayList<>();
		List<AppleDoupDTO> mappedApplePages = new ArrayList<>();
		List<AppleDoupDTO> unMappedApplePages = new ArrayList<>();

		try {
			BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
			businessLiteRequest.setKey("businessNumber");
			businessLiteRequest.setValue(enterpriseId);
			BusinessLiteDTO business = businessCoreService.getBusinessLite(businessLiteRequest);
			List<Integer> businessIds = businessUtilService.getBusinessLocationsForEnterpriseUsingLite(business, null);
			if(CollectionUtils.isEmpty(businessIds)){
				LOGGER.info("No location found for enterprise id: {}",businessIds);
				return response;
			}
			LOGGER.info("Business ids {} for the enterprise id {}",businessIds,enterpriseId);
			Map<String, Object>  businessLocationNames = businessCoreService.getBusinessesInBulkByBusinessIds(businessIds,false);
			Map<Integer,AppleBrandAccountPage> mappedPages = applePageRepository.findAllByEnterpriseId(enterpriseId).stream().
					filter(page -> Objects.nonNull(page.getBusinessId())).collect(Collectors.toMap(AppleBrandAccountPage::getBusinessId, Function.identity()));

			for(Map.Entry<String, Object> entry : businessLocationNames.entrySet()){
				LOGGER.info("Set data in AppleDoupDTO : {}",entry.getKey());
				AppleDoupDTO appleDoupDTO = new AppleDoupDTO();
				Map<String ,Object> locationData = (Map<String, Object>) businessLocationNames.get(entry.getKey());
				appleDoupDTO.setLocationId((Long) locationData.get("businessNumber"));
				appleDoupDTO.setBusinessLocationName((String) locationData.get("businessAlias"));

				if(mappedPages.containsKey(Integer.parseInt(entry.getKey()))){
					AppleBrandAccountPage appleBrandAccountPage = mappedPages.get(Integer.parseInt(entry.getKey()));
					appleDoupDTO.setAppleLocationAddress(appleBrandAccountPage.getSingleLineAddress());
					appleDoupDTO.setAppleLocationName(appleBrandAccountPage.getLocationName());
					appleDoupDTO.setIntentId(appleBrandAccountPage.getIntentId());
					appleDoupDTO.setMapUrl(appleBrandAccountPage.getMapLink());
					mappedApplePages.add(appleDoupDTO);
				}else{
					unMappedApplePages.add(appleDoupDTO);
				}
			}
			unMappedApplePages.sort(Comparator.comparing(AppleDoupDTO::getBusinessLocationName, nullsFirst(Comparator.naturalOrder())));
			mappedApplePages.sort(Comparator.comparing(AppleDoupDTO::getBusinessLocationName, nullsFirst(Comparator.naturalOrder())));

			appleDoupDTOS.addAll(unMappedApplePages);
			appleDoupDTOS.addAll(mappedApplePages);
			LOGGER.info("Process completed for the enterprise: {} and size {}",enterpriseId,appleDoupDTOS.size());
		}catch (Exception e){
			throw new BirdeyeSocialException("Exception occurred while getting the locations",e);
		}
		if(CollectionUtils.isNotEmpty(appleDoupDTOS)){
			response.setData(appleDoupDTOS);
		}
		return response;
	}

	@Override
	public AppleResponseStatusDTO addMappingForEnterprise(AppleBulkImport request) {
		AppleResponseStatusDTO responseStatusDTO;
		if(Objects.isNull(request) || Objects.isNull(request.getData()) || Objects.isNull(request.getMetaData())){
			LOGGER.info("Request is null from DOUP service");
			responseStatusDTO = pushToDoupService(AppleChatStatus.REJECTED.name(),request.getEventId(),DoupOperationEnum.ADD.name(),SocialErrorMessagesEnum.UNKNOWN_ERR.name());
			return responseStatusDTO;
		}
		try{
			BusinessAppleAccounts appleAccounts = appleAccountRepository.findByEnterpriseId(Long.valueOf(request.getMetaData().getExtraParams().getBusinessNumber()));
			if(Objects.isNull(appleAccounts)){
				LOGGER.info("No apple chat account exist for enterprise id : {}",request.getMetaData().getExtraParams().getBusinessNumber());
				responseStatusDTO = pushToDoupService(AppleChatStatus.REJECTED.name(),request.getEventId(),DoupOperationEnum.REJECTED.name(),SocialErrorMessagesEnum.ACCOUNT_NOT_FOUND.name());
				return responseStatusDTO;
			}
			LOGGER.info("Request received to map apple page for enterprise id : {},request :{}",request.getMetaData().getExtraParams().getBusinessNumber(),request);
			AppleDoupDTO appleDoupDTO = request.getData();
			if(Objects.isNull(appleDoupDTO.getIntentId()) || Objects.isNull(appleDoupDTO.getAppleLocationAddress()) ||
					Objects.isNull(appleAccounts.getBrandId()) || Objects.isNull(appleDoupDTO.getAppleLocationName()) ||
					Objects.isNull(appleDoupDTO.getLocationId())){
				responseStatusDTO = pushToDoupService(AppleChatStatus.REJECTED.name(),request.getEventId(),DoupOperationEnum.REJECTED.name(),SocialErrorMessagesEnum.FIELD_IS_NULL.name());
				return responseStatusDTO;
			}
			BusinessLiteDTO parentBusinessDetails = businessCoreService.getBusinessLiteByNumber(Long.valueOf(request.getMetaData().getExtraParams().getBusinessNumber()));
			List<Integer> businessIds = businessUtilService.getBusinessLocationsForEnterpriseUsingLite(parentBusinessDetails, null);
			if(CollectionUtils.isEmpty(businessIds)){
				LOGGER.info("No location exist under enterprise : {}",request.getMetaData().getExtraParams().getBusinessNumber());
				responseStatusDTO = pushToDoupService(AppleChatStatus.REJECTED.name(),request.getEventId(),DoupOperationEnum.REJECTED.name(),SocialErrorMessagesEnum.LOCATION_UNKNOWN.name());
				return responseStatusDTO;
			}

			BusinessLiteDTO childBusinessDetails = businessCoreService.getBusinessLiteByNumber(appleDoupDTO.getLocationId());
			if(!businessIds.contains(childBusinessDetails.getBusinessId())){
				LOGGER.info("Location id {} does not exist for the given enterprise id {}",childBusinessDetails.getBusinessId(),parentBusinessDetails.getBusinessId());
				responseStatusDTO = pushToDoupService(AppleChatStatus.REJECTED.name(),request.getEventId(),DoupOperationEnum.REJECTED.name(),SocialErrorMessagesEnum.BUSINESS_ERR.name());
				return responseStatusDTO;
			}

			AppleBrandAccountPage businessIdAlreadyExists = applePageRepository.findByBusinessId(childBusinessDetails.getBusinessId());
			if(Objects.nonNull(businessIdAlreadyExists)){
				LOGGER.info("Updating page for business id : {}",childBusinessDetails.getBusinessId());
				saveApplePage(businessIdAlreadyExists,parentBusinessDetails.getBusinessNumber(),childBusinessDetails.getBusinessId(),appleDoupDTO.getIntentId()
						,1,appleDoupDTO.getMapUrl(),appleDoupDTO.getAppleLocationName(),appleDoupDTO.getAppleLocationAddress());
				LOGGER.info("Updated Page for business id : {}",childBusinessDetails.getBusinessId());
				responseStatusDTO = pushToDoupService(AppleChatStatus.SUCCESS.name(),request.getEventId(),DoupOperationEnum.UPDATE.name(),"");
			}else {
				AppleBrandAccountPage intentIdAlreadyExists = applePageRepository.findByIntentIdAndEnterpriseId(appleDoupDTO.getIntentId(),parentBusinessDetails.getBusinessNumber());
				if(Objects.nonNull(intentIdAlreadyExists)) {
					if (!businessIds.contains(intentIdAlreadyExists.getBusinessId())) {
						LOGGER.info("Location id {} does not exist for the given enterprise id {}", intentIdAlreadyExists.getBusinessId(), parentBusinessDetails.getBusinessId());
						responseStatusDTO = pushToDoupService(AppleChatStatus.REJECTED.name(), request.getEventId(), DoupOperationEnum.REJECTED.name(), SocialErrorMessagesEnum.BUSINESS_ERR.name());
						return responseStatusDTO;
					}
					LOGGER.info("Updating page on basis of intent id :{}", intentIdAlreadyExists.getIntentId());
					saveApplePage(intentIdAlreadyExists,parentBusinessDetails.getBusinessNumber(),childBusinessDetails.getBusinessId(),appleDoupDTO.getIntentId()
							, 1,appleDoupDTO.getMapUrl(),appleDoupDTO.getAppleLocationName(),appleDoupDTO.getAppleLocationAddress());
					LOGGER.info("Updated page on basis of intent id :{}", intentIdAlreadyExists.getIntentId());
					responseStatusDTO = pushToDoupService(AppleChatStatus.SUCCESS.name(), request.getEventId(), DoupOperationEnum.UPDATE.name(), "");
				}else{
					AppleBrandAccountPage appleBrandAccountPages = new AppleBrandAccountPage();
					LOGGER.info("Adding new page for business id : {}",childBusinessDetails.getBusinessId());
					saveApplePage(appleBrandAccountPages,parentBusinessDetails.getBusinessNumber(),childBusinessDetails.getBusinessId(),appleDoupDTO.getIntentId()
							, 1,appleDoupDTO.getMapUrl(),appleDoupDTO.getAppleLocationName(),appleDoupDTO.getAppleLocationAddress());
					LOGGER.info("Saved Page for business id : {}",childBusinessDetails.getBusinessId());
					responseStatusDTO = pushToDoupService(AppleChatStatus.SUCCESS.name(),request.getEventId(),DoupOperationEnum.ADD.name(),"");
				}
			}
		}catch(Exception e){
			LOGGER.info("Something went wrong while mapping pages for enterprise id : {} with error {}",request.getMetaData().getExtraParams().getBusinessNumber(),e.getMessage());
			responseStatusDTO = pushToDoupService(AppleChatStatus.REJECTED.name(),request.getEventId(),DoupOperationEnum.REJECTED.name(),SocialErrorMessagesEnum.UNKNOWN_ERR.name());
		}
		return responseStatusDTO;
	}

	@Override
	public AppleChatAccountStatus getAccountStatus(Long enterpriseId) {
		AppleChatAccountStatus appleChatAccountStatus = new AppleChatAccountStatus();
		BusinessAppleAccounts appleAccounts = appleAccountRepository.findByEnterpriseId(enterpriseId);
		if(Objects.nonNull(appleAccounts)){
			LOGGER.info("Found apple account for enterprise id :{}",enterpriseId);
			appleChatAccountStatus.setStatus(AppleAccountStatus.FOUND.name());
			appleChatAccountStatus.setBrandId(appleAccounts.getBrandId());
		}else{
			LOGGER.info("Apple account not found for enterprise id :{}",enterpriseId);
			appleChatAccountStatus.setStatus(AppleAccountStatus.NOT_FOUND.name());
		}
		return appleChatAccountStatus;
	}

	private void saveApplePage(AppleBrandAccountPage appleBrandAccountPages,Long businessNumber, Integer businessId,
							   String intentId, Integer isValid, String mapUrl, String appleLocationName, String appleLocationAddress) {
		appleBrandAccountPages.setEnterpriseId(businessNumber);
		appleBrandAccountPages.setBusinessId(businessId);
		appleBrandAccountPages.setIntentId(intentId);
		appleBrandAccountPages.setIsValid(isValid);
		appleBrandAccountPages.setMapLink(mapUrl);
		appleBrandAccountPages.setLocationName(appleLocationName);
		appleBrandAccountPages.setSingleLineAddress(appleLocationAddress);
		applePageRepository.save(appleBrandAccountPages);
	}

	private AppleResponseStatusDTO pushToDoupService(String status, long eventId, String operation, String errorName) {
		AppleResponseStatusDTO responseStatusDTO = new AppleResponseStatusDTO();
		String errMessage = errorName.isEmpty() ?  "" : socialErrorMessageService.getMessage(errorName,SocialChannel.APPLE.getName());
		responseStatusDTO.setStatus(status);
		responseStatusDTO.setEventId(eventId);
		responseStatusDTO.setErrorMessage(errMessage);
		responseStatusDTO.setOperation(operation);
		LOGGER.info("Callback event sent to doup service for event id :{}",eventId);
		kafkaProducerService.sendObjectV1(Constants.APPLE_CHAT_CALL_BACK_EVENT, responseStatusDTO );
		return responseStatusDTO;
	}

	public AppleIntegrationStatus getPageIntegrationStatus(Integer businessId) {
		List<AppleBrandAccountPage> appleLocations = applePageRepository.findByBusinessIdList(businessId);
		if (appleLocations.isEmpty()) {
			return AppleIntegrationStatus.CONNECT;
		} else {
			// We only need to check if there are pages which have broken mapping
			// We don't need to check for unmapped pages here
			Optional<AppleBrandAccountPage> result = appleLocations.stream()
					.filter(appleLocation -> (appleLocation.getBusinessId() != null && appleLocation.getIsValid() != 1))
					.findAny();

			if (result.isPresent()) {
				return AppleIntegrationStatus.RECONNECT;
			}
		}
		return AppleIntegrationStatus.INTEGRATED;
	}

	@Override
	public Integer getBusinessIdForPageId(String destinationId, String intent, boolean isSMB) {
		if (isSMB == true) {
			List<Integer> results = applePageRepository.findBusinessIdByBrandId(destinationId);
			if (CollectionUtils.isNotEmpty(results)) {
				return results.get(0);
			}
		} else {
			return applePageRepository.findBusinessIdByIntentId(intent);
		}
		return null;
	}

	@Override
	public String getAppleIntegrationStatus(Integer businessId) {
			return getPageIntegrationStatus(businessId).name();
	}

	@Override
	public String getApplePageIdByBusinessId(Integer businessId, Long enterpriseNumber, boolean isSMB) {
		if (isSMB == true) {
			 List<BusinessAppleAccounts> businessAppleAccounts= appleAccountRepository.findByEnterpriseIdList(enterpriseNumber);
			if (CollectionUtils.isNotEmpty(businessAppleAccounts)) {
				return businessAppleAccounts.get(0).getBrandId();
			}
		} else {
			List<AppleBrandAccountPage> appleLocations = applePageRepository.findValidMappingByBusinessId(businessId);
			if(CollectionUtils.isNotEmpty(appleLocations)) {
				return appleLocations.get(0).getIntentId();
			}
		}
		return null;
	}

	@Override
	public void sendMessage(AppleSendRequest request) throws Exception {
		appleExternalService.sendAppleMessage(request);
	}

	@Override
	public String attachmentPreDownload(AttachmentPreDownloadRequest request) throws Exception {
		return appleExternalService.attachmentPreDownload(request);
	}

	@Override
	public AttachmentPreUploadResponse attachmentPreUpload(AttachmentPreUploadRequest request) throws Exception {
		AttachmentPreUploadResponse attachmentPreUploadResponse = null;
		Optional<Map<String, String>> preUploadResponse = appleExternalService.attachmentPreUpload(request);
		if (preUploadResponse.isPresent()) {
			attachmentPreUploadResponse = new AttachmentPreUploadResponse();
			attachmentPreUploadResponse.setMmcsUrl(preUploadResponse.get().get("mmcs-url"));
			attachmentPreUploadResponse.setMmcsOwner(preUploadResponse.get().get("mmcs-owner"));
			attachmentPreUploadResponse.setUploadUrl(preUploadResponse.get().get("upload-url"));
			return attachmentPreUploadResponse;
		}
		return attachmentPreUploadResponse;
	}

	@Override
	public List<SocialNotificationAudit> auditNotification(Object notificationObject) {
		SocialNotificationAudit socialNotificationAudit = new SocialNotificationAudit();
		try {
			ObjectMapper mapper = new ObjectMapper();
			JsonNode node = mapper.convertValue(notificationObject, JsonNode.class);
			for (Iterator<String> it = node.fieldNames(); it.hasNext(); ) {
				String s = it.next();
				switch (s) {
					case "id":
						socialNotificationAudit.setEvent_id(node.get("id").asText());
						break;
				}
			}
			socialNotificationAudit.setChannel(SocialChannel.APPLE.getName());
			socialNotificationAudit.setEvent_payload(JSONUtils.toJSON(notificationObject));
			socialNotificationAudit.setEvent_type(NotificationAuditTypes.APPLE_MESSAGE_NOTIFICATION.name());

		}catch (Exception e){
			LOGGER.info("Unable to audit exception for the notification : {}",notificationObject);
		}
		return Collections.singletonList(socialNotificationAudit);
	}
}
