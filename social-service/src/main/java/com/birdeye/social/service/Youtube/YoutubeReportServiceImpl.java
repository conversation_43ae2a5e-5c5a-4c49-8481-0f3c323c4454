package com.birdeye.social.service.Youtube;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessYoutubeChannelRepository;
import com.birdeye.social.dao.reports.SocialReportPropertyRepository;
import com.birdeye.social.dao.reports.YoutubeChannelInsightRepo;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessYoutubeChannel;
import com.birdeye.social.entities.report.YoutubeChannelInsight;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.insights.ES.Request.DataModel;
import com.birdeye.social.insights.ES.Request.ESPageRequest;
import com.birdeye.social.insights.ES.Request.InsightsESRequest;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.insights.PageInsightDataPoint;
import com.birdeye.social.insights.PageInsights;
import com.birdeye.social.insights.PageLevelMetaData;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.insights.constants.InsightsConstants;
import com.birdeye.social.insights.constants.YoutubeReportMetric;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.Youtube.YoutubeAnalytics.YoutubeAnalyticResponse;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.service.GoogleAuthenticationService;
import com.birdeye.social.service.SocialReportService.Converter.DbDataConverter;
import com.birdeye.social.service.SocialReportService.Converter.ReportDataConverter;
import com.birdeye.social.service.SocialReportService.ES.ReportsEsService;
import com.birdeye.social.sro.GoogleAuthToken;
import com.birdeye.social.utils.*;
import com.birdeye.social.youtube.YoutubeChannelStatisticsResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class YoutubeReportServiceImpl implements YoutubeReportService {

    @Autowired
    private GoogleAuthenticationService googleAuthenticationService;

    @Autowired
    private SocialProxyHandler socialProxyHandler;

    @Autowired
    private YoutubeAnalyticService youtubeAnalyticService;

    @Autowired
    private YoutubeChannelInsightRepo youtubeChannelInsightRepo;


    @Autowired
    private BusinessYoutubeChannelRepository youtubeChannelRepository;

    @Autowired
    private ReportDataConverter reportDataConverter;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private DbDataConverter dbDataConverter;

    @Autowired
    private ReportsEsService reportsEsService;

    @Autowired
    private YoutubeService youtubeService;
    @Autowired
    private SocialReportPropertyRepository reportPropertyRepository;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private IBusinessCoreService iBusinessCoreService;

    private static final Logger log = LoggerFactory.getLogger(YoutubeReportServiceImpl.class);

    private static final String VIDEO_FILTER = "video";

    private final String dateFormatterString = "yyyy-MM-dd HH:mm:ss";


    @Override
    public void postPageInsightsToEs(PageInsights pageInsights) {
        String index = InsightsConstants.YOUTUBE_PAGE_INSIGHTS;
        log.info("Started es upload for business id : {}", pageInsights.getBusinessId());
        List<ESPageRequest> esPageRequest = reportDataConverter.createPageInsightsObject(pageInsights);
        reportsEsService.bulkPostPageInsights(esPageRequest,index);
        log.info("Executed es upload for business id :{}",pageInsights.getBusinessId());
    }


    @Override
    public void generatePageInsightsFromSocialChannel(SocialScanEventDTO socialScanEventDTO) {
        String channelId = socialScanEventDTO.getExternalId();
        if(channelId == null ) {
            log.info("No channelId found for youtube with request {}", socialScanEventDTO);
            return;
        }
        BusinessYoutubeChannel youtubeChannel = youtubeChannelRepository.findByChannelId(channelId);
        log.info("Getting page insight for youtube account: {}",youtubeChannel);

        Integer businessId = iBusinessCoreService.getBusinessId(youtubeChannel.getEnterpriseId());

        // check for reporting enabled
        if(Objects.nonNull(businessId)) {
            String businessIdsEnabledReporting = CacheManager.getInstance()
                    .getCache(SystemPropertiesCache.class).getProperty(SystemPropertiesCache.REPORTING_ENABLED_BUSINESS_NUMBER);

            if(!businessIdsEnabledReporting.contains(String.valueOf(businessId))) {
                log.warn("Reporting not enabled for business id : {}", businessId);
                return;
            }
        }
        Date startDate, endDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        YoutubeChannelInsight businessPosts = youtubeChannelInsightRepo.findFirstByChannelIdOrderByIdDesc(socialScanEventDTO.getExternalId());
        if(Objects.isNull(businessPosts)){
            log.info("Page doesn't exist in db with page id : {}",socialScanEventDTO.getExternalId());
            startDate = DateUtils.addDays(endDate, -60);
        } else {
            log.info("Page exist in db for request : {}", socialScanEventDTO.getExternalId());
            long diff = endDate.getTime() - businessPosts.getLastSyncDate().getTime();
            long noOfDays = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
            if(noOfDays >= 1){
                startDate = DateUtils.addDays(businessPosts.getLastSyncDate(),-2);
            }else {
                log.info("Page is already synced for today with YT page insights: {} and date {} with day diff {}",
                        businessPosts, endDate, diff);
                return;
            }
        }

        pushDataToKafkaForEsUpdate(youtubeChannel, startDate, endDate);
    }


    private void pushDataToKafkaForEsUpdate(BusinessYoutubeChannel youtubeChannel, Date startDate, Date endDate) {
        try {
            GoogleAuthToken googleAuthToken =  getYoutubeCreds(youtubeChannel.getRefreshTokenId());

            String metrics = YoutubeReportMetric.getListOfMetric();

            YoutubeAnalyticResponse reportResponse = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(
                    () -> youtubeAnalyticService.getReportByMetrics(googleAuthToken.getAccess_token(),metrics,
                            DateTimeUtils.getYoutubeReportDateFormat(startDate), DateTimeUtils.getYoutubeReportDateFormat(endDate), null));


            Map<String, PageLevelMetaData> pageLevelMetaDataList = reportDataConverter.prepareDayWiseDataForYoutubeReport(reportResponse);

            if(MapUtils.isEmpty(pageLevelMetaDataList)) {
                log.info("could not proceed since pageLevelMetaDataList is null for youtube channel {}", youtubeChannel);
                return;
            }

            String engageMetrics = YoutubeReportMetric.getListOfEngagementMetric();

            YoutubeAnalyticResponse engagementReportResponse = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(
                    () -> youtubeAnalyticService.getReportByMetrics(googleAuthToken.getAccess_token(),engageMetrics,
                            DateTimeUtils.getYoutubeReportDateFormat(startDate), DateTimeUtils.getYoutubeReportDateFormat(endDate), VIDEO_FILTER));

            Map<String, PageLevelMetaData> pageLevelEngageReportList = reportDataConverter.prepareDayWiseDataForYoutubeReport(engagementReportResponse);

            if(MapUtils.isEmpty(pageLevelMetaDataList)) {
                log.info("could not proceed since pageLevelEngageReportList is empty for youtube channel {}", youtubeChannel);
            }

            YoutubeChannelStatisticsResponse youtubeChannelStatisticsResponse = youtubeService.getChannelInformation(youtubeChannel.getChannelId(), googleAuthToken.getAccess_token());

            Date reportEndDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());

            SimpleDateFormat dateFormatter = new SimpleDateFormat(dateFormatterString);
            DataModel dataModel = DataModel.builder()
                    .pageIds("\"" + youtubeChannel.getChannelId() + "\"")
                    .endDate("\""+dateFormatter.format(reportEndDate)+"\"")
                    .sourceIds(Integer.toString(SocialChannel.YOUTUBE.getId()))
                    .build();
            InsightsESRequest insightsESRequest = new InsightsESRequest(dataModel, SearchTemplate.PAGE_INSIGHT_FOR_PREV_DATE);
            insightsESRequest.setIndex(ElasticConstants.YOUTUBE_PAGE_INSIGHTS.getName());
            insightsESRequest.setRouting(youtubeChannel.getEnterpriseId().toString());
            List<PageInsightDataPoint> pageInsightDataPoints = reportsEsService.getPageInsightHitsDataFromEs(insightsESRequest);

            List<PageLevelMetaData> dataList = mergeYoutubeMetaDataList(pageLevelMetaDataList, pageLevelEngageReportList, youtubeChannelStatisticsResponse, pageInsightDataPoints);


            PageInsights pageInsights = new PageInsights(youtubeChannel.getEnterpriseId(), youtubeChannel.getChannelId(),
                    youtubeChannel.getBusinessId(), SocialChannel.YOUTUBE.getName(), dataList);
            kafkaProducerService.sendObjectV1(Constants.YOUTUBE_PAGE_INSIGHTS, pageInsights);

            addInsightsToDB(pageInsights);
        } catch (Exception ex) {
            log.info("Something went wrong while fetching yt page data {}", youtubeChannel, ex);
        }
    }

    private List<PageLevelMetaData> mergeYoutubeMetaDataList( Map<String, PageLevelMetaData> pageLevelMetaDataList, Map<String, PageLevelMetaData> pageLevelEngageReportList,
                                                              YoutubeChannelStatisticsResponse youtubeChannelStatisticsResponse, List<PageInsightDataPoint> pageInsightDataPoints) {
        Integer prevPostCount = 0;
        Integer currPostCount = Integer.valueOf(youtubeChannelStatisticsResponse.getVideoCount());

        if(CollectionUtils.isNotEmpty(pageInsightDataPoints)) {
            PageInsightDataPoint pageInsightDataPoint = pageInsightDataPoints.get(0);
            prevPostCount = pageInsightDataPoint.getPostCount()==null ? 0 : pageInsightDataPoint.getPostCount();
        }

        Integer finalPostCount =  currPostCount - prevPostCount;;

        pageLevelEngageReportList.keySet().stream().forEach(key -> {
           if(Objects.nonNull(pageLevelMetaDataList.get(key))) {
               pageLevelMetaDataList.get(key).setPostEngagements(pageLevelEngageReportList.get(key).getPostEngagements());
               pageLevelMetaDataList.get(key).setTotalFollower(Integer.valueOf(youtubeChannelStatisticsResponse.getSubscriberCount()));
               pageLevelMetaDataList.get(key).setPostCount(finalPostCount);
           }
        });
        return  pageLevelMetaDataList.values().stream().collect(Collectors.toList());
    }

    private void addInsightsToDB(PageInsights pageInsights) {
        YoutubeChannelInsight youtubeChannelInsight = dbDataConverter.convertPostInsightForYoutube(pageInsights);
        youtubeChannelInsightRepo.save(youtubeChannelInsight);
    }

    private GoogleAuthToken getYoutubeCreds(Integer refreshTokenId) {
        return googleAuthenticationService.getYoutubeAuthTokens(refreshTokenId);
    }
    @Override
    public void backfillYtInsight(BackfillInsightReq socialScanEventDTO) {
        Integer minBackfillDays= reportPropertyRepository.findMinDaysForSourceIdAndReportType(
                socialScanEventDTO.getSourceId(),"profile_matric");
        if(InsightsReportUtil.validateStartAndEndDate(socialScanEventDTO.getStartDate(),socialScanEventDTO.getEndDate(),minBackfillDays)) {
            BusinessYoutubeChannel youtubeChannel = youtubeChannelRepository.findByChannelId(socialScanEventDTO.getExternalId());
            List<PageLevelMetaData> dataList = getInsightsFromYoutube(youtubeChannel,socialScanEventDTO.getStartDate(),socialScanEventDTO.getEndDate());
            PageInsights pageInsights = new PageInsights(youtubeChannel.getEnterpriseId(), youtubeChannel.getChannelId(),
                    youtubeChannel.getBusinessId(), SocialChannel.YOUTUBE.getName(), dataList);
            kafkaProducerService.sendObjectV1(Constants.YOUTUBE_PAGE_INSIGHTS, pageInsights);

            addInsightsToDB(pageInsights);
        }
    }

    private List<PageLevelMetaData> getInsightsFromYoutube(BusinessYoutubeChannel youtubeChannel, Date startDate, Date endDate) {
        GoogleAuthToken googleAuthToken =  getYoutubeCreds(youtubeChannel.getRefreshTokenId());
        String metrics = YoutubeReportMetric.getListOfMetric();
        YoutubeAnalyticResponse reportResponse = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(
                () -> youtubeAnalyticService.getReportByMetrics(googleAuthToken.getAccess_token(),metrics,
                        DateTimeUtils.getYoutubeReportDateFormat(startDate), DateTimeUtils.getYoutubeReportDateFormat(endDate), null));
        Map<String, PageLevelMetaData> pageLevelMetaDataList = reportDataConverter.prepareDayWiseDataForYoutubeReport(reportResponse);
        if(MapUtils.isEmpty(pageLevelMetaDataList)) {
            log.info("backfill could not proceed since pageLevelMetaDataList is null for youtube channel {}", youtubeChannel);
            return new ArrayList<>();
        }
        String engageMetrics = YoutubeReportMetric.getListOfEngagementMetric();
        YoutubeAnalyticResponse engagementReportResponse = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(
                () -> youtubeAnalyticService.getReportByMetrics(googleAuthToken.getAccess_token(),engageMetrics,
                        DateTimeUtils.getYoutubeReportDateFormat(startDate), DateTimeUtils.getYoutubeReportDateFormat(endDate), VIDEO_FILTER));
        Map<String, PageLevelMetaData> pageLevelEngageReportList = reportDataConverter.prepareDayWiseDataForYoutubeReport(engagementReportResponse);
        if(MapUtils.isEmpty(pageLevelMetaDataList)) {
            log.info("backfill could not proceed since pageLevelEngageReportList is empty for youtube channel {}", youtubeChannel);
        }
        pageLevelEngageReportList.keySet().forEach(key -> {
            if(Objects.nonNull(pageLevelMetaDataList.get(key))) {
                pageLevelMetaDataList.get(key).setPostEngagements(pageLevelEngageReportList.get(key).getPostEngagements());
            }
        });
        return new ArrayList<>(pageLevelMetaDataList.values());
    }

}
