package com.birdeye.social.service.SocialReportService.Dedupe;

import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.dao.reports.BusinessPostsRepository;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.report.BusinessPosts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SocialPostDedupeServiceImpl implements SocialPostDedupeService {

	@Autowired
	private BusinessPostsRepository businessPostsRepository;

	@Autowired
	private SocialPostInfoRepository socialPostPublishInfoRepository;

	private static final Logger log = LoggerFactory.getLogger(SocialPostDedupeServiceImpl.class);

	@Override
	public void removeExistingPosts(Set<String> postIds) { // @saurabh -fixed use Set instead of list, contains() will work fast. -> this seems fixed, adding it in other implementation if required.
		try {
			// @saurabh -fixed : 1. use slave db,  2. check index on post id, 3. add logs 4. why to get full businesspost object if you need only post id?
			// 1. pending, 2. index added  3. added 4. fixed
			List<String> businessPostIds = businessPostsRepository.findBusinessPostsIds(postIds);
			log.info("Business post ids : {}",businessPostIds);
			if(CollectionUtils.isEmpty(businessPostIds)) {
				log.info("No existing post found");
				return ;
			}
			businessPostIds.forEach(postIds::remove);
			log.info("New posts Ids available are {}", postIds);
		} catch (Exception ex) {
			log.info("[Social Reporting] dedupe action failed for postIds {} with error {} ", postIds, ex);
		}
	}

	@Override
	public Map<String, Integer> freshPostsCheckInSystem(Set<String> postIds, Map<String, String> targetIdVsPostId) {
		try {
			Set<String> targetIds = targetIdVsPostId.keySet();
			Map<String, Integer> extPostIdToBePostId = new HashMap<>();
			postIds.addAll(targetIds);
			List<SocialPostPublishInfo> postInfoList = socialPostPublishInfoRepository.findByPostIdIn(postIds);


			if (CollectionUtils.isEmpty(postInfoList)) {
				return extPostIdToBePostId;
			}

			//extPostIdToBePostId = postInfoList.stream().collect(Collectors.toMap(SocialPostPublishInfo::getPostId, SocialPostPublishInfo::getSocialPostId));

			for (SocialPostPublishInfo socialPostPublishInfo : postInfoList) {
				String postId = targetIdVsPostId.get(socialPostPublishInfo.getPostId());
				if (!StringUtils.isEmpty(postId)) {
					extPostIdToBePostId.put(postId, socialPostPublishInfo.getSocialPostId());
				} else {
					extPostIdToBePostId.put(socialPostPublishInfo.getPostId(), socialPostPublishInfo.getSocialPostId());
				}
			}
			return extPostIdToBePostId;

		} catch (Exception ex) {
			log.info("Something went wrong while checking entries for postIds {} with error {}", postIds, ex);
			return null;
		}
	}
}
