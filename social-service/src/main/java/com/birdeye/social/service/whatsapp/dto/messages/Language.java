package com.birdeye.social.service.whatsapp.dto.messages;

import com.birdeye.social.service.whatsapp.dto.templates.type.LanguageType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class Language{
    @JsonProperty("code") String code;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
