package com.birdeye.social.service.ratelimit.impl;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.service.ratelimit.ChannelRateLimitService;
import org.springframework.stereotype.Service;
import java.util.regex.Pattern;

@Service
public class TwitterRateLimitServiceImpl implements ChannelRateLimitService {

    private static final String TWEETS_URL = "api.twitter.com/2/tweets";
    private static final String ACCESS_TOKEN_URL = "api.twitter.com/oauth2/token";
    private static final String SEARCH_USER_URL = "api.twitter.com/1.1/users/search.json";
    private static final String SEARCH_LOCATION_URL = "api.twitter.com/1.1/geo/search.json";
    private static final String HOME_TIMELINE_URL = "api.twitter.com/2/users/timelines/reverse_chronological";
    private static final String USER_TIMELINE_URL = "api.twitter.com/2/users/tweets";
    private static final String MENTIONS_TIMELINE_URL = "api.twitter.com/2/users/mentions";
    private static final String PROFILE_INFO_URL = "api.twitter.com/2/users/by/username";
    private static final String LIKE_URL = "api.twitter.com/2/users/likes";
    private static final String FOLLOW_URL = "api.twitter.com/2/users/following";
    private static final String RETWEET_URL = "api.twitter.com/2/users/%s/retweets";
    private static final String HISTORICAL_URL = "data-api.twitter.com/insights/engagement/totals";
    private static final String TOTAL_ENG_URL = "data-api.twitter.com/insights/engagement/historical";

    @Override
    public SocialChannel channelName() {
        return SocialChannel.TWITTER;
    }

    @Override
    public String getCacheUrl(String requestUrl, String method) {
        StringBuilder urlBuilder = new StringBuilder(method).append("/");

        if (requestUrl.contains("/oauth2/token")) {
            urlBuilder.append(ACCESS_TOKEN_URL);
        } else if (Pattern.matches("users/.*/retweets.*", requestUrl)) {
            urlBuilder.append(RETWEET_URL);
        } else if (Pattern.matches("users/.*/following.*", requestUrl)) {
            urlBuilder.append(FOLLOW_URL);
        } else if (Pattern.matches("users/.*/likes.*", requestUrl)) {
            urlBuilder.append(LIKE_URL);
        } else if (requestUrl.contains("/users/by/username")) {
            urlBuilder.append(PROFILE_INFO_URL);
        } else if (Pattern.matches("/users/.*/tweets.*", requestUrl)) {
            urlBuilder.append(USER_TIMELINE_URL);
        } else if (Pattern.matches("/users/.*/mentions.*", requestUrl)) {
            urlBuilder.append(MENTIONS_TIMELINE_URL);
        } else if (requestUrl.contains("/timelines/reverse_chronological")) {
            urlBuilder.append(HOME_TIMELINE_URL);
        } else if (requestUrl.contains("/geo/search.json")) {
            urlBuilder.append(SEARCH_LOCATION_URL);
        } else if (requestUrl.contains("/users/search.json")) {
            urlBuilder.append(SEARCH_USER_URL);
        } else if (requestUrl.contains("/tweets")) {
            urlBuilder.append(TWEETS_URL);
        } else if (requestUrl.contains("/insights/engagement/totals")) {
            urlBuilder.append(TOTAL_ENG_URL);
        } else if (requestUrl.contains("/insights/engagement/historical")) {
            urlBuilder.append(HISTORICAL_URL);
        }  else {
            return null;
        }
        return urlBuilder.toString();
    }
}
