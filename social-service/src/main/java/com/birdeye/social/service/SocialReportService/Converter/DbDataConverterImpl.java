package com.birdeye.social.service.SocialReportService.Converter;

import com.birdeye.social.entities.report.*;
import com.birdeye.social.insights.PageInsights;
import com.birdeye.social.insights.PostData;
import com.birdeye.social.utils.JSONUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;

@Service
public class DbDataConverterImpl implements DbDataConverter{


    @Override
    public FacebookPostInsight convertPostInsight(PostData postData) {
        return FacebookPostInsight.builder()
                .postId(postData.getPostId())
                .businessId(postData.getBusinessId())
                .pageId(postData.getPageId())
                .enterpriseId(postData.getEnterpriseId())
                .data(JSONUtils.toJSON(postData))
                .created(new Date())
                .build();
    }

    @Override
    public FacebookPageInsight convertPageInsight(PageInsights pageInsights) {
        return FacebookPageInsight.builder()
                .date(new Date())
                .lastSyncDate(new Date())
                .nextSyncDate(DateUtils.addDays(new Date(),1))
                .pageId(pageInsights.getPageId())
                .data(JSONUtils.toJSON(pageInsights))
                .enterpriseId(pageInsights.getEnterpriseId())
                .businessId(pageInsights.getBusinessId())
                .build();
    }

    @Override
    public GMBPageInsight convertPageInsightForGmb(PageInsights pageInsights) {
        return GMBPageInsight.builder()
                .date(new Date())
                .lastSyncDate(new Date())
                .nextSyncDate(DateUtils.addDays(new Date(),1))
                .locationId(pageInsights.getPageId())
                .data(JSONUtils.toJSON(pageInsights))
                .enterpriseId(pageInsights.getEnterpriseId())
                .businessId(pageInsights.getBusinessId())
                .build();
    }


    @Override
    public InstagramPageInsight convertPageInsightForInstagram(PageInsights pageInsights) {
        return InstagramPageInsight.builder()
                .date(new Date())
                .pageId(pageInsights.getPageId())
                .data(JSONUtils.toJSON(pageInsights))
                .enterpriseId(pageInsights.getEnterpriseId())
                .businessId(pageInsights.getBusinessId())
                .nextSyncDate(DateUtils.addDays(new Date(),1))
                .lastSyncDate(new Date())
                .build();
    }

    @Override
    public TwitterPageInsight convertPageInsightForTwitter(PageInsights pageInsights) {
        return TwitterPageInsight.builder()
                .date(new Date())
                .lastSyncDate(new Date())
                .nextSyncDate(DateUtils.addDays(new Date(),1))
                .pageId(pageInsights.getPageId())
                .data(JSONUtils.toJSON(pageInsights))
                .enterpriseId(pageInsights.getEnterpriseId())
                .businessId(pageInsights.getBusinessId())
                .build();
    }

    @Override
    public LinkedinPostInsight convertPostInsightForLinkedin(PostData postData) {
        return LinkedinPostInsight.builder()
                .postId(postData.getPostId())
                .businessId(postData.getBusinessId())
                .pageId(postData.getPageId())
                .enterpriseId(postData.getEnterpriseId())
                .data(JSONUtils.toJSON(postData))
                .created(new Date())
                .build();
    }

    @Override
    public LinkedInPageInsight convertPageInsightForLinkedin(PageInsights pageInsights,Date end) {
        Calendar c = Calendar.getInstance();
        c.setTime(end);
        c.add(Calendar.DATE, -1);
        return LinkedInPageInsight.builder()
                .date(new Date())
                .pageId(pageInsights.getPageId())
                .data(JSONUtils.toJSON(pageInsights))
                .enterpriseId(pageInsights.getEnterpriseId())
                .businessId(pageInsights.getBusinessId())
                .lastSyncDate(c.getTime())
                .nextSyncDate(end)
                .build();
    }

    @Override
    public YoutubeChannelInsight convertPostInsightForYoutube(PageInsights pageInsights) {
        return YoutubeChannelInsight.builder()
                .date(new Date())
                .lastSyncDate(new Date())
                .nextSyncDate(DateUtils.addDays(new Date(),1))
                .channelId(pageInsights.getPageId())
                .data(JSONUtils.toJSON(pageInsights, JsonInclude.Include.NON_NULL))
                .enterpriseId(pageInsights.getEnterpriseId())
                .businessId(pageInsights.getBusinessId())
                .build();
    }
}
