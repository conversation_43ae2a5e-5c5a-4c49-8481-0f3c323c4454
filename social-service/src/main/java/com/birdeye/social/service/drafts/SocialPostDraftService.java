package com.birdeye.social.service.drafts;

import com.birdeye.social.model.BusinessIdRequest;
import com.birdeye.social.model.SocialDraftPostListResponse;

public interface SocialPostDraftService {
    SocialDraftPostListResponse getDraftsForRequest(BusinessIdRequest businessIdRequest, Integer businessId,
                                                    Long businessNumber,String requestSource) throws InterruptedException;

    void removePageIdFromDraft(String channel, String pageId);
}
