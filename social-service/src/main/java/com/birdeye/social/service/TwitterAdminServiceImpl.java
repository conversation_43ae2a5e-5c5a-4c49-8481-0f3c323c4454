package com.birdeye.social.service;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.SocialTwitterAccountRepository;
import com.birdeye.social.dto.report.MonthlyScanDTO;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessTwitterAccounts;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service("TwitterAdminServiceImpl")
public class TwitterAdminServiceImpl implements ChannelsAdminService {

    @Autowired
    private SocialTwitterAccountRepository socialTwitterAccountRepository;

    private static final String MYSQL_DATE_FORMAT = "yyyy/MM/dd HH:mm:ss";
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(MYSQL_DATE_FORMAT);
    private static final long MILLIS_IN_A_DAY = 1000 * 60 * 60 * 24;

    private static final Logger log = LoggerFactory.getLogger(TwitterAdminServiceImpl.class);


    @Override
    public List<SocialScanEventDTO> fetchEligibleRecords(Integer count, Integer startId, Date date) throws ParseException {

        Page<BusinessTwitterAccounts> pages = socialTwitterAccountRepository.findByIsValidAndBusinessIdNotNullAndNextSyncDateIsLessThanEqualOrderByNextSyncDateAsc(1, date, new PageRequest(startId, count));
        log.info("Twitter Pages fetched to scan {}", pages);
           //TODO TWITTER_INSIGHTS_PERMISSION needs to be checked
        Date nextDate = simpleDateFormat.parse(simpleDateFormat.format(nextScanDate(date)));
        if(CollectionUtils.isEmpty(pages.getContent())){
            return new ArrayList<>();
        }
        List<Integer> eligibleIds = pages.getContent().stream().map(BusinessTwitterAccounts::getId).collect(Collectors.toList());
        updateNextSyncDate(eligibleIds, nextDate);
        return conversionToScanEventDTO(pages.getContent());
    }

    private static Date nextScanDate(Date date){
        return new Date(date.getTime() + MILLIS_IN_A_DAY);
    }


    private List<SocialScanEventDTO> conversionToScanEventDTO(List<BusinessTwitterAccounts> twitterPages) {
        List<SocialScanEventDTO> scanEventList = new ArrayList<>();

        if(CollectionUtils.isEmpty(twitterPages)){
            return scanEventList;
        }
        for(BusinessTwitterAccounts twitterPage : twitterPages) {
            SocialScanEventDTO scanEventDTO = new SocialScanEventDTO();
            scanEventDTO.setChannelPrimaryId(twitterPage.getId());
            scanEventDTO.setBusinessId(twitterPage.getBusinessId());
            scanEventDTO.setEnterpriseId(twitterPage.getEnterpriseId());
            scanEventDTO.setExternalId(String.valueOf(twitterPage.getProfileId()));
            scanEventDTO.setPageName(twitterPage.getName());
            scanEventDTO.setSourceName(SocialChannel.TWITTER.getName());
            scanEventDTO.setSourceId(SocialChannel.TWITTER.getId());
            scanEventDTO.setAccountId(twitterPage.getAccountId());
            scanEventList.add(scanEventDTO);
        }
        log.info("BusinessTwitterAccounts Scan event dto ready for {}", scanEventList);

        return scanEventList;
    }

    @Override
    public void updateNextSyncDate(List<Integer> eligibleIds, Date nextDate) {
        socialTwitterAccountRepository.updateNextSyncDate(nextDate, eligibleIds);
    }

    @Override
    public SocialChannel channelName() {
        return SocialChannel.TWITTER;
    }

    @Override
    public List<MonthlyScanDTO> fetchEligibleRecordsMonthly() throws ParseException {
        return null;
    }

    @Override
    public SocialScanEventDTO fetchChannelDetails(String channelId, Date date) throws ParseException {
        return null;
    }

}
