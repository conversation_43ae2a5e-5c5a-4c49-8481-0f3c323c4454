package com.birdeye.social.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.insights.InsightsRequest;
import com.birdeye.social.insights.PageInsightsV2Response;
import com.birdeye.social.insights.PerformanceSummaryResponse;
import com.birdeye.social.insights.ES.ReportType;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.model.GoogleLocationStatus;
import com.birdeye.social.service.GoogleMyBusinessPageService;
import com.birdeye.social.service.ResellerReportService;
import com.birdeye.social.service.SocialReportService.SocialReportService;
import com.birdeye.social.sro.ResellerDashboardReportRequest;
import com.birdeye.social.sro.ResellerDashboardReportResponse;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ResellerReportServiceImpl implements ResellerReportService {
	
	@Autowired
	private GoogleMyBusinessPageService googleMyBusinessPageService;
	
	@Autowired
	private SocialPostInfoRepository socialPostPublishInfoRepository;
	
	@Autowired
	private KafkaProducerService kafkaProducerService;
	
	@Autowired
	private SocialReportService socialReportService;
	
	@Autowired
	@Qualifier(Constants.SOCIAL_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor executor;
	
	@Override
	public void publishResellerDashboardReport(ResellerDashboardReportRequest request) throws Exception {
		ResellerDashboardReportResponse response = new ResellerDashboardReportResponse();
        response.setAccountId(request.getAccountId());
		//Thread spike issue due to which making call sequentially
		getPageSetupReport(request, response);
		getPostPublishedReport(request, response);
		try {
			getPageFollowersReport(request, response);
		} catch (Exception e) {
			throw new BirdeyeSocialException("[publishResellerDashboardReport] Exception occurred while fetching account insights {} ", e);
		}
		try {
			getPageImpressionAndEngagementRate(request, response);
		} catch (Exception e) {
			throw new BirdeyeSocialException("[publishResellerDashboardReport] Exception occurred while fetching account insights {}", e);
		}
		// List<Future<?>> futureTasks = submitTasks(request, response);
       // waitForCompletion(futureTasks);
        log.info("Publishing reseller dashboard report: {} " ,response);
		kafkaProducerService.sendObjectWithKeyV1(request.getAccountId().toString(),Constants.SOCIAL_RESELLER__DASHBOARD_REPORTS, response);
	}
	private List<Future<?>> submitTasks(ResellerDashboardReportRequest request, ResellerDashboardReportResponse response) {
		List<Future<?>> futureTasks = new ArrayList<>();
		futureTasks.add(executor.submit(() -> getPageSetupReport(request, response)));
		futureTasks.add(executor.submit(() -> getPostPublishedReport(request, response)));
		futureTasks.add(executor.submit(() -> {
			try {
				getPageFollowersReport(request, response);
			} catch (Exception e) {
				throw new BirdeyeSocialException("[publishResellerDashboardReport] Exception occurred while fetching account insights {} ", e);
			}
		}));
		futureTasks.add(executor.submit(() -> {
			try {
				getPageImpressionAndEngagementRate(request, response);
			} catch (Exception e) {
				throw new BirdeyeSocialException("[publishResellerDashboardReport] Exception occurred while fetching account insights {}", e);
			}
		}));
		return futureTasks;
    }
	private void waitForCompletion(List<Future<?>> futureTasks) {
	        for (Future<?> future : futureTasks) {
	            try {
	                future.get();
	            } catch (InterruptedException | ExecutionException e) {
	                log.error("[publishResellerDashboardReport] Exception occurred while waiting for future tasks to complete {}", e);
	            }
	        }
	    }
  
	private void getPageImpressionAndEngagementRate(ResellerDashboardReportRequest request,
			ResellerDashboardReportResponse response) throws Exception  {
		Calendar calendar=Calendar.getInstance();
		TimeZone timeZone = TimeZone.getTimeZone("GMT");
		calendar.setTimeZone(timeZone);
    	InsightsRequest insightsRequest=new InsightsRequest();
    	List<Integer> businessIds = new ArrayList<>();
		if ("Business".equals(request.getType())) {
			businessIds = new ArrayList<>(Arrays.asList(request.getAccountId()));
		}else {
			businessIds=new ArrayList<>(request.getBusinessIds());
		}
		insightsRequest.setBusinessIds(businessIds);
		insightsRequest.setEnterpriseId(request.getAccountNumber());
		insightsRequest.setGroupByType("day");
		insightsRequest.setReportType(ReportType.POST_INSIGHTS.getName());
		insightsRequest.setEndDate(calendar.getTime());
		calendar.add(Calendar.DAY_OF_YEAR, -30);
		insightsRequest.setStartDate(calendar.getTime());
		PerformanceSummaryResponse performanceSummaryResponse= socialReportService.getChannelSummaryForAllChannel(insightsRequest);        
		response.setSocialEngagementRate(performanceSummaryResponse.getEngRate());
		response.setSocialImpressions(performanceSummaryResponse.getImpressions());
	}
	private void getPageFollowersReport(ResellerDashboardReportRequest request,
			ResellerDashboardReportResponse response) throws Exception{
		Calendar calendar=Calendar.getInstance();
		TimeZone timeZone = TimeZone.getTimeZone("GMT");
		calendar.setTimeZone(timeZone);
    	InsightsRequest insightsRequest=new InsightsRequest();
    	List<Integer> businessIds = new ArrayList<>();
		if ("Business".equals(request.getType())) {
			businessIds = new ArrayList<>(Arrays.asList(request.getAccountId()));
		}else {
			businessIds=new ArrayList<>(request.getBusinessIds());
		}
		insightsRequest.setBusinessIds(businessIds);
		insightsRequest.setEnterpriseId(request.getAccountNumber());
		insightsRequest.setGroupByType("day");
		insightsRequest.setReportType(SearchTemplate.PAGE_FOLLOWER_INSIGHTS.getName());
		insightsRequest.setEndDate(calendar.getTime());
		calendar.add(Calendar.DAY_OF_YEAR, -30);
		insightsRequest.setStartDate(calendar.getTime());
		PageInsightsV2Response pageInsightsV2Response= (PageInsightsV2Response)
				socialReportService.getChannelPageInsightsForAll(insightsRequest,Boolean.TRUE);
		response.setSocialFollowerCount(Objects.nonNull(pageInsightsV2Response.getTotalAudience())?pageInsightsV2Response.getTotalAudience():0);
		response.setSocialFollowerGrowthCount(Objects.nonNull(pageInsightsV2Response.getNet())?pageInsightsV2Response.getNet():0);
	}

	private void getPostPublishedReport(ResellerDashboardReportRequest request,
			ResellerDashboardReportResponse response) {
		List<Integer> businessIds = new ArrayList<>();
		if ("Business".equals(request.getType())) {
			businessIds = new ArrayList<>(Arrays.asList(request.getAccountId()));
		} else {
			businessIds=new ArrayList<>(request.getBusinessIds());
		}
		List<Object[]>  socialPostPublishInfoList = socialPostPublishInfoRepository.findAllPostPublishedInLast30Days(businessIds);
		
		if(CollectionUtils.isNotEmpty(socialPostPublishInfoList)) {
			Map<String, List<Object[]>> groupedByChannel = socialPostPublishInfoList.stream()
	                .collect(Collectors.groupingBy(data -> (String) data[0]));
	        response.setGooglePostsPublished(groupedByChannel.getOrDefault(SocialChannel.GMB.getName(), Collections.emptyList()).size());
	        response.setFacebookPostsPublished(groupedByChannel.getOrDefault(SocialChannel.FACEBOOK.getName(), Collections.emptyList()).size());
	        response.setInstagramPostsPublished(groupedByChannel.getOrDefault(SocialChannel.INSTAGRAM.getName(), Collections.emptyList()).size());
	        response.setLinkedinPostsPublished(groupedByChannel.getOrDefault(SocialChannel.LINKEDIN.getName(), Collections.emptyList()).size());
	        response.setTwitterPostsPublished(groupedByChannel.getOrDefault(SocialChannel.TWITTER.getName(), Collections.emptyList()).size());
	        response.setYoutubePostsPublished(groupedByChannel.getOrDefault(SocialChannel.YOUTUBE.getName(), Collections.emptyList()).size());
	        
	        Map<Integer, List<Object[]>> groupedByBusinessId = socialPostPublishInfoList.stream()
	                .collect(Collectors.groupingBy(data -> (Integer) data[1]));

	        businessIds.removeIf(businesId -> groupedByBusinessId.containsKey(businesId) && groupedByBusinessId.get(businesId).size() >= 2);

	        if (businessIds.isEmpty()) {
	            response.setTwoPlusSocialPostsPublished("Yes");
	        } else {
	            response.setTwoPlusSocialPostsPublished("No");
	            response.setLessThanTwoSocialPostsBusinessIds(businessIds);
	        }
		}else {
			response.setTwoPlusSocialPostsPublished("No");
            response.setLessThanTwoSocialPostsBusinessIds(businessIds);
		}
        
	}

	private void getPageSetupReport(ResellerDashboardReportRequest request,ResellerDashboardReportResponse response) {
		List<Integer> businessIds = new ArrayList<>();
		if ("Business".equals(request.getType())) {
			businessIds = new ArrayList<>(Arrays.asList(request.getAccountId()));
		}else {
			businessIds=new ArrayList<>(request.getBusinessIds());
		}
		List<BusinessGoogleMyBusinessLocation> gmbPages = googleMyBusinessPageService
				.findByBusinessIds(request.getAccountId(), businessIds, GoogleLocationStatus.LAUNCHED.name());

		if (CollectionUtils.isNotEmpty(gmbPages)) {
			if (gmbPages.size() == businessIds.size()) {
				response.setGoogleMessagingIntegrated("Yes");
			} else {
				response.setGoogleMessagingIntegrated("No");
				List<Integer> mappedBusinessIds = gmbPages.stream().map(BusinessGoogleMyBusinessLocation::getBusinessId)
						.collect(Collectors.toList());
				businessIds.removeAll(mappedBusinessIds);
				response.setGoogleMessagingNotIntegratedBusinessIds(businessIds);
			}
		} else {
			response.setGoogleMessagingIntegrated("No");
			response.setGoogleMessagingNotIntegratedBusinessIds(businessIds);
		}
	}

}
