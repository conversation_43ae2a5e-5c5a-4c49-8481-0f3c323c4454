package com.birdeye.social.service.Youtube;

import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessYoutubeChannelRepository;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.dao.SocialPostsAssetsRepository;
import com.birdeye.social.dto.TokensAndUrlAuthData;
import com.birdeye.social.entities.BusinessYoutubeChannel;
import com.birdeye.social.entities.PermissionMapping;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.SocialPostsAssets;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.TooManyRequestException;
import com.birdeye.social.model.SocialPostSchedulerMetadata;
import com.birdeye.social.model.Youtube.YoutubeCategory.YoutubeCategory;
import com.birdeye.social.model.Youtube.YoutubeCategory.YoutubeCategoryItems;
import com.birdeye.social.model.Youtube.YoutubeError.YoutubeBaseErrorResponse;
import com.birdeye.social.model.Youtube.YoutubePlaylist.YoutubePlaylist;
import com.birdeye.social.model.Youtube.YoutubePlaylist.YoutubePlaylistItems;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.GoogleAuthenticationService;
import com.birdeye.social.service.IPermissionMappingService;
import com.birdeye.social.service.KafkaExternalService;
import com.birdeye.social.service.ratelimit.RateLimitService;
import com.birdeye.social.sro.GoogleAuthToken;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import com.birdeye.social.youtube.YoutubeMetaData;
import com.birdeye.social.youtube.YoutubeStatusEnum;
import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import com.google.api.client.http.InputStreamContent;
import com.google.api.services.youtube.YouTube;
import com.google.api.services.youtube.model.*;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpRequest;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URI;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
public class YoutubeVideoUploadImpl implements YoutubeVideoUpload {


    @Autowired
    private YoutubeService youtubeService;

    @Autowired
    private BusinessYoutubeChannelRepository businessYoutubeChannelRepository;

    @Autowired
    private GoogleAuthenticationService googleAuthenticationService;

    @Autowired
    private YoutubeObjectConverter youtubeObjectConverter;

    @Autowired
    private SocialPostInfoRepository socialPostPublishInfoRepository;

    @Autowired
    private SocialPostsAssetsRepository socialPostsAssetsRepository;

    @Autowired
    private CommonService commonService;

    @Autowired
    private KafkaExternalService kafkaExternalService;

    @Autowired
    private IPermissionMappingService permissionMappingService;

    @Autowired
    private RateLimitService rateLimitService;

    private static final String VIDEO_URL = "https://www.googleapis.com/upload/youtube/v3/videos";

    private static final String FORBIDDEN_ERROR_MSG = "The YouTube account of the authenticated user is suspended";

    private static final Logger logger = LoggerFactory.getLogger(YoutubeVideoUploadImpl.class);

    @Override
    public void postVideo(SocialPostPublishInfo publishInfo, File videoFile) {
        try {
            if(Objects.isNull(videoFile)){
                failedPostAudit(publishInfo,"Post should contain video",ErrorCodes.YOUTUBE_VIDEO_EMPTY.value(), null);
                throw new BirdeyeSocialException(ErrorCodes.YOUTUBE_VIDEO_EMPTY.value(),"Video can not be null in Youtube post");
            }
            List<BusinessYoutubeChannel> businessYoutubeChannels = businessYoutubeChannelRepository.findByBusinessId(publishInfo.getBusinessId());
            if (CollectionUtils.isEmpty(businessYoutubeChannels)) {
                logger.info("Unable to get youtube channel for business id : {}", publishInfo.getBusinessId());
                return ;
            }
            BusinessYoutubeChannel businessYoutubeChannel = businessYoutubeChannels.get(0);
            GoogleAuthToken googleAuthToken;
            try {
                googleAuthToken = googleAuthenticationService.getYoutubeAuthTokens(businessYoutubeChannel.getRefreshTokenId());
            }catch (BirdeyeSocialException e){
                if(e.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value()) {
                    markYoutubePageInvalid(businessYoutubeChannel);
                }
                throw new BirdeyeSocialException(e.getCode(),e.getMessage());
            }
            if (Objects.isNull(googleAuthToken)) {
                logger.info("Unable to get access token from refresh token for business id {}", publishInfo.getExternalPageId());
                return ;
            }
            TokensAndUrlAuthData tokensAndUrlAuthData = youtubeObjectConverter.convertAuthTokenResponse(googleAuthToken);
            YouTube service =  youtubeService.getService(tokensAndUrlAuthData);
            if(Objects.isNull(publishInfo.getSocialPost().getPostMetadata())) {
                logger.info("Required fields can not be null for post channel: {}",businessYoutubeChannel.getChannelId());
                failedPostAudit(publishInfo,"Mandatory fields are empty!",ErrorCodes.MANDATORY_FIELD_IS_NULL.value(), null);
                return;
            }
            SocialPostSchedulerMetadata socialPostSchedulerMetadata =
                    JSONUtils.fromJSON(publishInfo.getSocialPost().getPostMetadata(),SocialPostSchedulerMetadata.class);
            if(Objects.isNull(socialPostSchedulerMetadata) || Objects.isNull(socialPostSchedulerMetadata.getYoutubePostMetaData())){
                logger.info("Required fields can not be null for post channel: {}",businessYoutubeChannel.getChannelId());
                failedPostAudit(publishInfo,"Mandatory fields are empty!",ErrorCodes.MANDATORY_FIELD_IS_NULL.value(), null);
                return;
            }

            YoutubeMetaData request = JSONUtils.fromJSON(socialPostSchedulerMetadata.getYoutubePostMetaData(),YoutubeMetaData.class);
            if(Objects.isNull(request)){
                logger.info("Unable to get youtube metadata from post info for channel id : {}",businessYoutubeChannel.getChannelId());
                return;
            }
            Video videoResponse = uploadYoutubeVideo(request,service,publishInfo,videoFile, googleAuthToken.getAccess_token());
            logger.info("Video upload complete for business id : {}",publishInfo.getBusinessId());
            if(Objects.nonNull(videoResponse)) {
                SocialPostsAssets postAsset = socialPostsAssetsRepository.findById(Integer.valueOf(publishInfo.getSocialPost().getVideoIds()));
                if(Objects.nonNull(videoResponse.getStatus().getUploadStatus()) && videoResponse.getStatus().getUploadStatus().equalsIgnoreCase(YoutubeStatusEnum.UPLOADED.getName())) {
                    if(Objects.nonNull(request.getPlaylistId())) {
                        PlaylistItem playlistItem = addVideoToPlaylist(request.getPlaylistId(), videoResponse.getId(), service);
                        logger.info("Youtube video added to playlist for playlist id :{} and video id :{}",playlistItem.getId(),videoResponse.getId());
                    }
                    if(Objects.nonNull(postAsset) && StringUtils.isNotEmpty(postAsset.getVideoThumbnail())){
                        ThumbnailSetResponse thumbnailSetResponse = addThumbnailPhoto(commonService.getImageFile(postAsset.getVideoThumbnail()),videoResponse.getId(),service);
                        logger.info("Youtube thumbnail added to video for video id :{}  and thumbnailSetResponse :{}",videoResponse.getId(),thumbnailSetResponse);
                    }
                }else if(Objects.nonNull(videoResponse.getStatus().getFailureReason())){
                    throw new BirdeyeSocialException(ErrorCodes.FAILED_TO_UPLOAD_YOUTUBE_VIDEO,videoResponse.getStatus().getFailureReason());
                }
                publishInfo.setIsPublished(SocialPostStatusEnum.PUBLISHED.getId());
                publishInfo.setPostId(videoResponse.getId());
                publishInfo.setPostUrl(Constants.YOUTUBE_VIDEO_URL+videoResponse.getId());
                socialPostPublishInfoRepository.saveAndFlush(publishInfo);
                kafkaExternalService.publishSocialPostEvent(publishInfo);
            }else {
                logger.info("Video response is null for external id: {}",publishInfo.getExternalPageId());
                failedPostAudit(publishInfo,"Something went wrong while uploading video",ErrorCodes.FAILED_TO_UPLOAD_YOUTUBE_VIDEO.value(), null);
            }
        } catch (BirdeyeSocialException ex) {

            logger.warn("Error occurred while posting on youtube: {}",ex.toString());
            PermissionMapping pm = errorHandlerForYoutubeService(ex);

            if((Objects.nonNull(pm) && pm.getMarkInvalid() == 1)
                    ||  ((Objects.nonNull(ex.getErrorCode()) && ex.getErrorCode() == HttpStatus.FORBIDDEN.value()) &&
                    StringUtils.isNotEmpty(ex.getMessage()) && ex.getMessage().contains(FORBIDDEN_ERROR_MSG))) {
                kafkaExternalService.markPageInvalid(SocialChannel.YOUTUBE.getName(), publishInfo.getExternalPageId());
            }

            failedPostAudit(publishInfo, pm.getErrorMessage() , ErrorCodes.FAILED_TO_UPLOAD_YOUTUBE_VIDEO.value(), pm.getBucket());
            throw new BirdeyeSocialException(ex.getCode(),ex.getMessage());
        }
    }

    @Override
    public boolean deleteVideo(SocialPostPublishInfo publishInfo) {
        try {
            TokensAndUrlAuthData tokensAndUrlAuthData = getYouTubeTokensAndUrlAuthData(publishInfo.getExternalPageId());
            YouTube service =  youtubeService.getService(tokensAndUrlAuthData);
            return deleteVideo(service, publishInfo);
        } catch (BirdeyeSocialException ex) {
            logger.info("Error occurred while deleting on youtube: {}",ex.toString());
            return false;
        }
    }
    @Override
    public void editYoutubeVideoText(SocialPostPublishInfo publishInfo){
        logger.info("Edit youtube video text for channel id : {} and postId: {}",publishInfo.getExternalPageId(), publishInfo.getPostId());
        try {
            TokensAndUrlAuthData tokensAndUrlAuthData = getYouTubeTokensAndUrlAuthData(publishInfo.getExternalPageId());
            YouTube service = youtubeService.getService(tokensAndUrlAuthData);
            if(Objects.isNull(service)){
                logger.info("Unable to get youtube service for channel id : {}",publishInfo.getExternalPageId());
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_EDIT_YOUTUBE_OBJECT,"Unable to get youtube service for channel id : "+publishInfo.getExternalPageId());
            }
            YoutubeMetaData request = getYoutubeMetaData(publishInfo, publishInfo.getExternalPageId());
            if (Objects.isNull(request)) {
                logger.info("Unable to get youtube metadata from post info for channel id : {}", publishInfo.getExternalPageId());
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_EDIT_YOUTUBE_OBJECT,"Unable to get youtube metadata from post info for channel id : "+publishInfo.getExternalPageId());
            }
            if(Objects.isNull(request.getCategoryId())) {
                logger.info("Category id is null for channel id : {} so setting it to 22 (Default Value)",publishInfo.getExternalPageId());
                request.setCategoryId("22");
            }
            Video videoResponse = editVideoText(request, publishInfo, service);
            if(Objects.isNull(videoResponse)){
                logger.info("Video response is null for external id: {}",publishInfo.getExternalPageId());
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_EDIT_YOUTUBE_OBJECT,"Video response is null for external id: "+publishInfo.getExternalPageId());
            }
            publishInfo.setIsPublished(SocialPostStatusEnum.PUBLISHED.getId());
            publishInfo.setPostUrl("https://www.youtube.com/watch?v="+publishInfo.getPostId());
            logger.info("Youtube video edited for channel id : {} and post id : {}",publishInfo.getExternalPageId(),publishInfo.getPostId());
        }catch (BirdeyeSocialException ex) {
            logger.info("Error occurred while editing posting on youtube: {}",ex.toString());
            PermissionMapping pm = errorHandlerForYoutubeService(ex);
            publishInfo.setIsPublished(SocialPostStatusEnum.FAILED.getId());
            publishInfo.setFailureReason(pm.getErrorMessage());
            publishInfo.setFailureCode(ErrorCodes.UNABLE_TO_EDIT_YOUTUBE_OBJECT.value());
            publishInfo.setBucket(pm.getBucket());
        }
        socialPostPublishInfoRepository.saveAndFlush(publishInfo);
    }
    private YoutubeMetaData getYoutubeMetaData(SocialPostPublishInfo publishInfo, String channelId){
        if(Objects.isNull(publishInfo.getSocialPost().getPostMetadata())) {
            logger.info("Required fields can not be null for post channel: {}",channelId);
            failedPostAudit(publishInfo,"Mandatory fields are empty!",ErrorCodes.MANDATORY_FIELD_IS_NULL.value(), null);
            return null;
        }
        SocialPostSchedulerMetadata socialPostSchedulerMetadata =
                JSONUtils.fromJSON(publishInfo.getSocialPost().getPostMetadata(),SocialPostSchedulerMetadata.class);
        if(Objects.isNull(socialPostSchedulerMetadata) || Objects.isNull(socialPostSchedulerMetadata.getYoutubePostMetaData())){
            logger.info("Required fields can not be null for post channel: {}",channelId);
            failedPostAudit(publishInfo,"Mandatory fields are empty!",ErrorCodes.MANDATORY_FIELD_IS_NULL.value(), null);
            return null;
        }

        YoutubeMetaData request = JSONUtils.fromJSON(socialPostSchedulerMetadata.getYoutubePostMetaData(),YoutubeMetaData.class);
        if(Objects.isNull(request)){
            logger.info("Unable to get youtube metadata from post info for channel id : {}",channelId);
        }
        return request;
    }
    private Video editVideoText(YoutubeMetaData request, SocialPostPublishInfo publishInfo, YouTube service){
        logger.info("Prepare edit youtube video content : {}",publishInfo.getExternalPageId());
        Video video = new Video();
        video.setId(publishInfo.getPostId());
        VideoSnippet snippet = new VideoSnippet();
        snippet.setCategoryId(request.getCategoryId());
        snippet.setDescription(publishInfo.getSocialPost().getPostText());
        snippet.setTitle(request.getTitle());
        video.setSnippet(snippet);
        Video videoResponse = null;

        try {
            if (rateLimitService.validateAndEnqueueRateLimitsPresent(true, getHttpRequest(HttpMethod.PUT, VIDEO_URL, null),
                    Constants.SOCIAL_APPLICATION.toUpperCase(), SocialChannel.YOUTUBE.getName(), null)) {
                logger.info("Rate limit passed for youtube video upload");
                YouTube.Videos.Update videoPostRequest = service.videos()
                        .update("id,snippet", video);
                videoResponse = videoPostRequest.execute();
                logger.info("Youtube video edited for channel id : {} and video id : {}",publishInfo.getExternalPageId(),videoResponse.getId());
            }
        }catch (GoogleJsonResponseException e){
            if(CollectionUtils.isNotEmpty(e.getDetails().getErrors())) {
                throw new BirdeyeSocialException(e.getDetails().getCode(), e.getDetails().getErrors().get(0).getMessage());
            }
            throw new BirdeyeSocialException(e.getDetails().getCode(), e.getDetails().getMessage());
        } catch (TooManyRequestException e){
            String requestId = MDC.get("requestId");
            MDC.put(requestId + RateLimitingConstants.RETRY_TIME_SECONDS, e.getRetryAfterSeconds().toString());
            rateLimitService.publishToKafkaForRateLimitAudit(MDC.get("uri"), MDC.get("uriType"),
                    VIDEO_URL, HttpMethod.POST.name(), e.getBucketConfig().getApiIdentifier(),
                    e.getBucketConfig().getChannelName(), e.getRetryAfterSeconds());
            throw new BirdeyeSocialException(e.getCode().value(), e.getMessage());
        } catch (Exception e){
            logger.info("[social post] YT error for business id: {} : {}",publishInfo.getBusinessId(),e.getMessage());
            throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED.value(),e.getMessage());
        }
        return videoResponse;
    }

    public TokensAndUrlAuthData getYouTubeTokensAndUrlAuthData(String channelId) {
        List<BusinessYoutubeChannel> businessYoutubeChannels = businessYoutubeChannelRepository.findByChannelIdAndIsValid(channelId, 1);
        if (CollectionUtils.isEmpty(businessYoutubeChannels)) {
            logger.info("Unable to get youtube channel for channel id : {}", channelId);
            return null;
        }
        BusinessYoutubeChannel businessYoutubeChannel = businessYoutubeChannels.get(0);
        GoogleAuthToken googleAuthToken;
        try {
            googleAuthToken = googleAuthenticationService.getYoutubeAuthTokens(businessYoutubeChannel.getRefreshTokenId());
        }catch (BirdeyeSocialException e){
            if(e.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value()) {
                markYoutubePageInvalid(businessYoutubeChannel);
            }
            throw new BirdeyeSocialException(e.getCode(),e.getMessage());
        }
        if (Objects.isNull(googleAuthToken)) {
            logger.info("Unable to get access token from refresh token for channel id {}", channelId);
            return null;
        }
        return youtubeObjectConverter.convertAuthTokenResponse(googleAuthToken);
    }

    private PermissionMapping errorHandlerForYoutubeService(BirdeyeSocialException ex) {
        Integer errorCode = ex.getErrorCode();
        String message = ex.getMessage();
        // no need to check sub-code, text can be parsed on the basis of parentCode and error text.
        List<PermissionMapping> permissionMappings =permissionMappingService.getDataByChannelAndParentErrorCodeAndPermissionCode(SocialChannel.YOUTUBE.getName(),
                errorCode, -1);

        if(CollectionUtils.isEmpty(permissionMappings)) {
            logger.info("New error found for YOUTUBE posting with errorCode: {} subcode: {} and message: {}", errorCode, message);
            return permissionMappingService.getDataByChannelAndPermissionCode(SocialChannel.YOUTUBE.getName(),
                    Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
        }
        if(permissionMappings.size() == 1) return permissionMappings.get(0);

        for(PermissionMapping pm: permissionMappings) {
            if(StringUtils.isNotEmpty(message) && message.contains(pm.geterrorActualMessage())){
                return pm;
            }
        }
        return new PermissionMapping();
    }

    @Override
    public void markYoutubePageInvalid(BusinessYoutubeChannel businessYoutubeChannel) {
        businessYoutubeChannel.setIsValid(0);
        businessYoutubeChannelRepository.save(businessYoutubeChannel);
        commonService.sendYoutubeSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(businessYoutubeChannel),
                null, businessYoutubeChannel.getBusinessId(), businessYoutubeChannel.getEnterpriseId());
    }

    private ThumbnailSetResponse addThumbnailPhoto(File photo, String videoId, YouTube service) {
        ThumbnailSetResponse response = new ThumbnailSetResponse();
        try {
            InputStreamContent mediaContent =
                    new InputStreamContent("application/octet-stream",
                            new BufferedInputStream(new FileInputStream(photo)));
            mediaContent.setLength(photo.length());
            YouTube.Thumbnails.Set request = service.thumbnails().set(videoId, mediaContent);
            response = request.execute();
        }catch (GoogleJsonResponseException e){
            YoutubeBaseErrorResponse baseResponse = JSONUtils.fromJSON(e.getContent(),YoutubeBaseErrorResponse.class);
            if(Objects.nonNull(baseResponse)) {
                logger.error("GoogleJsonResponseException occurred while adding thumbnail : {}",baseResponse);
            }
        }catch (IOException e){
            logger.error("IOException occurred while adding thumbnail to video id :{}",videoId);
        }catch (Exception e){
            logger.error("Unable to add video to playlist");
        }
        return response;
    }

    private Video uploadYoutubeVideo(YoutubeMetaData request, YouTube service, SocialPostPublishInfo publishInfo,
                                     File videoFile, String accessToken) {
        logger.info("Prepare youtube video upload data : {}",publishInfo.getExternalPageId());
        Video video = new Video();
        VideoSnippet snippet = new VideoSnippet();
        snippet.setCategoryId(request.getCategoryId());
        snippet.setDescription(publishInfo.getSocialPost().getPostText());
        snippet.setTitle(request.getTitle());
        video.setSnippet(snippet);
        VideoStatus status = new VideoStatus();
        status.setPrivacyStatus(request.getPrivacyStatus());
        status.setMadeForKids(request.isMadeForKids());
        video.setStatus(status);
        Video videoResponse = new Video();

        try {
            if (rateLimitService.validateAndEnqueueRateLimitsPresent(true, getHttpRequest(HttpMethod.POST, VIDEO_URL, null),
                    Constants.SOCIAL_APPLICATION.toUpperCase(), SocialChannel.YOUTUBE.getName(), null)) {
                logger.info("Rate limit passed for youtube video upload");
                InputStreamContent mediaContent = new InputStreamContent("application/octet-stream", new BufferedInputStream(new FileInputStream(videoFile)));
                mediaContent.setLength(videoFile.length());
                YouTube.Videos.Insert videoPostRequest = service.videos()
                        .insert("snippet,status", video, mediaContent);
                videoResponse = videoPostRequest.setAutoLevels(false).setNotifySubscribers(true).setStabilize(true).execute();
                logger.info("Youtube video uploaded for channel id : {} and video id : {}",publishInfo.getExternalPageId(),videoResponse.getId());
            }

        }catch (GoogleJsonResponseException e){
            if(CollectionUtils.isNotEmpty(e.getDetails().getErrors())) {
                throw new BirdeyeSocialException(e.getDetails().getCode(), e.getDetails().getErrors().get(0).getMessage());
            }
            throw new BirdeyeSocialException(e.getDetails().getCode(), e.getDetails().getMessage());
        } catch (TooManyRequestException e){
            String requestId = MDC.get("requestId");
            MDC.put(requestId + RateLimitingConstants.RETRY_TIME_SECONDS, e.getRetryAfterSeconds().toString());
            rateLimitService.publishToKafkaForRateLimitAudit(MDC.get("uri"), MDC.get("uriType"),
                    VIDEO_URL, HttpMethod.POST.name(), e.getBucketConfig().getApiIdentifier(),
                    e.getBucketConfig().getChannelName(), e.getRetryAfterSeconds());
            throw new BirdeyeSocialException(e.getCode().value(), e.getMessage());
        } catch (Exception e){
            logger.info("[social post] YT error for business id: {} : {}",publishInfo.getBusinessId(),e.getMessage());
//            failedPostAudit(publishInfo,e.getMessage(),ErrorCodes.UNKNOWN_ERROR_OCCURRED.value());
        }
        return videoResponse;
    }

    private HttpRequest getHttpRequest(HttpMethod httpMethod, String url, HttpHeaders headers) {
        return new HttpRequest() {
            @Override
            public HttpMethod getMethod() {
                return httpMethod;
            }

            @SneakyThrows
            @Override
            public URI getURI() {
                return new URI(url);
            }

            @Override
            public HttpHeaders getHeaders() {
                return headers;
            }
        };
    }

    private boolean deleteVideo(YouTube service, SocialPostPublishInfo publishInfo) {
        try {
            YouTube.Videos.Delete videoDeleteRequest = service.videos()
                    .delete(publishInfo.getPostId());
           videoDeleteRequest.execute();
            logger.info("Youtube video deleted for channel id : {} and post publish info id : {}",publishInfo.getExternalPageId(), publishInfo.getId());
            return true;
        } catch (GoogleJsonResponseException e){
            if(CollectionUtils.isNotEmpty(e.getDetails().getErrors())) {
                throw new BirdeyeSocialException(e.getDetails().getCode(), e.getDetails().getErrors().get(0).getMessage());
            }
            logger.error("GoogleJsonResponseException occurred while deleting video with error {} {}",e.getDetails().getCode(), e.getDetails().getMessage());
        } catch (Exception e){
            logger.info("[social post delete] YT error for business id: {} : {}",publishInfo.getBusinessId(),e.getMessage());
        }
        return false;
    }

    private PlaylistItem addVideoToPlaylist(String playlistId, String videoId,YouTube service) {
        logger.info("Add video to playlist: {} and video id :{}",playlistId,videoId);
        PlaylistItem playlistItem = new PlaylistItem();
        PlaylistItemSnippet playlistItemSnippet = new PlaylistItemSnippet();
        playlistItemSnippet.setPlaylistId(playlistId);
        ResourceId resourceId = new ResourceId();
        resourceId.setKind("youtube#video");
        resourceId.setVideoId(videoId);
        playlistItemSnippet.setResourceId(resourceId);
        playlistItem.setSnippet(playlistItemSnippet);
        PlaylistItem playlistResponse = new PlaylistItem();
        try {
            YouTube.PlaylistItems.Insert playListRequest = service.playlistItems()
                    .insert("snippet", playlistItem);
            playlistResponse = playListRequest.execute();
            logger.info("Added video to playlist: {} and video id :{}",playlistId,videoId);
        }catch (GoogleJsonResponseException e){
            YoutubeBaseErrorResponse baseResponse = JSONUtils.fromJSON(e.getContent(),YoutubeBaseErrorResponse.class);
            if(Objects.nonNull(baseResponse)) {
                logger.info("GoogleJsonResponseException occurred while adding video to playlist : {}",baseResponse);
            }
        }catch (IOException e){
            logger.info("IOException occurred while adding video to playlist:{}",e.getMessage());
        }catch (Exception e){
            logger.info("Unable to add video to playlist");
        }

        return playlistResponse;
    }

    private void failedPostAudit(SocialPostPublishInfo publishInfo, String message, Integer code, Integer bucket) {
        publishInfo.setIsPublished(SocialPostStatusEnum.FAILED.getId());
        publishInfo.setFailureReason(message);
        publishInfo.setFailureCode(code);
        publishInfo.setBucket(bucket);
        socialPostPublishInfoRepository.saveAndFlush(publishInfo);
        kafkaExternalService.publishSocialPostEvent(publishInfo);
    }


    @Override
    public List<YoutubeCategory> getYoutubeCategories(String pageId) throws Exception {
        logger.info("Get categories for pageId {}",pageId);
        List<YoutubeCategory> youtubeCategories = new ArrayList<>();
        BusinessYoutubeChannel businessYoutubeChannel = businessYoutubeChannelRepository.findByChannelId(pageId);
        if (Objects.isNull(businessYoutubeChannel)) {
            logger.info("Unable to get youtube channel for business id : {}", pageId);
            return youtubeCategories;
        }
        YoutubeCategoryItems items;
        try {
            final GoogleAuthToken googleAuthToken = googleAuthenticationService.getYoutubeAuthTokens(businessYoutubeChannel.getRefreshTokenId());
            if (Objects.isNull(googleAuthToken)) {
                logger.info("Unable to get access token from refresh token for business id {}", businessYoutubeChannel.getBusinessId());
                return youtubeCategories;
            }
            String regionCode = Objects.isNull(businessYoutubeChannel.getCountryCode()) ? "US" : businessYoutubeChannel.getCountryCode();
            items = youtubeService.getCategoriesByRegion(googleAuthToken.getAccess_token(), regionCode);
            if (Objects.isNull(items) || CollectionUtils.isEmpty(items.getItems())) {
                logger.info("Unable to get youtube categories for business id : {} and region code {}", businessYoutubeChannel.getBusinessId(), businessYoutubeChannel.getCountryCode());
                return youtubeCategories;
            }
        }catch (BirdeyeSocialException e){
            if(e.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value()){
                markYoutubePageInvalid(businessYoutubeChannel);
            }
            throw new BirdeyeSocialException(e.getCode(),e.getMessage());
        }
        return youtubeObjectConverter.convertCategoriesItems(items);
    }

    @Override
    public List<YoutubePlaylist> getPlaylistForChannel(String pageId) throws Exception {
        logger.info("Get playlist for pageId {}",pageId);
        List<YoutubePlaylist> playlists = new ArrayList<>();
        BusinessYoutubeChannel businessYoutubeChannel = businessYoutubeChannelRepository.findByChannelId(pageId);
        if(Objects.isNull(businessYoutubeChannel)){
            logger.info("Unable to get youtube channel for page id : {}",pageId);
            return playlists;
        }

        GoogleAuthToken googleAuthToken;
        try {
            googleAuthToken = googleAuthenticationService.getYoutubeAuthTokens(businessYoutubeChannel.getRefreshTokenId());
        }catch (BirdeyeSocialException e){
            if(e.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value()) {
                markYoutubePageInvalid(businessYoutubeChannel);
            }
            throw new BirdeyeSocialException(e.getCode(),e.getMessage());
        }
        if (Objects.isNull(googleAuthToken)) {
            logger.info("Unable to get access token from refresh token for page id {}", businessYoutubeChannel.getChannelId());
            return playlists;
        }
        YoutubePlaylistItems items = youtubeService.getPlaylistForChannelId(businessYoutubeChannel.getChannelId(), googleAuthToken.getAccess_token());
        if (Objects.isNull(items)) {
            logger.info("Unable to get youtube playlist for page id {}", businessYoutubeChannel.getChannelId());
            return playlists;
        }
        return youtubeObjectConverter.convertPlaylistItems(items);
    }

    @Override
    public List<com.google.api.services.youtube.model.Channel> getChannel(TokensAndUrlAuthData tokensAndUrlAuthData) {
        try {
            YouTube youtube =  youtubeService.getService(tokensAndUrlAuthData);
            YouTube.Channels.List request = youtube.channels()
                    .list("snippet,contentDetails,statistics,brandingSettings,contentOwnerDetails,id,localizations,status,topicDetails");
            ChannelListResponse response = request.setMine(true).execute();
            return response.getItems();
        } catch (Exception ex) {
            return null;
        }
    }

    private void markPageInvalidForPublishEvent(Integer businessId, Integer errorCode, String errorMessage) {
        if((Objects.nonNull(errorCode) && errorCode == HttpStatus.FORBIDDEN.value()) &&
                StringUtils.isNotEmpty(errorMessage) && errorMessage.contains(FORBIDDEN_ERROR_MSG)) {
            List<BusinessYoutubeChannel> businessYoutubeChannels = businessYoutubeChannelRepository.findByBusinessId(businessId);
            if(CollectionUtils.isEmpty(businessYoutubeChannels)) {
                logger.info("Youtube channel not found for business id : {}", businessId);
                return ;
            }
            BusinessYoutubeChannel businessYoutubeChannel = businessYoutubeChannels.get(0);
            markYoutubePageInvalid(businessYoutubeChannel);
        }
    }
}
