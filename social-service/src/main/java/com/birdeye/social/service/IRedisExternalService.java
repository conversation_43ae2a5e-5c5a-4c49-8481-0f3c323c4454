package com.birdeye.social.service;

import java.util.List;
import java.util.Set;

import com.birdeye.social.model.RateLimitDomainInfoDTO;
import com.birdeye.social.model.RateLimitingDTO;
import com.birdeye.social.model.RateLimitingMasterDTO;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;

import java.util.Optional;

/**
 * <AUTHOR>
 *
 */
public interface IRedisExternalService {

	void set(String key, String value);
	void set(String key,Long value);
	void set(String key, List<Integer> value);

	void delete(String key);

	Optional<Object> get(String key);

	Integer decrement(String key);

	void populatePriorityQueue(String priorityQueue, Set<TypedTuple<Object>> set);

	Long getPriorityQueueSize(String priorityQueue);

	List<Integer> getRefreshtokenIdListFromPQueue(String priorityQueue, Integer range);

	void priorityQueueRemoveRangeByRank(String priorityQueue, Integer range);
	
	List<Integer> popFromRedis(String queueName, int count);

	List<Long> popLongFromRedis(String queueName, int count);
	void putIfAbsent(String key,Integer value);
	boolean isKeyPresent(String key);
	void fillPriorityQueue(List<Long> snapshot, String priorityQueue);
	Boolean saveToRedisByBusinessGetPageRequestId(String requestId, Set<String> accountSet);

	Boolean checkIfAllProcessedAfterCurrentForGMB(String businessGetPageRequestId, String accountId);

	void setWithExipry(String key, String value, long expiry);

	List<String> popFromQueue(String pattern);

	void setKeyAndValue(String key,String value);
    RateLimitingDTO getRateLimitingDto(String key);

	//RateLimitingMasterDTO getRateLimitingSocialApiDto(String key);
	List<Integer> removeElementFromList(String key, Integer value);

	void setListData(String key, List<Integer> dataList);

	List<Integer> getList(String key);
	RateLimitDomainInfoDTO getRateLimitingSocialDomainDto(String key);


}
