package com.birdeye.social.service.notification;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class NotificationFactory {

    @Autowired
    List<NotificationAuditService> notificationAuditServices;


    public Optional<NotificationAuditService> getNotificationAudit(String channel) {
        Map<String, NotificationAuditService> operationMap = new HashMap<>();
        for(NotificationAuditService notificationAuditService : notificationAuditServices){
            operationMap.put(notificationAuditService.channelName(), notificationAuditService);
        }
        return Optional.ofNullable(operationMap.get(channel));
    }
}
