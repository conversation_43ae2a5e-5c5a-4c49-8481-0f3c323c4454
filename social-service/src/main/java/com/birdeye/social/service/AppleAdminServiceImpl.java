package com.birdeye.social.service;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessAppleLocationRepo;
import com.birdeye.social.dto.report.MonthlyScanDTO;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessAppleLocation;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 23/11/23
 */

@Service("AppleAdminServiceImpl")
public class AppleAdminServiceImpl implements ChannelsAdminService {

    private static final String MYSQL_DATE_FORMAT = "yyyy/MM/dd HH:mm:ss";
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(MYSQL_DATE_FORMAT);
    private static final long MILLIS_IN_A_DAY = 1000 * 60 * 60 * 24;

    @Autowired
    private BusinessAppleLocationRepo businessAppleLocationRepo;


    private final static Logger log = LoggerFactory.getLogger(AppleAdminServiceImpl.class);

    @Override
    public SocialChannel channelName() {
        return SocialChannel.APPLE_CONNECT;
    }

    @Override
    public List<SocialScanEventDTO> fetchEligibleRecords(Integer count, Integer startId, Date date) throws ParseException {

         //Page<BusinessAppleLocation> pages = (Page<BusinessAppleLocation>) businessAppleLocationRepo.findByAppleLocationId("2323070950386171323");
        Page<BusinessAppleLocation> pages = businessAppleLocationRepo.findByBusinessIdNotNullAndNextSyncDateIsLessThanEqualOrderByNextSyncDateAsc(date, new PageRequest(startId, count));
        log.info("Apple Locations fetched to scan {}", pages);

        Date nextDate = simpleDateFormat.parse(simpleDateFormat.format(nextScanDate(date)));

        if (CollectionUtils.isEmpty(pages.getContent())) {
            return new ArrayList<>();
        }

        List<Integer> eligibleIds = pages.getContent().stream().map(BusinessAppleLocation::getId).collect(Collectors.toList());

        updateNextSyncDate(eligibleIds, nextDate);

        //return conversionToScanEventDTO(getEligibleLocationIds(pages.getContent()));
        return conversionToScanEventDTO(pages.getContent());
    }


    private static Date nextScanDate(Date date){
        return new Date(date.getTime() + MILLIS_IN_A_DAY);
    }

    @Override
    public List<MonthlyScanDTO> fetchEligibleRecordsMonthly() throws ParseException {
        return null;
    }

    @Override
    public SocialScanEventDTO fetchChannelDetails(String channelId, Date date) throws ParseException {
        return null;
    }

    private List<SocialScanEventDTO> conversionToScanEventDTO(List<BusinessAppleLocation> appleLocations) {
        List<SocialScanEventDTO> scanEventList = new ArrayList<>();

        for (BusinessAppleLocation appleLocation : appleLocations) {
            SocialScanEventDTO scanEventDTO = new SocialScanEventDTO();
            scanEventDTO.setChannelPrimaryId(appleLocation.getId());
            scanEventDTO.setBusinessId(appleLocation.getBusinessId());
            scanEventDTO.setEnterpriseId(appleLocation.getEnterpriseId());
            scanEventDTO.setExternalId(appleLocation.getAppleLocationId());
            scanEventDTO.setPageName(appleLocation.getLocationName());
            scanEventDTO.setSourceName(SocialChannel.APPLE_CONNECT.getName());
            scanEventDTO.setSourceId(SocialChannel.APPLE_CONNECT.getId());
            scanEventDTO.setAccountId(appleLocation.getAccountId());
            scanEventList.add(scanEventDTO);
        }

        log.info("BusinessAppleLocation Scan event dto ready for {}", scanEventList);
        return scanEventList;
    }

    @Override
    public void updateNextSyncDate(List<Integer> eligibleIds, Date nextDate) {
        businessAppleLocationRepo.updateNextSyncDate(nextDate, eligibleIds);
    }

}
