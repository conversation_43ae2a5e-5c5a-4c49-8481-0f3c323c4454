package com.birdeye.social.service;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.google.GMBPost.GoogleLocalPost;
import com.birdeye.social.model.Feed;
import com.birdeye.social.model.PageDetail;
import com.birdeye.social.model.SocialTimeline;
import com.birdeye.social.model.tiktok.TikTokHashtagResponse;
import com.birdeye.social.utils.ConversionUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service("GooglePostServiceImpl")
public class GooglePostServiceImpl implements ChannelPostService{

    private static final Logger log = LoggerFactory.getLogger(GooglePostServiceImpl.class);

    @Autowired
    private BusinessGMBLocationRawRepository gmbRepo;

    @Autowired
    private GMBPostService gmbService;

    @Override
    public SocialChannel channelName() {
        return SocialChannel.GMB;
    }

    @Override
    public SocialTimeline getFeedData(Date lastPostDate, SocialScanEventDTO data) {
        try {
            BusinessGoogleMyBusinessLocation page = gmbRepo.findById(data.getChannelPrimaryId());
            if (page == null) {
                log.info("No social enabled gmb page found for pageId: {}", data.getChannelPrimaryId());
                return null;
            }

            log.info("[Social Report] request received to scan {}", page.getLocationId());
            List<GoogleLocalPost> feedData = getPagePosts(page);
            if(CollectionUtils.isEmpty(feedData)) {
                return null;
            }
            SocialTimeline socialTimeline = ConversionUtils.convertGmbPostToTimelineObject(feedData, page, lastPostDate);

            return socialTimeline;
        } catch (Exception ex) {
            log.info("Social feed data exception caught for data {} with error {}", data, ex);
            return null;
        }
    }

    @Override
    public List<Object[]> findPageIdAndEnterpriseIdbyPageIds(List<String> locationIds) {
        return gmbRepo.findLocationIdAndEnterpriseIdByLocationsIds(locationIds);
    }

    private List<GoogleLocalPost> getPagePosts(BusinessGoogleMyBusinessLocation page) {
        try {
            List<GoogleLocalPost> feedData = gmbService.getAllPosts(page.getBusinessId(), null);
            return feedData;
        } catch (BirdeyeSocialException ex) {
            log.info("[Social Report] Could not fetch Posts for gmb page {}", page , ex);
            return null;
        } catch (Exception e) {
            log.info("[Social Report] Something went wrong while fetching pages for gmb page {}", page ,e);
            return null;
        }
    }

    @Override
    public List<PageDetail> getPageDetails(List<String> pageIds) {
        List<PageDetail> pageDetails = new ArrayList<>();
        List<BusinessGoogleMyBusinessLocation> googleMyBusinessLocations = gmbRepo.findByLocationIdIn(pageIds);
        if (CollectionUtils.isNotEmpty(googleMyBusinessLocations)) {
            pageDetails.addAll(googleMyBusinessLocations.stream().map(s -> new PageDetail(s.getLocationId(),
                    s.getLocationName(), s.getPictureUrl(), s.getBusinessId())).collect(Collectors.toList()));
        }
        return pageDetails;
    }

    @Override
    public Feed getPostFeedDetails(SocialPostPublishInfo request, SocialPost socialPost) {
        return new Feed();
    }

    @Override
    public SocialScanEventDTO prepareSocialScanEventDto(String pageId) {
        BusinessGoogleMyBusinessLocation gmbPage = gmbRepo.findFirstByLocationIdAndIsValid(pageId, 1);
        SocialScanEventDTO scanEventDTO = new SocialScanEventDTO();
        scanEventDTO.setChannelPrimaryId(gmbPage.getId());
        scanEventDTO.setBusinessId(gmbPage.getBusinessId());
        scanEventDTO.setEnterpriseId(gmbPage.getEnterpriseId());
        scanEventDTO.setExternalId(gmbPage.getLocationId());
        scanEventDTO.setPageName(gmbPage.getLocationName());
        scanEventDTO.setSourceName(SocialChannel.GMB.getName());
        scanEventDTO.setSourceId(SocialChannel.GMB.getId());
        return scanEventDTO;
    }

    @Override
    public List<TikTokHashtagResponse.HashtagResponse> fetchRecommendedHashtags(String keyword, Integer businessId) throws Exception {
        return Collections.emptyList();
    }
}
