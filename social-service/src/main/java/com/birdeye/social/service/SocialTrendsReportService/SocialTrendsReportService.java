package com.birdeye.social.service.SocialTrendsReportService;

import com.birdeye.social.trends.*;

import java.io.IOException;
import java.util.List;

public interface SocialTrendsReportService {

    TrendsReportSummaryResponse getSLAReportSummary(TrendsReportRequest request);
    SocialTrendsReportResponse getResponseRateByChannel(TrendsReportRequest request);
    SocialTrendsReportResponse getResponseTimeByUser(TrendsReportRequest request) throws IOException;
    SocialTrendsReportResponse getResponseTimeByChannel(TrendsReportRequest request);
    TrendsOverviewResponseDTO getTrendsResponseOverview(TrendsReportRequest request);
    List<String> getPageIdsFromBidsForGivenChannels(List<Integer> businessIds, List<String> socialChannels);
    List<Object> getSLAExcelReport(TrendsReportRequest request) throws IOException;
    TrendsLocationLeaderboardResponse getLocationLeaderboardData(TrendsReportRequest request);
}
