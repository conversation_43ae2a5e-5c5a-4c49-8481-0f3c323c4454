/**
 * 
 */
package com.birdeye.social.service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialSetupAuditEnum;
import com.birdeye.social.dao.GMBLocationMigrationRepository;
import com.birdeye.social.dao.GoogleRefreshTokenRepo;
import com.birdeye.social.dto.DebeziumGMBRefreshToken;
import com.birdeye.social.dto.GMBAccountDTO;
import com.birdeye.social.dto.GMBLocationEntity;
import com.birdeye.social.dto.GoogleMyBusinessPagesDTO;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.entities.GoogleRefreshToken;
import com.birdeye.social.entities.StagingGoogleMyBusinessLocation;
import com.birdeye.social.external.response.google.GMBLocationPageUrl;
import com.birdeye.social.external.response.google.GMBLocationState;
import com.birdeye.social.external.response.google.GMBPageLocation;
import com.birdeye.social.external.response.google.GMBPageLocationResponse;
import com.birdeye.social.googleplus.GooglePlusService;
import com.birdeye.social.googleplus.GoogleProfileResponse;
import com.birdeye.social.googleplus.IGMBService;
import com.birdeye.social.model.ConsumerTokenAndSecret;
import com.birdeye.social.model.GMBAccount;
import com.birdeye.social.platform.dao.*;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.platform.entities.BusinessGMBLocation;
import com.birdeye.social.sro.GoogleAuthToken;
import com.birdeye.social.utils.BatchUtils;
import com.birdeye.social.utils.BusinessUtilsService;
import com.birdeye.social.utils.CoreUtils;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.birdeye.social.gmb.GMBMappingHandler.prepareLocationState;

/**
 * 
 * <AUTHOR> Kumar
 *
 */
@Service("gmbMigrationService")
public class GMBDataMigrationServiceImpl implements GMBDataMigrationService {

	private static Logger LOGGER = LoggerFactory.getLogger(GMBDataMigrationServiceImpl.class); //NOSONAR

	@Autowired
	private IGMBService gmbService;

	@Autowired
	private GMBLocationRepository gmbRepo;

	@Autowired
	private CommonService commonService;

	@Autowired
	private GoogleRefreshTokenRepo googleRefreshTokenRepo;

	@Autowired
	private GoogleAuthenticationService googleAuthenticationService;

	@Autowired
	private GoogleSocialAccountService googleSocialAccountService;

	@Autowired
	private GooglePlusService googlePlusService;

	@Autowired
	private BusinessUtilsService businessUtils;

	@Autowired
	private GMBLocationMigrationRepository migrationRepo;

	@Autowired
	private GoogleMyBusinessPageService googleMyBusinessPageServce;

	@Autowired
	private BusinessRepository businessRepo;

	@Autowired
	private BusinessGMBLocationRepository businessGMBPageRepo;

	@Autowired
	private BusinessAggregationRepository businessAggregationRepo;

	@Autowired
	private IBrokenIntegrationService brokenIntegrationService;

	@Override
	public void fetchGMBLocation(Integer refreshTokenId) {
		try {
			LOGGER.info("[GMB Setup] Adding GMB locations for refresh token: {}", refreshTokenId);
			ConsumerTokenAndSecret appCreds = commonService.getDefaultAppKeyAndToken("google");
			GoogleRefreshToken refreshToken = googleRefreshTokenRepo.findOne(refreshTokenId);
			String accessToken = googleAuthenticationService.generateFreshAccessToken(appCreds.getToken(), appCreds.getSecret(), refreshToken.getRefreshToken(), appCreds.getDomainName());
			GoogleAuthToken gmbAuth = new GoogleAuthToken();
			gmbAuth.setRefreshTokenId(refreshTokenId);
			gmbAuth.setAccess_token(accessToken);
			GoogleProfileResponse user = googlePlusService.getUserDetailsForGoogleUser(accessToken);
			if(user!=null)
			{
			gmbAuth.setUserId(user.getId());
			gmbAuth.setUserName(user.getName());
			}
			Map<String, GoogleMyBusinessPagesDTO> pagesMap = getGoogleMyBusinessPagesDTO(gmbAuth);
			if (pagesMap != null && CollectionUtils.isNotEmpty(pagesMap.values())) {
				// Save to DB
				saveNewGoogleMyBusinessPagesToStaging(pagesMap.values());
			}

		} catch (Exception exe) {
			LOGGER.error("Error {} while getting GMB locations", exe.getLocalizedMessage());
		}
	}

	private void saveNewGoogleMyBusinessPagesToStaging(Collection<GoogleMyBusinessPagesDTO> locations) {
		locations.parallelStream().forEach(loc -> savePages(loc));
	}

	private void savePages(GoogleMyBusinessPagesDTO loc) {
		StagingGoogleMyBusinessLocation businessLoc = prepareBusinessGoogleMyBusinessPages(loc);
		if (businessLoc != null) {
			LOGGER.info("Saving GMB location {} for account {}", businessLoc.getLocationId(), businessLoc.getAccountId());
			migrationRepo.saveAndFlush(businessLoc);
		}

	}

	private StagingGoogleMyBusinessLocation prepareBusinessGoogleMyBusinessPages(GoogleMyBusinessPagesDTO page) {
		StagingGoogleMyBusinessLocation businessPage = null;
		if (page != null) {
			businessPage = new StagingGoogleMyBusinessLocation();
			businessPage.setAccountId(page.getAccountId());
			businessPage.setAccountName(page.getAccountName());
			businessPage.setAccountStatus(page.getAccountStatus());
			businessPage.setAccountType(page.getAccountType());
			businessPage.setCoverImageUrl(page.getCoverImageUrl());
			businessPage.setGooglePlusId(page.getGooglePlusId());
			businessPage.setIsSelected(0);
			if(page.getPlaceId() != null) {
				businessPage.setIsValid(1);
			} else {
				businessPage.setIsValid(0);
			}

			businessPage.setIsVerified(page.getIsVerified());
			businessPage.setLocationId(page.getLocationId());
			businessPage.setLocationName(page.getLocationName());
			businessPage.setLocationState(page.getLocationState());
			businessPage.setLocationUrl(Constants.ACCOUNTS+page.getAccountId()+"/"+page.getLocationUrl());
			businessPage.setLocationMapUrl(page.getLocationMapUrl());
			businessPage.setPictureUrl(page.getPictureUrl());
			businessPage.setPlaceId(page.getPlaceId());
			businessPage.setPrimaryPhone(page.getPrimaryPhone());
			businessPage.setRefreshTokenId(page.getRefreshTokenId());
			businessPage.setSingleLineAddress(page.getSingleLineAddress());
			businessPage.setUserId(page.getUserId());
			businessPage.setUserName(businessPage.getUserName());
			businessPage.setWebsiteUrl(page.getWebsiteUrl());
		}
		return businessPage;
	}

	private Map<String, GoogleMyBusinessPagesDTO> getGoogleMyBusinessPagesDTO(GoogleAuthToken gmbAuth) {
		Map<String, GoogleMyBusinessPagesDTO> pagesMap = new HashMap<>();
		try {
			List<GMBAccount> gmbAccountList = gmbService.getAllGmbAccounts(gmbAuth.getAccess_token());
			if (CollectionUtils.isNotEmpty(gmbAccountList)) {
				// prepare Account DTO
				List<GMBAccountDTO> accounts = new ArrayList<>();
				gmbAccountList.stream().forEach(gmbacc -> accounts.add(prepareAccountDTO(gmbacc)));
				LOGGER.info("[GMB Setup] GMB Accounts  are: {}", gmbAccountList);
				accounts.stream().forEach(gmbAccount -> {
					Map<String, GoogleMyBusinessPagesDTO> data = processAllGMBLocationsForAccount(gmbAccount, gmbAuth);
					if (data != null && !data.isEmpty()) {
						pagesMap.putAll(data);
					} else {
						LOGGER.error("Account : {} has no locations ", gmbAccount);
					}
				});
			}
		} catch (Exception exe) {
			LOGGER.error("Error {} while getting GMB locations", exe.getLocalizedMessage());
		}
		return pagesMap;
	}


	
	private GMBAccountDTO prepareAccountDTO(GMBAccount gmbaccnt) {
		GMBAccountDTO account = new GMBAccountDTO();
		account.setAccountId(CoreUtils.getAccountOrLocationId(gmbaccnt.getName(), 1));
		account.setName(gmbaccnt.getName());
		account.setAccountName(gmbaccnt.getAccountName());
		account.setStatus(gmbaccnt.getVerificationState() != null ? gmbaccnt.getVerificationState() : null);
		account.setType(gmbaccnt.getType());
		return account;
	}

	private Map<String, GoogleMyBusinessPagesDTO> processAllGMBLocationsForAccount(GMBAccountDTO gmbAccount, GoogleAuthToken gmbAuth) {
		LOGGER.info("For GMB Account: {}, Fetching GMB location", gmbAccount);
		String nextPageToken = null;
		Map<String, GoogleMyBusinessPagesDTO> gmbLocations = new HashMap<>();
		do {
			GMBPageLocationResponse locationResponse = gmbService.getGMBLocations(gmbAuth.getAccess_token(), gmbAccount.getName(), nextPageToken);
			if (Objects.isNull(locationResponse) || CollectionUtils.isEmpty(locationResponse.getLocations())) {
				// if we got no locations
				gmbLocations = null;
				break;
			}
			// prepare GMBPageLocation data
			gmbLocations.putAll(preapreGoogleMyBusinessPagesDTO(gmbAccount, locationResponse.getLocations(), gmbAuth));
			nextPageToken = locationResponse.getNextPageToken();
			// Delay per batch to control concurrent hits. We need to use it since entire batch is moved to async
			try {
				Thread.sleep(200);
			} catch (InterruptedException e) {
				LOGGER.warn("[GMB Setup] Exception in thread sleep: ", e.getMessage());
			}

		} while (StringUtils.isNotBlank(nextPageToken));
		LOGGER.info("For GMB Account: {}, Total GMB locations: {}.", gmbAccount, (gmbLocations != null ? gmbLocations.size() : 0));
		return gmbLocations;
	}

	private Map<String, GoogleMyBusinessPagesDTO> preapreGoogleMyBusinessPagesDTO(GMBAccountDTO gmbAccount, List<GMBPageLocation> gmbLocations, GoogleAuthToken gmbAuth) {
		Map<String, GoogleMyBusinessPagesDTO> businessPages = new HashMap<>();
		gmbLocations.stream().forEach(location -> {
			GoogleMyBusinessPagesDTO businessPage = prepareLocationData(location);
			businessPage.setAccountId(gmbAccount.getAccountId());
			businessPage.setAccountStatus(gmbAccount.getStatus());
			businessPage.setAccountType(gmbAccount.getType());
			businessPage.setAccountName(gmbAccount.getAccountName());
			businessPage.setUserId(gmbAuth.getUserId());
			businessPage.setRefreshTokenId(gmbAuth.getRefreshTokenId());
			businessPages.put(businessPage.getLocationId(), businessPage);
		});
		return businessPages;
	}

	private GoogleMyBusinessPagesDTO prepareLocationData(GMBPageLocation location) {

		GoogleMyBusinessPagesDTO businessPage = new GoogleMyBusinessPagesDTO();
		businessPage.setLocationId(CoreUtils.getAccountOrLocationId(location.getName(), 1));
		businessPage.setLocationName(location.getTitle());
		businessPage.setWebsiteUrl(location.getWebsiteUri());
		businessPage.setPrimaryPhone(location.getPhoneNumbers().getPrimaryPhone());
		businessPage.setSingleLineAddress(location.getStorefrontAddress() != null ? location.getStorefrontAddress().getAddressInSingleLine() : null);
		businessPage.setPlaceId(location.getMetadata() != null ? location.getMetadata().getPlaceId() : null);
//		businessPage.setGooglePlusId(location.getLocationKey() != null ? location.getLocationKey().getPlusPageId() : null);
		businessPage.setLocationMapUrl(location.getMetadata() != null ? location.getMetadata().getMapsUri() : null);
		businessPage.setIsVerified(isLocationVerified(location.getMetadata()));
		businessPage.setLocationUrl(location.getName());
//		businessPage.setPictureUrl(location.getPhotos() != null ? location.getPhotos().getProfilePhotoUrl() : null);
//		businessPage.setCoverImageUrl(location.getPhotos() != null ? location.getPhotos().getCoverPhotoUrl() : null);
		businessPage.setLocationState(prepareLocationState(location.getMetadata(),location.getOpenInfo()));
		return businessPage;
	}

	private int isLocationVerified(GMBLocationPageUrl state) {
		if (Objects.nonNull(state) && Objects.nonNull(state.getHasVoiceOfMerchant()) && state.getHasVoiceOfMerchant()) {
			return 1;
		}
		return 0;
	}

	@Override
	public List<String> migrateAllGMBLocation() {
		// get all GMB Location
		List<GMBLocationEntity> existingGMbLocations = gmbRepo.getAllGMBLocations();
		return processMigration(existingGMbLocations);

	}

	private List<String> processMigration(List<GMBLocationEntity> existingGMbLocations) {
		List<String> unmatchLocation = new ArrayList<>();
		LOGGER.info("Existing GMB size {}", existingGMbLocations != null ? existingGMbLocations.size() : 0);
		if (CollectionUtils.isNotEmpty(existingGMbLocations)) {
			List<String> locationIds = new ArrayList<>();
			List<Integer> businessIds = new ArrayList<>();
			existingGMbLocations.stream().forEach(loc -> {
				locationIds.add(loc.getLocationId());
				businessIds.add(loc.getBusinessId());
			});
			LOGGER.info("Existing location size {}, Business size {}", locationIds.size(),businessIds.size());
			List<StagingGoogleMyBusinessLocation> adminGmbLocations = new ArrayList<>();
			BatchUtils.doInBatch(new ArrayList<>(locationIds), 500, data -> getGoogleMyBusinessLocation(data, adminGmbLocations));
			if (CollectionUtils.isNotEmpty(adminGmbLocations)) {
				Map<String, StagingGoogleMyBusinessLocation> adminGMbMap = adminGmbLocations.stream().collect(Collectors.toMap(adminLoc -> adminLoc.getLocationId(), adminLoc -> adminLoc,(x1, x2) -> x1));
				LOGGER.info("Staging location size {}", adminGMbMap==null?0:adminGMbMap.size());
				existingGMbLocations.parallelStream().forEach(gmbLoc -> migrationGMBLocationToBusinessAndEnterpriseLevel(unmatchLocation,gmbLoc, adminGMbMap));
			} else {
				LOGGER.error("No adminGmbLocations in StagingGoogleMyBusinessLocation");
			}
		} else {
			LOGGER.error("Existing GMB location is Empty");
		}
		return unmatchLocation;
	}

	private void getGoogleMyBusinessLocation(List<String> locationIds, List<StagingGoogleMyBusinessLocation> adminGmbLocations) {
		LOGGER.info("getGoogleMyBusinessLocation : locationIds {}", (locationIds != null ? locationIds.size() : 0));
		adminGmbLocations.addAll(getStagingGoogleMyBusinessLocation(locationIds));
	}

	private List<StagingGoogleMyBusinessLocation> getStagingGoogleMyBusinessLocation(List<String> locationIds) {
		return migrationRepo.getGoogleMyBusinessByLocationIds(locationIds);
	}

	private void migrationGMBLocationToBusinessAndEnterpriseLevel(List<String> unmatchLoc,GMBLocationEntity gmbLoc, Map<String, StagingGoogleMyBusinessLocation> adminGMbMap) {
		Business business = businessRepo.findFirstById(gmbLoc.getBusinessId());
		Business enterprise = businessUtils.getEnterpriseBusiness(business);
		BusinessGoogleMyBusinessLocation mybusinessLocation = prepareBusinessGoogleMyBusinessPages(adminGMbMap.get(gmbLoc.getLocationId()), enterprise);
		if (mybusinessLocation != null) {
			List<BusinessGoogleMyBusinessLocation> existingList=googleMyBusinessPageServce.getGMBPagesByLocationIds(Collections.singletonList(mybusinessLocation.getLocationId()));
			if(CollectionUtils.isEmpty(existingList))
			{
				googleMyBusinessPageServce.saveOrUpdateGMBRowPage(mybusinessLocation);
				saveGMBLocationMapping(business, mybusinessLocation);
			}
			else
			{
				LOGGER.error("GMB Location id {} is already mapped to enterprise {} ", mybusinessLocation.getLocationId(),existingList.get(0).getEnterpriseId());
			}
		} else {
			unmatchLoc.add(gmbLoc.getLocationId());
			LOGGER.info("Location {} does not found in adminGMbMap", gmbLoc.getLocationId());
		}
	}

	private void saveGMBLocationMapping(Business business, BusinessGoogleMyBusinessLocation mybusinessLocation) {
		List<BusinessGMBLocation> existingPages = businessGMBPageRepo.findByLocationId(mybusinessLocation.getLocationId());
		if (CollectionUtils.isNotEmpty(existingPages)) {
			LOGGER.error("GMB Location id {} is already mapped to business {} ", mybusinessLocation.getLocationId(),existingPages.get(0).getBusinessId());
			
		}
		else
		{
		BusinessGMBLocation gmbPage = getBusinessGMBPage(mybusinessLocation);
		if (gmbPage != null) //NOSONAR
		{
			LOGGER.info("Saving the location {} mapping with business {}", mybusinessLocation.getLocationId(), business.getId());
			gmbPage.setBusinessId(business.getId());
			gmbPage.setEnabled(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSocialAutoPostFlag());
			businessGMBPageRepo.saveAndFlush(gmbPage);
		}
	}
	}

	private BusinessGMBLocation getBusinessGMBPage(BusinessGoogleMyBusinessLocation page) {
		BusinessGMBLocation gmbPage = new BusinessGMBLocation();
		gmbPage.setRefreshTokenId(page.getRefreshTokenId());
		gmbPage.setLocationId(page.getLocationId());
		gmbPage.setLocationName(page.getLocationName());
		gmbPage.setLocationUrl(page.getLocationUrl());
		gmbPage.setLocationMapUrl(page.getLocationMapUrl());
		gmbPage.setProfileImageUrl(page.getPictureUrl() != null ? page.getPictureUrl() : page.getCoverImageUrl());
		if(page.getPlaceId()!=null) {
			gmbPage.setIsValid(page.getIsValid());
		} else {
			gmbPage.setIsValid(0);
		}

		gmbPage.setAddress(page.getSingleLineAddress());
		return gmbPage;
	}

	private BusinessGoogleMyBusinessLocation prepareBusinessGoogleMyBusinessPages(StagingGoogleMyBusinessLocation page, Business business) {
		BusinessGoogleMyBusinessLocation businessPage = null;
		if (page != null) {
			businessPage = new BusinessGoogleMyBusinessLocation();
			businessPage.setAccountId(page.getAccountId());
			businessPage.setAccountName(page.getAccountName());
			businessPage.setAccountStatus(page.getAccountStatus());
			businessPage.setAccountType(page.getAccountType());
			businessPage.setCoverImageUrl(page.getCoverImageUrl());
			businessPage.setGooglePlusId(page.getGooglePlusId());
			businessPage.setIsSelected(1);
			GMBLocationState locationState = JSONUtils.fromJSON(page.getLocationState(), GMBLocationState.class);
			if(page.getPlaceId() != null && locationState.getIsDisabled() != true) {
				businessPage.setIsValid(1);
			} else {
				businessPage.setIsValid(0);
			}
			businessPage.setIsVerified(page.getIsVerified());
			businessPage.setLocationId(page.getLocationId());
			businessPage.setLocationName(page.getLocationName());
			businessPage.setLocationState(page.getLocationState());
			businessPage.setLocationUrl(page.getLocationUrl());
			businessPage.setLocationMapUrl(page.getLocationMapUrl());
			businessPage.setPictureUrl(page.getPictureUrl());
			businessPage.setPlaceId(page.getPlaceId());
			businessPage.setPrimaryPhone(page.getPrimaryPhone());
			businessPage.setRefreshTokenId(page.getRefreshTokenId());
			businessPage.setSingleLineAddress(page.getSingleLineAddress());
			businessPage.setUserId(page.getUserId());
			businessPage.setUserName(businessPage.getUserName());
			businessPage.setWebsiteUrl(page.getWebsiteUrl());
			businessPage.setEnterpriseId(business.getBusinessId());
			if(businessPage.getIsValid().equals(0)) {
//				brokenIntegrationService.pushForBrokenIntegration(businessPage.getEnterpriseId(),"GMB");
				commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(businessPage),
						businessPage.getUserId(), businessPage.getBusinessId(), businessPage.getEnterpriseId());
			}
		}
		return businessPage;
	}

	@Override
	public List<String> migrateEnterpriseGMBdata(Long enterpriseId) {
		// Get all the business for enterprise
		Business business = businessRepo.findByBusinessId(enterpriseId);
		List<Integer> businessIds = businessUtils.getBusinessLocationsForEnterprise(business, null);

		// get all the aggregation for business
		List<Integer> aggregationIds = businessAggregationRepo.findByBusinessIdForGoogle(businessIds);
		// get all GMB Location
		List<GMBLocationEntity> existingGMbLocations = gmbRepo.getAllGMBLocationsByAggregationIds(aggregationIds);
		return processMigration(existingGMbLocations);

	}

	@Override
	public void checkGmbTokenAndUpdate(List<Integer> rawIds, Boolean sync) {
		googleSocialAccountService.checkGmbTokenAndUpdate(rawIds,sync);
	}

	@Override
	public void migrateGmbPermission(List<Integer> rawIds, Boolean sync) {
		googleSocialAccountService.migrateGmbPermission(rawIds,sync);
	}

	private GoogleRefreshToken convertToGMBToken(DebeziumGMBRefreshToken.GoogleRefreshTokenDTO googleRefreshTokenDTO) {
		GoogleRefreshToken googleRefreshToken = new GoogleRefreshToken();
		googleRefreshToken.setId(googleRefreshTokenDTO.getId());
		googleRefreshToken.setClientCredentialsId(googleRefreshTokenDTO.getClient_cred_id());
		googleRefreshToken.setRefreshToken(googleRefreshTokenDTO.getRefresh_token());
		googleRefreshToken.setType(googleRefreshTokenDTO.getType());
		googleRefreshToken.setChannel(googleRefreshTokenDTO.getChannel());
		googleRefreshToken.setDomainId(googleRefreshTokenDTO.getDomain_id());
		googleRefreshToken.setBusinessId(googleRefreshTokenDTO.getBusiness_id());
		googleRefreshToken.setIsValid(googleRefreshTokenDTO.getIs_valid());
		googleRefreshToken.setGoogleUserId(googleRefreshTokenDTO.getGoogle_user_id());
		googleRefreshToken.setLastScannedOn(googleRefreshTokenDTO.getLast_scanned_on());
		googleRefreshToken.setGoogleUserName(googleRefreshTokenDTO.getGoogle_user_name());
		googleRefreshToken.setUserImageUrl(googleRefreshTokenDTO.getUser_image_url());
		googleRefreshToken.setUserProfileUrl(googleRefreshTokenDTO.getUser_profile_url());
		return googleRefreshToken;
	}

	@Override
	public void migrateGoogleRefreshToken(DebeziumGMBRefreshToken debeziumGMBRefreshToken) {
		LOGGER.info("Received DebeziumGMBRefreshToken object: {}", debeziumGMBRefreshToken);
		if(Objects.nonNull(debeziumGMBRefreshToken) && Objects.nonNull(debeziumGMBRefreshToken.getPayload()) && Objects.nonNull(debeziumGMBRefreshToken.getPayload().getAfter())) {
			DebeziumGMBRefreshToken.GoogleRefreshTokenDTO googleRefreshTokenDTO = debeziumGMBRefreshToken.getPayload().getAfter();
			if(Objects.nonNull(googleRefreshTokenDTO.getId())) {
				LOGGER.info("Saving GoogleRefreshToken for id: {}", googleRefreshTokenDTO.getId());
				GoogleRefreshToken googleRefreshToken = convertToGMBToken(googleRefreshTokenDTO);
				googleRefreshTokenRepo.saveAndFlush(googleRefreshToken);
			} else {
				LOGGER.info("cannot save GoogleRefreshToken without id: {}", googleRefreshTokenDTO);
			}
		}
	}
}
