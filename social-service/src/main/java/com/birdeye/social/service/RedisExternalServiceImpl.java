package com.birdeye.social.service;

import java.util.*;
import java.util.concurrent.TimeUnit;

import com.birdeye.social.model.RateLimitDomainInfoDTO;
import com.birdeye.social.model.RateLimitingDTO;
import com.birdeye.social.model.RateLimitingMasterDTO;
import com.birdeye.social.utils.JSONUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.*;

import org.springframework.data.redis.core.ZSetOperations.TypedTuple;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 *
 */
@Service
public class RedisExternalServiceImpl implements IRedisExternalService {

	private static final Logger LOG = LoggerFactory.getLogger(RedisExternalServiceImpl.class);
	private static final String BUSINESS_GMB_ACCOUNT_MAPPING = "BUSINESS_GMB_ACCOUNT_MAPPING";

	@Autowired
	@Qualifier("redisTemplate")
	private RedisTemplate redisTemplate;

	@Resource(name="redisTemplate")
	private ValueOperations<String, Integer> valueOps;

	@Resource(name = "redisTemplate")
	private ListOperations<String, Integer> listOps;

@Autowired
	private ObjectMapper objectMapper;


	/**
	 * Method to set value in redis
	 * 
	 * @param key
	 * @param value
	 */
	@Override
	public void set(String key, String value) {
		redisTemplate.boundHashOps(key).put(key, value);
	}

	@Override
	public void set(String key, Long value) {
		redisTemplate.boundHashOps(key).put(key,value);
	}

	@Override
	public void set(String key, List<Integer> value) {
		redisTemplate.boundHashOps(key).put(key, value);
	}


	@Override
	public void delete(String key) {
		redisTemplate.delete(key);
	}

	/**
	 * Method to get value from redis using key
	 *
	 * @param key
	 * @return Optional<Object>
	 */
	@Override
	public Optional<Object> get(String key) {
		return Optional.ofNullable(redisTemplate.boundHashOps(key).get(key));
	}

	@Override
	public Integer decrement(String key) {
		return Math.toIntExact(redisTemplate.boundHashOps(key).increment(key, -1L));
	}

	/**
	 * Method to populate daily aggregation priority queue with businessIds
	 * 
	 * @param priorityQueue
	 * @params values
	 */

	@Override
	public void populatePriorityQueue(String priorityQueue, Set<TypedTuple<Object>> values) {
		if (CollectionUtils.isEmpty(values)) {
			LOG.warn("Received request to populate :{} priority queue with 0 values", priorityQueue);
			return;
		}
		try {
			redisTemplate.boundZSetOps(priorityQueue).add(values);
		} catch (Exception e) {
			LOG.error("Some error occurred while trying to fill :{} priority queue", priorityQueue, e);
		}
	}

	/**
	 * Method to get size of daily aggregation priority queue
	 * 
	 * @param priorityQueue
	 */
	@Override
	public Long getPriorityQueueSize(String priorityQueue) {

		try {
			return redisTemplate.boundZSetOps(priorityQueue).size();
		} catch (Exception e) {
			LOG.error("Some error while trying to fetch size of priority queue with name :{}", priorityQueue, e);
			return -1L;
		}
	}

	/**
	 * Method to fetch refresh token Ids for the given count from priority queue
	 * Note : Range here denotes the number of businessIds
	 * 
	 * @param priorityQueue
	 * @param count
	 */
	public List<Integer> getRefreshtokenIdListFromPQueue(String priorityQueue, Integer count) {
		List<Integer> businessIds = new ArrayList<>();

		if (count <= 0)
			return businessIds;

		try {

			Set<Object> redisSet = redisTemplate.boundZSetOps(priorityQueue).range(0, count - 1);
			Iterator<Object> itr = redisSet.iterator();

			while (itr.hasNext())
				businessIds.add((Integer) itr.next());

			return businessIds;

		} catch (Exception e) {
			LOG.error("Some error occurred while trying to fetch list" + " of businessIds from :{} priority queue",
					priorityQueue, e);
			return businessIds;
		}
	}

	/**
	 * Method to fetch Long type for the given count from priority queue
	 * Note : Range here denotes the number of tokens
	 *
	 * @param priorityQueue
	 * @param count
	 */
	public List<Long> getTokenListFromPQueue(String priorityQueue, Integer count) {
		List<Long> cachedEntryList = new ArrayList<>();

		if (count <= 0)
			return cachedEntryList;

		try {

			Set<Object> redisSet = redisTemplate.boundZSetOps(priorityQueue).range(0, count - 1);
			Iterator<Object> itr = redisSet.iterator();

			while (itr.hasNext())
				cachedEntryList.add( ((Number)itr.next()).longValue());

			return cachedEntryList;

		} catch (Exception e) {
			LOG.error("Some error occurred while trying to fetch list" + " of businessIds from :{} priority queue",
					priorityQueue, e);
			return cachedEntryList;
		}
	}

	/**
	 * Method to remove businessIds from daily aggregation priority queue for given
	 * range Note : Range here denotes the number of elements
	 * 
	 * @param priorityQueue
	 * @param range
	 */
	@Override
	public void priorityQueueRemoveRangeByRank(String priorityQueue, Integer range) {
		try {
			redisTemplate.boundZSetOps(priorityQueue).removeRange(0, range - 1);
		} catch (Exception e) {
			LOG.error("Some error occurred while  trying to " + "remove businessIds from priorityQueue", e);
		}

	}
	
	@Override
	public List<Integer> popFromRedis(String queueName, int count) {
		List<Integer> refreshTokenIdList = getRefreshtokenIdListFromPQueue(queueName, count);
		priorityQueueRemoveRangeByRank(queueName, count);
		return refreshTokenIdList;
	}

	@Override
	public List<Long> popLongFromRedis(String queueName, int count) {
		List<Long> refreshTokenIdList = getTokenListFromPQueue(queueName, count);
		priorityQueueRemoveRangeByRank(queueName, count);
		return refreshTokenIdList;
	}

	@Override
	public void putIfAbsent(String key,Integer value) {
		redisTemplate.boundHashOps(key).put(key,value);
	}

	@Override
	public boolean isKeyPresent(String key) {
		return redisTemplate.boundHashOps(key).hasKey(key);
	}


	@Override
	  public void fillPriorityQueue(List<Long> snapshot,String priorityQueue) {

		  LOG.info("Received call to fill  priority queue with tokens");

			populatePriorityQueue(
					priorityQueue,
					createTypedTupleLongSet(snapshot)
					);

			Long queueSize = getPriorityQueueSize(priorityQueue);

		  LOG.info("Completed recording daily refresh token Id list snapshot. "
					+ "Current priority queue size is :{}",queueSize);
		}

	  /**
		 * Method to create Set of type Set<TypedTuple<Object>> which is needed by sorted set add operation in Redis
		 * @param snapshot
		 * @return
		 */

		private Set<TypedTuple<Object>> createTypedTupleLongSet(List<Long> snapshot){
			Set<TypedTuple<Object>> set = new HashSet<TypedTuple<Object>>();

			for(int i = 0 ; i < snapshot.size() ; i ++) {
				set.add(
						new DefaultTypedTuple<Object>(snapshot.get(i),
								snapshot.get(i).doubleValue())
						);
			}
			return set;
		}

	@Override
	public Boolean saveToRedisByBusinessGetPageRequestId(String requestId, Set<String> accountSet) {
		if(CollectionUtils.isEmpty(accountSet)){
			LOG.info("Received request for storing empty account for businessRequestId:{}", requestId);
			return false;
		}

		LOG.info("Received request for storing account ids for businessRequestId:{}", requestId);
		String key = "";
		key = keyForBusinessGetPageReqId(requestId);

		try{
			redisTemplate.delete(key);
			redisTemplate.boundSetOps(key).add(accountSet.toArray(new String[accountSet.size()]));
			LOG.info("GMB account added to the redis for businessRequestId:{}", requestId);
			return true;
		} catch (Exception e) {
			LOG.error("Some error occurred storing GMB account ids by businessRequestId", e);
			return false;
		}
	}

	@Override
	public Boolean checkIfAllProcessedAfterCurrentForGMB(String businessGetPageRequestId, String accountId) {
		String key = keyForBusinessGetPageReqId(businessGetPageRequestId);
		return (Boolean) redisTemplate.execute(new SessionCallback<Boolean>() {
			@SuppressWarnings("unchecked")
			@Override
			public Boolean execute(RedisOperations operations) throws DataAccessException {
				operations.multi();
				operations.boundSetOps(key).size();
				operations.boundSetOps(key).remove(accountId);
				operations.boundSetOps(key).size();
				LOG.info("Deleting account id :{} from redis",accountId);
				List executions = operations.exec();
				Long elementsBeforeRemoval = (Long) executions.get(0);
				Long elementsRemoved = (Long) executions.get(1);
				Long sizeAfterRemoval = (Long) executions.get(2);
				LOG.info("Elements removed :{} from redis, size after removal:{},size before removal:{} ,account id :{}",elementsRemoved,sizeAfterRemoval,elementsBeforeRemoval,accountId);
				if (elementsRemoved > 0 && sizeAfterRemoval == 0) {
					return true;
				}
				return false;
			}
		});
	}




	private String keyForBusinessGetPageReqId(String requestId) {
		return BUSINESS_GMB_ACCOUNT_MAPPING+":#"+requestId;
	}


	@Override
	public void setWithExipry(String key, String value, long expirationTime) {
		Optional<Object> keyData= get(key);
		if(keyData.isPresent()){
			redisTemplate.delete(key);
		}
		set(key,value);
		redisTemplate.expire(key, expirationTime, TimeUnit.SECONDS);
	}

	@Override
	public void setKeyAndValue(String key,String value){
		String finalValue = "";
		Optional<Object> keyData= get(key);
		if(keyData.isPresent()){
			finalValue = keyData.get().toString();
			finalValue = finalValue+","+value;
		}else {
			finalValue = value;
		}
		set(key,finalValue);
	}

	@Override
	public List<String> popFromQueue(String pattern) {
		Set<String> matchingKeys = redisTemplate.keys(pattern);
		List<String> matchingData= new ArrayList<>();
		for (String key : matchingKeys) {

			Optional<Object> value = get(key);
			if (value.isPresent()) {
				JSONObject obj = new JSONObject();
				try {
					obj.put(key, value.get());
					matchingData.add(obj.toString());
			} catch(JSONException e){
				LOG.error("JSONException while setting key value pair :: {}", e.getMessage());
			}
		}
		}
		return matchingData;

}

	@Override
	public RateLimitingDTO getRateLimitingDto(String key) {
		Optional<Object> rateLimitingOptional = get(key);
		return rateLimitingOptional.map(o -> JSONUtils.fromJSON((String) o, RateLimitingDTO.class)).orElse(null);
	}

	/*@Override
	public RateLimitingMasterDTO getRateLimitingSocialApiDto(String key) {
		Optional<Object> rateLimitingOptional = get(key);
		return rateLimitingOptional.map(o -> JSONUtils.fromJSON((String) o, RateLimitingMasterDTO.class)).orElse(null);
	}*/
	public List<Integer> removeElementFromList(String key, Integer value) {
		List<Integer> updatedList = new ArrayList<>();
		try {
			listOps.remove(key, 0, value);
			updatedList = listOps.range(key, 0, -1);
		} catch (Exception e) {
			LOG.info("[removeElementFromList] Exception occurred for key: {} and value: {}, exception: {}", key, value, e.getMessage());
		}
		return updatedList;
	}

	public void setListData(String key, List<Integer> dataList) {
		try {
			redisTemplate.delete(key); // clear existing list if any
			listOps.rightPushAll(key, dataList); // Push all elements to the list
		} catch (Exception e) {
			LOG.info("[setListData] Exception occurred for key: {} and dataList: {}", key, dataList.toString());
		}
	}

	public List<Integer> getList(String key) {
		List<Integer> list = new ArrayList<>();
		try {
			list = listOps.range(key, 0, -1); // Get all elements in the list
		} catch (Exception e) {
			LOG.info("[getList] Exception occurred for key: {}", key);
		}
		return list;
	}

	@Override
	public RateLimitDomainInfoDTO getRateLimitingSocialDomainDto(String key) {
		Optional<Object> rateLimitingOptional = get(key);
		return rateLimitingOptional.map(o -> JSONUtils.fromJSON((String) o, RateLimitDomainInfoDTO.class)).orElse(null);
	}
}
