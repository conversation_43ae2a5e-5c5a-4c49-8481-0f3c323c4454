package com.birdeye.social.service;

import com.birdeye.social.dao.LinkedinRefreshTokenRepo;
import com.birdeye.social.entities.LinkedinRefreshToken;
import com.birdeye.social.linkedin.AccessTokenInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class SocialLinkedinRefreshTokenServiceImpl implements ISocialLinkedinRefreshTokenService{

    @Autowired
    private LinkedinRefreshTokenRepo linkedinRefreshTokenRepo;

    @Override
    public LinkedinRefreshToken createOrUpdateLinkedinRefreshToken(Integer userId, String linkedinProfileId, AccessTokenInfo accessTokenInfo) {
        LinkedinRefreshToken linkedinRefreshToken = null;
        if(Objects.isNull(linkedinProfileId)){
            linkedinRefreshToken = new LinkedinRefreshToken();
        }else{
            linkedinRefreshToken = fetchLinkedinRefreshTokenByLinkedinProfileId(linkedinProfileId);
            if(Objects.isNull(linkedinRefreshToken)){
                linkedinRefreshToken = new LinkedinRefreshToken();
            }
        }
        linkedinRefreshToken.setUserId(userId);
        linkedinRefreshToken.setRefreshToken(accessTokenInfo.getRefreshToken());
        linkedinRefreshToken.setLinkedinProfileId(linkedinProfileId);
        linkedinRefreshToken.setExpiresOn(accessTokenInfo.getRefreshTokenExpiresIn());
        return linkedinRefreshTokenRepo.saveAndFlush(linkedinRefreshToken);
    }

    @Override
    public LinkedinRefreshToken updateLinkedinRefreshToken(LinkedinRefreshToken linkedinRefreshToken) {
        return linkedinRefreshTokenRepo.saveAndFlush(linkedinRefreshToken);
    }

    @Override
    @Transactional
    public LinkedinRefreshToken fetchLinkedinRefreshTokenByUserId(Integer userId) {
        return linkedinRefreshTokenRepo.findByUserId(userId);
    }

    @Override
    public LinkedinRefreshToken fetchLinkedinRefreshTokenByLinkedinProfileId(String profileId) {
        return linkedinRefreshTokenRepo.findByLinkedinProfileId(profileId);
    }

    @Override
    @Transactional
    public LinkedinRefreshToken findById(Integer id) {
        return linkedinRefreshTokenRepo.findOne(id);
    }

    @Override
    public List<LinkedinRefreshToken> findByIdIn(List<Integer> genericIntegers) {
        return linkedinRefreshTokenRepo.findByIdIn(genericIntegers);
    }

    @Override
    public void save(LinkedinRefreshToken token) {
        linkedinRefreshTokenRepo.saveAndFlush(token);
    }

    @Override
    public Page<LinkedinRefreshToken> findByExpiresOnGreaterThan(Date date, PageRequest pageRequest) {
        return linkedinRefreshTokenRepo.findByExpiresOnGreaterThan(date,pageRequest);
    }
}
