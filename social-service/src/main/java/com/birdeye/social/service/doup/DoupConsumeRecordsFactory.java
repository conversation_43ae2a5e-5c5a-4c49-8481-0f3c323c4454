package com.birdeye.social.service.doup;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class DoupConsumeRecordsFactory {
    @Autowired
    List<DoupConsumeRecords> doupConsumeRecordsList;


    public Optional<DoupConsumeRecords> getDoupConsumeRecords(String channel) {
        Map<String, DoupConsumeRecords> operationMap = new HashMap<>();
        for(DoupConsumeRecords doupConsumeRecords : doupConsumeRecordsList){
            operationMap.put(doupConsumeRecords.channelName(), doupConsumeRecords);
        }
        return Optional.ofNullable(operationMap.get(channel.toLowerCase()));
    }
}
