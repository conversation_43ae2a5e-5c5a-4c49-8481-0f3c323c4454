package com.birdeye.social.service.Youtube;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.dto.TokensAndUrlAuthData;
import com.birdeye.social.entities.BusinessYoutubeChannel;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.google.response.GoogleBaseResponse;
import com.birdeye.social.google.response.GoogleErrorResponse;
import com.birdeye.social.model.Youtube.YoutubeCategory.YoutubeCategoryItems;
import com.birdeye.social.model.Youtube.YoutubePlaylist.YoutubePlaylistItems;
import com.birdeye.social.service.GoogleAuthenticationService;
import com.birdeye.social.sro.GoogleAuthToken;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import com.birdeye.social.youtube.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.api.client.auth.oauth2.BearerToken;
import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.youtube.YouTube;
import com.google.api.services.youtube.model.Comment;
import com.google.api.services.youtube.model.CommentSnippet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.net.URI;
import java.security.GeneralSecurityException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 *
 */
@Service
public class YoutubeServiceImpl implements YoutubeService {

	@Autowired
	private GoogleAuthenticationService googleAuthenticationService;

	@Autowired
	private YoutubeVideoUpload youtubeUploadService;

	@Autowired
	private YoutubeObjectConverter youtubeObjectConverter;
	
	private static final Logger	logger	= LoggerFactory.getLogger(YoutubeServiceImpl.class);
	
	public static final String	URL		= "https://www.googleapis.com/youtube/v3/search";
	public static final String CATEGORY_URL = "https://youtube.googleapis.com/youtube/v3/videoCategories";
	public static final String PLAYLIST_URL = "https://www.googleapis.com/youtube/v3/playlists";
	private static final Long EXPIRE_TIME_IN_SECONDS = 1661952113L;
	private static final JsonFactory JSON_FACTORY = JacksonFactory.getDefaultInstance();
	public static final String COMMENTS_THREADS_URL = "https://www.googleapis.com/youtube/v3/commentThreads";
	public static final String COMMENTS_URL = "https://www.googleapis.com/youtube/v3/comments";
	public static final String VIDEO_URL = "https://www.googleapis.com/youtube/v3/videos";
	public static final String REJECTED = "rejected";
	public static final String PUBLISHED = "published";

	public static final String PAGE_INVALID_SUSPENDED_ERROR_MESSAGE = "The YouTube account of the authenticated user is suspended.";


	@Autowired
	@Qualifier("socialRestTemplate")
	private RestTemplate		restTemplate;
	
	@Override
	public List<YoutubeActivity> getActivities(String keyword) {
		logger.info("Inside getActivities for keyword:{}", keyword);
		
		List<YoutubeActivity> youtubeActivityList = null;
		ResponseEntity<YoutubeResponse> responseEntity = null;
		
		MultiValueMap<String, String> parameters = new LinkedMultiValueMap<>();
		parameters.add("part", "snippet");
		parameters.add("order", "date");
		parameters.add("q", keyword);
		parameters.add("maxResults", "50");
		parameters.add("type", "video");
		parameters.add("key", "AIzaSyBPuwnj_EpX1smiCehugi0P6_RymOZ16Yk");
		
		// Since '#' is a general delimited character as per RestTemplate, So it is not being encoded internally by RestTemplate.
		// Hence, we chose to use uri instead of url.
		URI uri = UriComponentsBuilder.fromHttpUrl(URL).queryParams(parameters).build().encode().toUri();
		try {
			logger.info("Calling Youtube api for uri: {}", uri);
			responseEntity = restTemplate.getForEntity(uri, YoutubeResponse.class);
			logger.info("Received Response from Youtube api for URI:{} with status :{}", uri, responseEntity.getStatusCode());
		} catch (HttpStatusCodeException e) {
			if (e.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling getActivities for youtube for URI {} and exception {}", uri, e.getResponseBodyAsString());
//				throw new ExternalAPIException(ExternAPIErrorCode.INTERNAL_SERVER_ERROR, e.getStatusCode().getReasonPhrase());
			}
			if (e.getStatusCode().is4xxClientError()) {
				logger.error("RestClientException while calling getActivities for youtube for URI {} and exception {}", uri, e.getResponseBodyAsString());
//				throw new ExternalAPIException(ExternAPIErrorCode.CLIENT_ERROR, e.getStatusCode().getReasonPhrase());
			}
		} catch (Exception e) {
			logger.error("Error occurred while calling Youtube api for uri {}, exception : {}", uri, e);

		}
		
		if (responseEntity != null && responseEntity.getBody() != null) {
			youtubeActivityList = responseEntity.getBody().getItems();
		}
		
		return youtubeActivityList;
	}
	
	//TODO: Why multiple keys?
	private static final String YT_KEY="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";
	private static final String YT_CHANNEL_URL="https://www.googleapis.com/youtube/v3/channels";
	//?part=snippet";&id={userId}&key={key}"
	
	public Map<?, ?> getChannelInformation(String channelId) {
		// Accessing channel info i.e. channel name, channel image etc.
		MultiValueMap<String, String> parameters = new LinkedMultiValueMap<>();
		parameters.add("part", "snippet");
		parameters.add("id", channelId);
		parameters.add("key", YT_KEY);

		URI uri = UriComponentsBuilder.fromHttpUrl(YT_CHANNEL_URL).queryParams(parameters).build().encode().toUri();
		ResponseEntity<JsonNode> responseEntity = null;
		Map<?, ?> result = Collections.emptyMap();
		try {
			responseEntity = restTemplate.getForEntity(uri, JsonNode.class);
			logger.info("Received Response from Youtube api for URI:{} with status :{}", uri,
					responseEntity.getStatusCode());
			JsonNode response = responseEntity.getBody();
			// Channel Information 
			JsonNode items = response.get("items");
			if (items != null) {
				if (items instanceof ArrayNode) {
					JsonNode item = items.get(0);
					if (item != null) {
						ObjectMapper mapper = new ObjectMapper();
						result = mapper.convertValue(item.get("snippet"), Map.class);
						logger.info("Youtube channel#{} info : {}",channelId, result);
					}
				}
			}
		} catch (Exception e) {
			logger.error("Error calling in channel API", e);
		}
		return result;
	}

	public static Credential createCredential(TokensAndUrlAuthData authData) {
		try {
			final NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
			return new Credential.Builder(BearerToken.authorizationHeaderAccessMethod())
					.setTransport(httpTransport)
					.setJsonFactory(JSON_FACTORY)
					.setTokenServerEncodedUrl("https://oauth2.googleapis.com/token")
					.build()
					.setAccessToken(authData.getAccessToken())
					.setRefreshToken(authData.getRefreshToken())
					.setExpiresInSeconds(EXPIRE_TIME_IN_SECONDS);
		}catch (GeneralSecurityException e){
			throw new BirdeyeSocialException(ErrorCodes.YOUTUBE_GENERAL_SECURITY_EXCEPTION,e.getLocalizedMessage());
		}catch (IOException e){
			throw new BirdeyeSocialException(ErrorCodes.YOUTUBE_IO_EXCEPTION,e.getLocalizedMessage());
		}
	}

	@Override
	public YouTube getService(TokensAndUrlAuthData tokensAndUrlAuthData) {
		try {
			final NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
			Credential credential = createCredential(tokensAndUrlAuthData);
			return new YouTube.Builder(httpTransport,JSON_FACTORY, credential).setApplicationName("POC").build();
		}catch (GeneralSecurityException e){
			throw new BirdeyeSocialException(ErrorCodes.YOUTUBE_GENERAL_SECURITY_EXCEPTION,e.getLocalizedMessage());
		}catch (IOException e){
			throw new BirdeyeSocialException(ErrorCodes.YOUTUBE_IO_EXCEPTION,e.getLocalizedMessage());
		}
	}

	@Override
	@Cacheable(value = "ytCategory", key = "#regionCode" , unless = "#result==null")
	public YoutubeCategoryItems getCategoriesByRegion(String accessToken, String regionCode) throws Exception {
		MultiValueMap<String, String> parameters = new LinkedMultiValueMap<>();
		parameters.add("part", "snippet");
		parameters.add("regionCode",regionCode);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.set(HttpHeaders.AUTHORIZATION,"Bearer "+accessToken);
		httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		HttpEntity<Void> entity = new HttpEntity<>(httpHeaders);
		URI uri = UriComponentsBuilder.fromHttpUrl(CATEGORY_URL).queryParams(parameters).build().encode().toUri();
		try {
			ResponseEntity<YoutubeCategoryItems> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,entity,YoutubeCategoryItems.class);
			return responseEntity.getBody();
		}catch (HttpStatusCodeException e) {
			GoogleBaseResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), GoogleBaseResponse.class);
			if (e.getStatusCode().is5xxServerError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR,errorResponse.getMessage());
				}
			}
			if(e.getStatusCode().is4xxClientError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					throw new BirdeyeSocialException(errorResponse.getCode(),errorResponse.getMessage());
				}
			}
		}catch (Exception e){
			throw new Exception("Unable to get reason of failure"+regionCode);
		}
		return null;
	}

	@Override
	public YoutubePlaylistItems getPlaylistForChannelId(String channelId, String accessToken) throws Exception {
		MultiValueMap<String, String> parameters = new LinkedMultiValueMap<>();
		parameters.add("part", "snippet,contentDetails");
		parameters.add("channelId",channelId);
		parameters.add("maxResults","25");
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.set(HttpHeaders.AUTHORIZATION,"Bearer "+accessToken);
		httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		HttpEntity<Void> entity = new HttpEntity<>(httpHeaders);
		URI uri = UriComponentsBuilder.fromHttpUrl(PLAYLIST_URL).queryParams(parameters).build().encode().toUri();
		try {
			ResponseEntity<YoutubePlaylistItems> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,entity,YoutubePlaylistItems.class);
			return responseEntity.getBody();
		}catch (HttpStatusCodeException e) {
			GoogleBaseResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), GoogleBaseResponse.class);
			if (e.getStatusCode().is5xxServerError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR,errorResponse.getMessage());
				}
			}
			if(e.getStatusCode().is4xxClientError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					throw new BirdeyeSocialException(errorResponse.getCode(),errorResponse.getMessage());
				}
			}
		}catch (Exception e){
			throw new Exception("Unable to get reason of failure"+channelId);
		}
		return null;
	}

	@Override
	public YoutubeCommentThreadResponse getYTComments(String accessToken, String channelId, String nextToken) throws Exception {
		MultiValueMap<String, String> parameters = new LinkedMultiValueMap<>();
		parameters.add("part", "id,replies,snippet");
		parameters.add("allThreadsRelatedToChannelId",channelId);
		parameters.add("maxResults", String.valueOf(100));
		if(StringUtils.isNotEmpty(nextToken)) {
			parameters.add("pageToken",nextToken);
		}
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.set(HttpHeaders.AUTHORIZATION,"Bearer "+accessToken);
		httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		HttpEntity<Void> entity = new HttpEntity<>(httpHeaders);
		URI uri = UriComponentsBuilder.fromHttpUrl(COMMENTS_THREADS_URL).queryParams(parameters).build().encode().toUri();
		try {
			ResponseEntity<YoutubeCommentThreadResponse> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,entity,YoutubeCommentThreadResponse.class);
			return responseEntity.getBody();
		}catch (HttpStatusCodeException e) {
			logger.info("Error occurred while fetching comments from YouTube: {}", e.getResponseBodyAsString());
			GoogleBaseResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), GoogleBaseResponse.class);
			if (e.getStatusCode().is5xxServerError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR,errorResponse.getMessage());
				}
			}
			if(e.getStatusCode().is4xxClientError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					if(Objects.nonNull(errorResponse) && StringUtils.isNotEmpty(errorResponse.getStatus())
							&& "PERMISSION_DENIED".equalsIgnoreCase(errorResponse.getStatus())) {
						throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, errorResponse.getMessage());
					}
					if(Objects.nonNull(errorResponse) && StringUtils.isNotEmpty(errorResponse.getMessage())
						&& errorResponse.getMessage().contains(PAGE_INVALID_SUSPENDED_ERROR_MESSAGE)) {
						throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, errorResponse.getMessage());
					}
					throw new BirdeyeSocialException(errorResponse.getCode(),errorResponse.getMessage());
				}
			}
		}catch (Exception e){
			throw new Exception("Unable to get reason of failure"+channelId);
		}
		return null;
	}

	@Override
	public YoutubeCommentThreadResponse getVideoInfo(String accessToken, List<String> videoIdsList) throws Exception {
		MultiValueMap<String, String> parameters = new LinkedMultiValueMap<>();
		String videoIds = String.join(",", videoIdsList);
		parameters.add("part", "snippet,contentDetails,statistics");
		parameters.add("id",videoIds);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.set(HttpHeaders.AUTHORIZATION,"Bearer "+accessToken);
		httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		HttpEntity<Void> entity = new HttpEntity<>(httpHeaders);
		URI uri = UriComponentsBuilder.fromHttpUrl(VIDEO_URL).queryParams(parameters).build().encode().toUri();
		try {
			ResponseEntity<YoutubeCommentThreadResponse> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,entity,YoutubeCommentThreadResponse.class);
			return responseEntity.getBody();
		}catch (HttpStatusCodeException e) {
			logger.error("[getVideoInfo] Error occurred while getting YouTube video info: {}", e.getResponseBodyAsString());
			GoogleBaseResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), GoogleBaseResponse.class);
			if (e.getStatusCode().is5xxServerError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR,errorResponse.getMessage());
				}
			}
			if(e.getStatusCode().is4xxClientError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					throw new BirdeyeSocialException(errorResponse.getCode(),errorResponse.getMessage());
				}
			}
		}catch (Exception e){
			throw new Exception("Unable to get reason of failure"+videoIdsList);
		}
		return null;
	}

	@Override
	public void hideUnhideComment(BusinessYoutubeChannel youtubeChannel, String commentId, Boolean hide) {
		logger.info("Inside hideUnhideComment, hide: {}, channelId: {}, commentId: {}", hide, youtubeChannel.getChannelId(), commentId);

		MultiValueMap<String, String> parameters = new LinkedMultiValueMap<>();
		parameters.add("id", commentId);
		parameters.add("moderationStatus", Objects.nonNull(hide) && hide ? REJECTED : PUBLISHED);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.set(HttpHeaders.AUTHORIZATION, "Bearer " + getGoogleAuthToken(youtubeChannel).getAccess_token());
		HttpEntity<Void> entity = new HttpEntity<>(httpHeaders);
		URI uri = UriComponentsBuilder.fromHttpUrl(COMMENTS_URL + "/setModerationStatus").queryParams(parameters).build().encode().toUri();
		try {
			restTemplate.exchange(uri, HttpMethod.POST, entity, Void.class);
		}catch (HttpStatusCodeException e) {
			GoogleBaseResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), GoogleBaseResponse.class);
			if (e.getStatusCode().is5xxServerError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR,errorResponse.getMessage());
				}
			}
			if(e.getStatusCode().is4xxClientError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					throw new BirdeyeSocialException(errorResponse.getCode(),errorResponse.getMessage());
				}
			}
		}catch (Exception e){
			throw new SocialBirdeyeException(ErrorCodes.YOUTUBE_IO_EXCEPTION, e.getMessage());
		}
	}

	@Override
	public YoutubeCommentReply.Comment replyToComment(BusinessYoutubeChannel youtubeChannel, String commentId, String replyMessage) {
		logger.info("Inside replyToComment, reply message {}, channelId {}, commentId {}",
				replyMessage, youtubeChannel.getChannelId(), commentId);

		Comment comment = new Comment();
		CommentSnippet commentSnippet = new CommentSnippet();
		commentSnippet.setTextOriginal(replyMessage);
		commentSnippet.setParentId(commentId);
		comment.setSnippet(commentSnippet);

		MultiValueMap<String, String> parameters = new LinkedMultiValueMap<>();
		parameters.add("part", "snippet");
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.set(HttpHeaders.AUTHORIZATION, "Bearer " + getGoogleAuthToken(youtubeChannel).getAccess_token());
		HttpEntity<Comment> entity = new HttpEntity<>(comment, httpHeaders);
		URI uri = UriComponentsBuilder.fromHttpUrl(COMMENTS_URL).queryParams(parameters).build().encode().toUri();
		try {
			ResponseEntity<YoutubeCommentReply.Comment> newComment = restTemplate.postForEntity(uri, entity, YoutubeCommentReply.Comment.class);
			logger.info("New comment created {}", newComment.getBody());
			return newComment.getBody();
		}catch (HttpStatusCodeException e) {
			GoogleBaseResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), GoogleBaseResponse.class);
			if (e.getStatusCode().is5xxServerError()){
				if (Objects.nonNull(response)) {
					logger.info("response from youtube {}",response);
					GoogleErrorResponse errorResponse = response.getError();
					logger.info("errorResponse {}",errorResponse);
						if(!CollectionUtils.isEmpty(errorResponse.getErrors()) && errorResponse.getErrors().get(0).getReason().equalsIgnoreCase(Constants.PARENT_COMMENT_NOT_FOUND))
						{
							throw new SocialBirdeyeException(ErrorCodes.PARENT_COMMENT_NOT_FOUND,"The comment you’re trying to reply to has been deleted. Join the conversation on active threads instead.");
						}

					else {
						throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, errorResponse.getMessage());
					}
				}
			}
			if(e.getStatusCode().is4xxClientError()){
				if (Objects.nonNull(response)) {
					logger.info("response from youtube {}",response);
					GoogleErrorResponse errorResponse = response.getError();
					logger.info("errorResponse {}",errorResponse);
					if(!CollectionUtils.isEmpty(errorResponse.getErrors()) && errorResponse.getErrors().get(0).getReason().equalsIgnoreCase(Constants.PARENT_COMMENT_NOT_FOUND))
					{
						throw new SocialBirdeyeException(ErrorCodes.PARENT_COMMENT_NOT_FOUND,"The comment you’re trying to reply to has been deleted. Join the conversation on active threads instead.");
					}
					else {
						throw new BirdeyeSocialException(errorResponse.getCode(), errorResponse.getMessage());
					}
				}
			}
		}catch (Exception e){
			logger.info("Exception occured:{}",e.getMessage());
			throw e;
		}
		return null;
	}

	@Override
	public YoutubeCommentReply.Comment commentOnVideo(BusinessYoutubeChannel youtubeChannel, String videoId, String message) {
		logger.info("Inside commentOnVideo, message {}, channelId {}, videoId {}",
				message, youtubeChannel.getChannelId(), message);

		YoutubeCommentReply.Comment comment = new YoutubeCommentReply.Comment();
		YoutubeCommentSnippet commentSnippet = new YoutubeCommentSnippet();
		commentSnippet.setChannelId(youtubeChannel.getChannelId());
		commentSnippet.setVideoId(videoId);
		YoutubeCommentSnippet.TopLevelComment topLevelComment = new YoutubeCommentSnippet.TopLevelComment();
		YoutubeCommentSnippet youtubeCommentSnippet = new YoutubeCommentSnippet();
		youtubeCommentSnippet.setTextOriginal(message);
		topLevelComment.setSnippet(youtubeCommentSnippet);
		commentSnippet.setTopLevelComment(topLevelComment);
		comment.setSnippet(commentSnippet);

		MultiValueMap<String, String> parameters = new LinkedMultiValueMap<>();
		parameters.add("part", "snippet");
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.set(HttpHeaders.AUTHORIZATION, "Bearer " + getGoogleAuthToken(youtubeChannel).getAccess_token());
		HttpEntity<YoutubeCommentReply.Comment> entity = new HttpEntity<>(comment, httpHeaders);
		URI uri = UriComponentsBuilder.fromHttpUrl(COMMENTS_THREADS_URL).queryParams(parameters).build().encode().toUri();
		try {
			ResponseEntity<YoutubeCommentReply.Comment> newComment = restTemplate.postForEntity(uri, entity, YoutubeCommentReply.Comment.class);
			logger.info("New comment created {}", newComment.getBody());
			return newComment.getBody();
		}catch (HttpStatusCodeException e) {
			GoogleBaseResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), GoogleBaseResponse.class);
			if (e.getStatusCode().is5xxServerError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR,errorResponse.getMessage());
				}
			}
			if(e.getStatusCode().is4xxClientError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					if(Objects.nonNull(errorResponse) && StringUtils.isNotEmpty(errorResponse.getMessage())
							&& errorResponse.getMessage().contains(PAGE_INVALID_SUSPENDED_ERROR_MESSAGE)) {
						throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, errorResponse.getMessage());
					}
					throw new BirdeyeSocialException(errorResponse.getCode(),errorResponse.getMessage());
				}
			}
		}catch (Exception e){
			throw new SocialBirdeyeException(ErrorCodes.YOUTUBE_IO_EXCEPTION, e.getMessage());
		}
		return null;
	}

	@Override
	public void banAuthor(BusinessYoutubeChannel youtubeChannel, String commentId) {
		logger.info("Inside banAuthor, channelId {}, commentId {}", youtubeChannel.getChannelId(), commentId);

		MultiValueMap<String, String> parameters = new LinkedMultiValueMap<>();
		parameters.add("id", commentId);
		parameters.add("moderationStatus", REJECTED);
		parameters.add("banAuthor", String.valueOf(true));
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.set(HttpHeaders.AUTHORIZATION, "Bearer " + getGoogleAuthToken(youtubeChannel).getAccess_token());
		HttpEntity<Void> entity = new HttpEntity<>(httpHeaders);
		URI uri = UriComponentsBuilder.fromHttpUrl(COMMENTS_URL + "/setModerationStatus").queryParams(parameters).build().encode().toUri();
		try {
			restTemplate.exchange(uri, HttpMethod.POST, entity, Void.class);
		}catch (HttpStatusCodeException e) {
			GoogleBaseResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), GoogleBaseResponse.class);
			if (e.getStatusCode().is5xxServerError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR,errorResponse.getMessage());
				}
			}
			if(e.getStatusCode().is4xxClientError()){
				if (Objects.nonNull(response)) {
					GoogleErrorResponse errorResponse = response.getError();
					throw new BirdeyeSocialException(errorResponse.getCode(),errorResponse.getMessage());
				}
			}
		}catch (Exception e){
			throw new SocialBirdeyeException(ErrorCodes.YOUTUBE_IO_EXCEPTION, e.getMessage());
		}
	}

	private YouTube getYouTubeServiceFromRawChannel(BusinessYoutubeChannel youtubeChannel) {
		GoogleAuthToken googleAuthToken = getGoogleAuthToken(youtubeChannel);
		TokensAndUrlAuthData tokensAndUrlAuthData = youtubeObjectConverter.convertAuthTokenResponse(googleAuthToken);
		return getService(tokensAndUrlAuthData);
	}

	private GoogleAuthToken getGoogleAuthToken(BusinessYoutubeChannel youtubeChannel) {
		GoogleAuthToken googleAuthToken;
		try {
			googleAuthToken = googleAuthenticationService.getYoutubeAuthTokens(youtubeChannel.getRefreshTokenId());
		}catch (BirdeyeSocialException e){
			if(e.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value()) {
				youtubeUploadService.markYoutubePageInvalid(youtubeChannel);
			}
			throw new BirdeyeSocialException(e.getCode(),e.getMessage());
		}

		return googleAuthToken;
	}

	@Override
	public YoutubeChannelStatisticsResponse getChannelInformation(String channelId, String accessToken) {
		// Accessing channel info i.e. channel name, channel image etc.
		MultiValueMap<String, String> parameters = new LinkedMultiValueMap<>();
		parameters.add("part", "statistics");
		parameters.add("id", channelId);
		parameters.add("key", YT_KEY);

		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.set(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken);
		HttpEntity<Void> entity = new HttpEntity<>(httpHeaders);

		URI uri = UriComponentsBuilder.fromHttpUrl(YT_CHANNEL_URL).queryParams(parameters).build().encode().toUri();
		ResponseEntity<JsonNode> responseEntity = null;
		YoutubeChannelStatisticsResponse result = null;
		try {
			responseEntity = restTemplate.exchange(uri, HttpMethod.GET, entity, JsonNode.class);
			logger.info("Received Response from Youtube api for URI:{} with status :{}", uri,
					responseEntity.getStatusCode());
			JsonNode response = responseEntity.getBody();
			// Channel Information
			JsonNode items = response.get("items");
			if (items != null && items instanceof ArrayNode) {
					JsonNode item = items.get(0);
					if (item != null) {
						ObjectMapper mapper = new ObjectMapper();
						result = mapper.convertValue(item.get("statistics"), YoutubeChannelStatisticsResponse.class);
						logger.info("Youtube channel#{} info : {}",channelId, result);
					}
			}
		} catch (Exception e) {
			logger.error("Error calling in channel API", e);
		}
		return result;
	}

}
