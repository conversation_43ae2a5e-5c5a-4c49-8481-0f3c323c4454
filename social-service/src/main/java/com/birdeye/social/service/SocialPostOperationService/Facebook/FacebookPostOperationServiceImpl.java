package com.birdeye.social.service.SocialPostOperationService.Facebook;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.FacebookPostType;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.SocialPostStatusEnum;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.*;
import com.birdeye.social.facebook.FacebookPost.FacebookPostDetails;
import com.birdeye.social.facebook.response.FbMediaPublishResponse;
import com.birdeye.social.facebook.response.FbUploadStatusResponse;
import com.birdeye.social.model.*;
import com.birdeye.social.service.*;
import com.birdeye.social.service.SocialPostOperationService.PostOperationUtil;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.birdeye.social.constant.Constants.FB_BASE_URL;
import static com.birdeye.social.constant.KafkaTopicEnum.SOCIAL_SYNC_BUSINESS_POSTS;

@Service
public class FacebookPostOperationServiceImpl implements FacebookPostOperationService{

    @Autowired
    private SocialFBPageRepository socialFBPageRepository;

    @Autowired
    private SocialPostFacebookService socialPostFacebookService;

    @Autowired
    private IPermissionMappingService permissionMappingService;
    @Autowired
    private SocialPostInfoRepository publishInfoRepository;
    @Autowired
    private PostOperationUtil postOperationUtil;
    @Autowired
    private FacebookService facebookService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ISocialPostsAssetService socialPostsAssetService;
    @Autowired
    private KafkaProducerService kafkaProducerService;
    @Autowired
    private KafkaExternalService kafkaExternalService;

    private static final String ERROR_CODE = "error_code";
    private static final String HTTP_RESPONSE = "http_response";

    private static final Logger LOGGER = LoggerFactory.getLogger(FacebookPostOperationServiceImpl.class);


    private void addErrorMessage(BirdeyeSocialException e, SocialPostPublishInfo publishInfo) {
        List<PermissionMapping> permissionMapping = new ArrayList<>();
        boolean check = false;
        boolean isErrorMessageFound = false;
        if (Objects.nonNull(e.getData().get(ERROR_CODE))) {
            if (Objects.nonNull(e.getData().get("error_sub_code"))) {
                permissionMapping = permissionMappingService.getDataByChannelandHttpResponseAndErrorCodeAndErrorSubCode(Constants.FACEBOOK, (Integer) e.getData().get(HTTP_RESPONSE), (Integer) e.getData().get(ERROR_CODE), (Integer) e.getData().get("error_sub_code"));
                check = true;
            }
            if (!check) {
                permissionMapping = permissionMappingService.getDataByChannelandHttpResponseAndErrorCode(Constants.FACEBOOK, (Integer) e.getData().get(HTTP_RESPONSE), (Integer) e.getData().get(ERROR_CODE));
            }
        } else {
            permissionMapping = permissionMappingService.getDataByChannelandHttpResponse(Constants.FACEBOOK, (Integer) e.getData().get(HTTP_RESPONSE));
        }
        if (permissionMapping.size() == 1) {
            publishInfo.setFailureReason(permissionMapping.get(0).getErrorMessage());
            publishInfo.setBucket(permissionMapping.get(0).getBucket());
            isErrorMessageFound = true;
        } else if (permissionMapping.size() > 1) {
            for (PermissionMapping permissionMappingList : permissionMapping) {
                if (e.getMessage().contains(permissionMappingList.geterrorActualMessage())) {
                    publishInfo.setFailureReason(permissionMappingList.getErrorMessage());
                    publishInfo.setBucket(permissionMappingList.getBucket());
                    isErrorMessageFound = true;
                    break;
                }
            }
        }
        if(!isErrorMessageFound){
            PermissionMapping permissionMappingUnknown = permissionMappingService.getDataByChannelAndPermissionCodeAndModule(Constants.FACEBOOK, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR,Constants.PUBLISH_MODULE);
            publishInfo.setFailureReason(permissionMappingUnknown.getErrorMessage());
            publishInfo.setBucket(permissionMappingUnknown.getBucket());
            LOGGER.info("New error found : {}", e.getMessage());
            LOGGER.info("Error code sub values :{}", e.getData());
        }

        LOGGER.error("FB post edit failed: {}", e.getMessage());
        publishInfo.setIsPublished(2);
    }

    @Override
    public void editPublishedPost(SocialPostPublishInfo publishInfo) throws Exception{
        List<BusinessFBPage> businessFBPages = socialFBPageRepository.findByFacebookPageIdAndIsValid(publishInfo.getExternalPageId(), 1);
        try {
            if(CollectionUtils.isEmpty(businessFBPages)) {
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_PAGE_DETAILS, "Page not found");
            }
            // If postId is not in the format of pageId_postId, then convert it to that format
            String postId = publishInfo.getPostId();
            if(StringUtils.isEmpty(publishInfo.getSocialPost().getVideoIds()) &&!postId.contains(publishInfo.getExternalPageId()+"_")){
                postId = publishInfo.getExternalPageId() + "_" + postId;
            }
            LOGGER.info("Editing FB post with postId: {} and fb page id: {}", postId, publishInfo.getExternalPageId());
            FacebookPostInfo info = socialPostFacebookService.editFBPost(businessFBPages.get(0).getPageAccessToken(),postId,publishInfo.getSocialPost().getPostText());
            publishInfo.setIsPublished(SocialPostStatusEnum.PUBLISHED.getId());
            publishInfo.setPostUrl(FB_BASE_URL + publishInfo.getPostId());
            LOGGER.info("FB post edit {} is successful", info.getPostId());
        } catch (BirdeyeSocialException e) {
            addErrorMessage(e,publishInfo);
        } catch (Exception ex) {
            LOGGER.error("FB post edit failed: {}", ex.getMessage());
            PermissionMapping permissionMappingUnknown = permissionMappingService.getDataByChannelAndPermissionCodeAndModule(Constants.FACEBOOK, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR,Constants.PUBLISH_MODULE);
            publishInfo.setFailureReason(permissionMappingUnknown.getErrorMessage());
            publishInfo.setBucket(permissionMappingUnknown.getBucket());
            publishInfo.setIsPublished(SocialPostStatusEnum.FAILED.getId());
        }
        publishInfoRepository.saveAndFlush(publishInfo);
        postOperationUtil.markOriginalPost(publishInfo);
    }

    @Override
    public void deletePost(SocialPostPublishInfo publishedPost) throws Exception{
        List<BusinessFBPage> businessFBPages = socialFBPageRepository.findByFacebookPageIdAndIsValid(publishedPost.getExternalPageId(), 1);
        if(CollectionUtils.isEmpty(businessFBPages)) {
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_PAGE_DETAILS, "Page not found");
        }
        try {
            BusinessFBPage businessFBPage = businessFBPages.get(0);
            boolean success = socialPostFacebookService.deletePostObject(businessFBPage.getPageAccessToken(), publishedPost.getPostId());
            if(!success) {
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_DELETE_FACEBOOK_OBJECT, "post delete failed on facebook");
            }
        } catch (BirdeyeSocialException e) {
            if(e.getCode() == ErrorCodes.CLIENT_ERROR_404.value()) {
                LOGGER.info("Seems like {} post with publish info id: {} is already deleted, marking it as deleted in the system", SocialChannel.FACEBOOK.getName(), publishedPost.getId());
            } else {
                throw e;
            }
        }
    }

    private void uploadReelThumbnail(FacebookPageAccessInfo facebookPageAccessInfo, String videoId, SocialPostPublishInfo publishInfo) {
        try {
            String thumbUrl = null;
            if(StringUtils.isNotEmpty(publishInfo.getSocialPost().getVideoIds())) {
                Integer assetId = Integer.valueOf(publishInfo.getSocialPost().getVideoIds().split(",")[0]);
                SocialPostsAssets socialPostsAsset = socialPostsAssetService.findById(assetId);
                if(StringUtils.isNotEmpty(socialPostsAsset.getVideoThumbnail())) {
                    thumbUrl = socialPostsAsset.getVideoThumbnail();
                    File imgFile = commonService.getFile(null, thumbUrl);
                    FbMediaPublishResponse thumbnailResponse = facebookService.uploadThumbnail(facebookPageAccessInfo, videoId, imgFile);
                    LOGGER.info("Thumbnail uploaded for video id {}: {}",videoId, thumbnailResponse.getSuccess());
                }
            }
        } catch (Exception e) {
            LOGGER.info("Error while uploading reel thumbnail for video id {} and publish info id {}", videoId, publishInfo.getId(), e);
        }
    }

    @Override
    public void updateStoryLink (FacebookPageAccessInfo facebookPageAccessInfo, SocialPostPublishInfo publishInfo) {
        LOGGER.info("Updating story link for post id {}", publishInfo.getPostId());
        try {
            FacebookPostDetails facebookPostDetails = facebookService.getStoryData(facebookPageAccessInfo.getAccessToken(), publishInfo.getPostId());
            LOGGER.info("[updateStoryLink] facebookPostDetails: {}", facebookPostDetails);
            if(Objects.nonNull(facebookPostDetails) && StringUtils.isNotEmpty(facebookPostDetails.getUrl())) {
                LOGGER.info("[updateStoryLink] facebookPostDetails url: {}", facebookPostDetails.getUrl());
                publishInfo.setPostUrl(facebookPostDetails.getUrl());
            }
        } catch (Exception e) {
            LOGGER.info("Error while updating story link for post id {}", publishInfo.getPostId(), e);
        }
    }

    private void checkRetryExhausted(ProcessingPost processingPost, SocialPostPublishInfo publishInfo) {
        if(processingPost.getRetryCount()==1) {
            //retry exhausted, fail the post
            PermissionMapping permissionMappingUnknown = permissionMappingService.getDataByChannelAndPermissionCodeAndModule(Constants.FACEBOOK, Constants.ERROR_CONSTANT_FOR_PENDING_STATE_FB,Constants.PUBLISH_MODULE);
            publishInfo.setFailureReason(permissionMappingUnknown.getErrorMessage());
            publishInfo.setBucket(permissionMappingUnknown.getBucket());
            publishInfo.setIsPublished(SocialPostStatusEnum.FAILED.getId());
            publishInfoRepository.saveAndFlush(publishInfo);

        }
    }

    @Override
    public void processPendingPosts(ProcessingPost processingPost, PageInfoDto pageInfoDto, SocialPostPublishInfo publishInfo) {

        try {
            FacebookPageAccessInfo facebookPageAccessInfo = new FacebookPageAccessInfo();
            facebookPageAccessInfo.setAccessToken(pageInfoDto.getAccessToken());
            facebookPageAccessInfo.setPageId(pageInfoDto.getPageId());
            FbUploadStatusResponse statusResponse = facebookService.checkMediaStatus(facebookPageAccessInfo, processingPost.getMediaId());

            if("ready".equalsIgnoreCase(statusResponse.getStatus().getVideoStatus())) {
                publishInfo.setIsPublished(SocialPostStatusEnum.PUBLISHED.getId());
                processingPost.setRetryCount(0);
                if(FacebookPostType.REEL.getName().equalsIgnoreCase(processingPost.getMediaType())) {
                    uploadReelThumbnail(facebookPageAccessInfo,processingPost.getMediaId(), publishInfo);
                } else if(FacebookPostType.STORY.getName().equalsIgnoreCase(processingPost.getMediaType())) {
                    updateStoryLink(facebookPageAccessInfo, publishInfo);
                }
                publishInfoRepository.saveAndFlush(publishInfo);
                if (SocialPostStatusEnum.PUBLISHED.getId().equals(publishInfo.getIsPublished())
                        && commonService.isBusinessAllowedToSyncBusinessPosts(publishInfo.getEnterpriseId())) {
                    kafkaProducerService.sendObjectV1(SOCIAL_SYNC_BUSINESS_POSTS.getName(), SocialPostPublishInfoRequest.builder().id(publishInfo.getId()).build());
                }
                addFirstComment(publishInfo, facebookPageAccessInfo, pageInfoDto);
            } else if("error".equalsIgnoreCase(statusResponse.getStatus().getVideoStatus())) {
                publishInfo.setIsPublished(SocialPostStatusEnum.FAILED.getId());
                publishInfo.setFailureReason(statusResponse.getStatus().getProcessingPhase().getErrors().get(0).getMessage());
                publishInfoRepository.saveAndFlush(publishInfo);
                processingPost.setRetryCount(0);
            }
            checkRetryExhausted(processingPost, publishInfo);

        } catch (Exception e) {
            LOGGER.info("Error while processing post {}", processingPost, e);
            checkRetryExhausted(processingPost, publishInfo);
        }
    }

    public void addFirstComment(SocialPostPublishInfo publishInfo, FacebookPageAccessInfo facebookPageAccessInfo, PageInfoDto pageInfoDto) {
        LOGGER.info("Adding first comment for postId: {}", publishInfo.getPostId());
        try {
            String postMetadataJson = publishInfo.getSocialPost().getPostMetadata();
            if (Objects.isNull(postMetadataJson)) {
                LOGGER.info("Post metadata is null for postId: {}", publishInfo.getPostId());
                return;
            }

            SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(postMetadataJson, SocialPostSchedulerMetadata.class);
            if (Objects.isNull(metadata) || Objects.isNull(metadata.getFbPostMetadata())) {
                LOGGER.info("FbPostMetadata is missing for postId: {}", publishInfo.getPostId());
                return;
            }

            FbPostMetadata fbPostMetadata = JSONUtils.fromJSON(metadata.getFbPostMetadata(), FbPostMetadata.class);
            if (fbPostMetadata == null || fbPostMetadata.getFirstComment() == null) {
                LOGGER.info("No first comment found to post for postId: {}", publishInfo.getPostId());
                return;
            }
            String postId = publishInfo.getPostId();
            if (!postId.contains("_")) {
                postId = pageInfoDto.getPageId() + "_" + postId;
            }
            facebookPageAccessInfo.setObjectId(postId);

            FacebookData facebookData = new FacebookData();
            facebookData.setText(fbPostMetadata.getFirstComment());

            FbUser publisher = new FbUser();
            publisher.setId("123");
            publisher.setName("ADMIN");

            facebookService.postComment(facebookPageAccessInfo, facebookData, publisher);
            LOGGER.info("First comment posted successfully for postId: {}", publishInfo.getPostId());

        } catch (Exception ex) {
            LOGGER.error("Error while adding first comment to postId: {}", publishInfo.getPostId(), ex);
        }
    }
}
