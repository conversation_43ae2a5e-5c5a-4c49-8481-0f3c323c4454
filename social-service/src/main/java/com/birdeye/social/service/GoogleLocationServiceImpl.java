package com.birdeye.social.service;

import com.birdeye.social.GoogleLocationSearchResponseDto;
import com.birdeye.social.constant.KafkaTopicEnum;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.dto.GoogleLocationSearchRequest;
import com.birdeye.social.dto.GoogleLocationSearchResponse;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.google.AgentLaunchState;
import com.birdeye.social.google.AgentVerificationState;
import com.birdeye.social.google.GoogleBusinessCommCredentialsService;
import com.birdeye.social.model.CreateLocationRequest;
import com.birdeye.social.sro.GoogleAuthToken;
import com.google.api.services.businesscommunications.v1.BusinessCommunications;
import com.google.api.services.businesscommunications.v1.model.*;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.*;

@Service
public class GoogleLocationServiceImpl implements GoogleLocationService {

    @Autowired
    private GoogleBusinessCommCredentialsService googleBizCommService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private BusinessGMBLocationRawRepository businessGMBLocationRawRepository;

    @Autowired
    GoogleAuthenticationService googleAuthenticationService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private Environment env;

    private final Logger logger = LoggerFactory.getLogger(GoogleLocationServiceImpl.class);

    private final List<String> allowedEntryPoint = Arrays.asList("MAPS_TACTILE","PLACESHEET");

    private static final String GOOGLE_SEARCH_LOCATION_URL = "https://mybusinessbusinessinformation.googleapis.com/v1/googleLocations:search";

    @Autowired
    private GoogleMessagesAgentService googleMsgAgentService;

    private Location createLocation(String brandName, Location location) throws Exception {
        BusinessCommunications.Brands.Locations.Create request = googleBizCommService.getAppBuilder()
                .build().brands().locations().create(brandName,location);
        Location createdLocation = request.execute();
        return createdLocation;
    }

    @Override
    @Deprecated
    public void requestLocationVerification(BusinessGoogleMyBusinessLocation gmbPage , String accessToken) throws Exception {
        BusinessCommunications.Brands.Locations.RequestVerification request = googleBizCommService.getUserBuilder(accessToken)
                .build().brands().locations().requestVerification(gmbPage.getgMsgLocationName(), new RequestLocationVerificationRequest());

        LocationVerification locationVerification = request.execute();
        logger.info("requestLocationVerification: LocationVerification successfully created {}", locationVerification.toPrettyString());

        if(Objects.isNull(locationVerification.getVerificationState())){
            locationVerification = getLocationVerificationState(gmbPage.getgMsgLocationName());
        }
        // Location verificationState should be set to VERIFICATION_STATE_VERIFIED
        if (!locationVerification.getVerificationState().equals(AgentVerificationState.VERIFICATION_STATE_VERIFIED.toString())) {
            throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_LOCATION_NOT_VERIFIED,
                    "Location's verificationState is not " + AgentVerificationState.VERIFICATION_STATE_VERIFIED);
        }
    }

    @Override
    @Deprecated
    public void requestLocationLaunch(BusinessGoogleMyBusinessLocation gmbPage) throws Exception {
        BusinessCommunications.Brands.Locations.RequestLaunch request = googleBizCommService.getAppBuilder()
                .build().brands().locations().requestLaunch(gmbPage.getgMsgLocationName(), new RequestLocationLaunchRequest());
        LocationLaunch locationLaunch = request.execute();
        logger.info("requestLocationVerification: LocationVerification successfully created {}", locationLaunch.toPrettyString());
        if(Objects.isNull(locationLaunch.getLaunchState())){
            locationLaunch = getLocationLaunchState(gmbPage.getgMsgLocationName());
        }
        // Location launchState should be set to LAUNCH_STATE_LAUNCHED
        if (!locationLaunch.getLaunchState().equalsIgnoreCase(AgentLaunchState.LAUNCH_STATE_LAUNCHED.name())) {
            throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_LOCATION_NOT_LAUNCHED,
                    "Location's verificationState is not " + AgentVerificationState.VERIFICATION_STATE_VERIFIED.toString());
        }
    }

    @Override
    @Deprecated
    public void deleteLocation(String locationName) {
        try {
            BusinessCommunications.Brands.Locations.Delete delete = googleBizCommService.getAppBuilder().build().brands().locations().delete(locationName);
            delete.execute();
        }catch (Exception e){
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_DELETE_GMB_LOCATION,"Unable to delete location with id :"+locationName);
        }
    }

    @Override
    public void searchLocations(String query, int pageSize, Integer businessId) {
        BusinessGoogleMyBusinessLocation page = businessGMBLocationRawRepository.findByBusinessId(businessId);
        if (Objects.isNull(page)) {
            logger.info("No business location found with business id: {}", businessId);
            return;
        }

        GoogleAuthToken googleAuthToken = googleAuthenticationService.getGoogleAuthTokens(page.getRefreshTokenId());

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + googleAuthToken.getAccess_token());

        GoogleLocationSearchRequest requestBody = new GoogleLocationSearchRequest(pageSize, query);
        HttpEntity<GoogleLocationSearchRequest> entity = new HttpEntity<>(requestBody, headers);

        GoogleLocationSearchResponseDto responseDto = GoogleLocationSearchResponseDto.builder()
                .businessId(businessId)
                .build();

        try {
            GoogleLocationSearchResponse response = restTemplate.exchange(
                    GOOGLE_SEARCH_LOCATION_URL,
                    HttpMethod.POST,
                    entity,
                    GoogleLocationSearchResponse.class
            ).getBody();

            responseDto.setGoogleLocations(response.getGoogleLocations());

        } catch (RestClientException e) {
            String errorMessage = "[searchLocations] Error during the HTTP request: " + e.getMessage();
            logger.info(errorMessage);
            responseDto.setErrorMessage(errorMessage);
        } catch (Exception e) {
            String errorMessage = "[searchLocations] Unexpected error: " + e.getMessage();
            logger.error(errorMessage);
            responseDto.setErrorMessage(errorMessage);
        }

        kafkaProducerService.sendObjectV1(KafkaTopicEnum.LISTING_GOOGLE_SEARCH_RESPONSE.getName(), responseDto);
    }


    @Override
    @Deprecated
    public void requestLocationUnLaunch(String locationName) throws Exception {
        LocationLaunch locationLaunch  = new LocationLaunch().setLaunchState(AgentLaunchState.LAUNCH_STATE_UNLAUNCHED.name());
        StringBuilder builder = new StringBuilder();
        builder.append(locationName);
        builder.append("/launch");
        BusinessCommunications.Brands.Locations.UpdateLaunch request = googleBizCommService.getAppBuilder()
                .build().brands().locations().updateLaunch(builder.toString(),locationLaunch);

        LocationLaunch updateLaunch = request.execute();
        logger.info("requestLocationVerification: Location launch successfully created {}", updateLaunch.toPrettyString());

        // Location launchState should be set to LAUNCH_STATE_UNLAUNCHED
        if (!updateLaunch.getLaunchState().equalsIgnoreCase(AgentLaunchState.LAUNCH_STATE_UNLAUNCHED.name())) {
            throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_LOCATION_NOT_LAUNCHED,
                    "Locations's launch state is not " + AgentLaunchState.LAUNCH_STATE_UNLAUNCHED.toString());
        }
    }

    @Override
    @Deprecated
    public Location createLocation(CreateLocationRequest req) throws Exception {
        final List<LocationEntryPointConfig> locationEntryPointConfigs = new ArrayList<>();
        allowedEntryPoint.stream().forEach(item->{
            LocationEntryPointConfig loc = new LocationEntryPointConfig();
            loc.set("allowedEntryPoint",item);
            locationEntryPointConfigs.add(loc);
        });
        final Location location = new Location()
                        .setLocationEntryPointConfigs(locationEntryPointConfigs)
                        .setDefaultLocale("en")
                        .setAgent(req.getAgentName())
                        .setPlaceId(req.getPlaceId()); // TODO @mahak use builder pattern here
        Location createdLocation = createLocation(req.getBrandName(),location);
        logger.info("createdLocation: Location successfully created {}", createdLocation.toPrettyString());
        return createdLocation;
    }

    @Override
    @Deprecated
    public Location updateLocation(Location location, String agentName) {
        return null;
    }

    @Override
    @Deprecated
    public Location getLocation(String locationName) {
        return null;
    }

    @Override
    @Deprecated
    public LocationLaunch getLocationLaunchState(String locationName) throws Exception {
        BusinessCommunications.Brands.Locations.GetLaunch launchRequest = googleBizCommService.getAppBuilder()
                .build().brands().locations().getLaunch(locationName.concat("/launch"));
        return launchRequest.execute();
    }

    @Override
    @Deprecated
    public LocationVerification getLocationVerificationState(String locationName) throws Exception {
        BusinessCommunications.Brands.Locations.GetVerification verification = googleBizCommService.getAppBuilder()
                .build().brands().locations().getVerification(locationName.concat("/verification"));
        return verification.execute();
    }

    @Override
    @Deprecated
    public Location getLocationByPlaceId(String brandName, String placeId) throws IOException {
        List<Location> allLocations=getAllLocations(brandName);
        if(CollectionUtils.isNotEmpty(allLocations)) {
            for (Location location : allLocations) {
                if (location.getPlaceId().equals(placeId))
                    return location;
            }
        }
        return null;
    }

    @Override
    @Deprecated
    public List<Location> getAllLocations(String brandName) throws IOException {
        BusinessCommunications.Brands.Locations.List request = googleBizCommService.getAppBuilder()
            .build().brands().locations().list(brandName);
        return request.execute().getLocations();
    }

}
