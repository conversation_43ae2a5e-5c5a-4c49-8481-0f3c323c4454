package com.birdeye.social.service;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.Status;
import com.birdeye.social.dao.AutoMappingRepo;
import com.birdeye.social.entities.AutoMapping;
import com.birdeye.social.model.AutoMappingStatusResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service("AutomappingService")
public class AutoMappingServiceImpl implements AutoMappingService {
    @Autowired
    private AutoMappingRepo autoMappingRepo;

    @Override
    public void createAutoMappingEntry (Long enterpriseId, String channel){
       AutoMapping autoMapping = new AutoMapping();
       autoMapping.setEnterpriseId(enterpriseId);
       autoMapping.setChannel(channel);
       autoMapping.setStatus(Status.INITIAL.getName());
       autoMapping.setMappedRawIds(null);
       autoMapping.setRawIdsCount(0);
       autoMappingRepo.save(autoMapping);
    }

    @Override
    @Transactional
    public void updateAutoMappingEntryStatus (Long enterpriseId, String status,String channel){
        List<AutoMapping> autoMappingList = autoMappingRepo.findLastRequestByEnterpriseIdAndChannel(enterpriseId,channel);
        if(autoMappingList.size()>0) {
            AutoMapping autoMapping = autoMappingList.get(0);
            autoMapping.setStatus(status);
            autoMappingRepo.saveAndFlush(autoMapping);
        }
    }

    @Override
    @Transactional
    public void updateAutoMappingEntryStatusWithValue (AutoMapping value){
        if(value != null) {
            value.setStatus(Status.COMPLETE.getName());
            autoMappingRepo.saveAndFlush(value);
        }
    }

    @Override
    @Transactional
    public void updateAutoMappingRawIds(Long enterpriseId, Integer rawId,String channel) {
        List<AutoMapping> autoMappingList = autoMappingRepo.findLastRequestByEnterpriseIdAndChannel(enterpriseId,channel);
        if(autoMappingList.size()>0) {
            AutoMapping autoMapping = autoMappingList.get(0);
            if(autoMapping.getMappedRawIds() == null) {
                autoMapping.setMappedRawIds(rawId.toString());
            } else {
                String mappedStr = autoMapping.getMappedRawIds();
                autoMapping.setMappedRawIds(mappedStr + "," + rawId.toString());
            }
            autoMapping.setRawIdsCount(autoMapping.getRawIdsCount()+1);
            autoMappingRepo.saveAndFlush(autoMapping);
        }
    }

    @Override
    public AutoMapping fetchAutoMappingRequest(Long enterpriseId,String channel){
        List<AutoMapping> autoMappingList = autoMappingRepo.findLastRequestByEnterpriseIdAndChannel(enterpriseId,channel);
        if(CollectionUtils.isNotEmpty(autoMappingList)){
            return autoMappingList.get(0);
        }
        return null;
    }

    @Override
    public AutoMapping fetchResellerAutoMappingRequest(Long resellerId, String channel){
        List<AutoMapping> autoMappingList = autoMappingRepo.findLastRequestByResellerIdAndChannel(resellerId, channel);
        if(CollectionUtils.isNotEmpty(autoMappingList)){
            return autoMappingList.get(0);
        }
        return null;
    }

    @Override
    public AutoMappingStatusResponse fetchAutoMappingStatus(Long id, String channel, String type) {
        AutoMappingStatusResponse response = new AutoMappingStatusResponse();
        AutoMapping autoMapping = null;
        if(type.equals("enterprise")) {
            autoMapping = fetchAutoMappingRequest(id, channel);
            if(Objects.nonNull(autoMapping)) {
                response.setEnterpriseId(autoMapping.getEnterpriseId());
                response.setChannel(autoMapping.getChannel());
                response.setStatus(autoMapping.getStatus());
            }
        } else if(type.equals("reseller")){
            autoMapping = fetchResellerAutoMappingRequest(id, channel);
            if(Objects.nonNull(autoMapping)) {
                response.setResellerId(autoMapping.getResellerId());
                response.setChannel(autoMapping.getChannel());
                response.setStatus(autoMapping.getStatus());
            }
        }
        return response;
    }
}
