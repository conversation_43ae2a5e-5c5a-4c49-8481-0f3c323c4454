package com.birdeye.social.service.SocialReportService.Twitter;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.SocialTwitterAccountRepository;
import com.birdeye.social.dao.reports.SocialReportPropertyRepository;
import com.birdeye.social.dao.reports.BusinessPostsRepository;
import com.birdeye.social.dao.reports.TwitterPageInsightRepo;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessTwitterAccounts;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.entities.report.TwitterPageInsight;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.Request.DataModel;
import com.birdeye.social.insights.ES.Request.InsightsESRequest;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.insights.constants.InsightsConstants;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.SocialReportService.Converter.DbDataConverter;
import com.birdeye.social.service.SocialReportService.Converter.ReportDataConverter;
import com.birdeye.social.service.SocialReportService.ES.ReportsEsService;
import com.birdeye.social.service.TwitterCommonService;
import com.birdeye.social.twitter.*;
import com.birdeye.social.utils.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class TwitterReportServiceImpl implements TwitterReportService{

    private static final String HISTORICAL_TWITTER_PAGE_INSIGHTS = "historical_twitter_page_insights";
    @Autowired
    SocialTwitterAccountRepository twitterAccountRepository;
    @Autowired
    private ReportDataConverter reportDataConverter;
    @Autowired
    private KafkaProducerService kafkaProducerService;
    @Autowired
    private DbDataConverter dbDataConverter;
    @Autowired
    private TwitterPageInsightRepo twitterPageInsightRepo;
    @Autowired
    private ReportsEsService reportsEsService;
    @Autowired
    private TwitterService twitterService;
    @Autowired
    private TwitterCommonService twitterCommonService;

    @Autowired
    private SocialProxyHandler socialProxyHandler;
    @Autowired
    private SocialReportPropertyRepository reportPropertyRepository;

    @Autowired
    private BusinessPostsRepository businessPostsRepository;

    @Autowired
    private CommonService commonService;

    private final String dateFormatterString = "yyyy-MM-dd HH:mm:ss";

    private static final Logger log = LoggerFactory.getLogger(TwitterReportServiceImpl.class);

    @Override
    public void getPageInsightsFromTwitter(SocialScanEventDTO socialScanEventDTO) {
        String profileId = socialScanEventDTO.getExternalId();
        if(profileId == null ) {
            return;
        }
        List<BusinessTwitterAccounts> twitterAccounts = twitterAccountRepository.findByProfileId(Long.parseLong(profileId));
        log.info("Getting page insight for twitter account: {}",twitterAccounts);
        twitterAccounts.forEach(this::pushDataToKafkaForEsUpdate);
    }

    private void pushDataToKafkaForEsUpdate(BusinessTwitterAccounts twitterAccount) {
        TwitterCreds twitterCreds = twitterCommonService.getTwitterCreds(twitterAccount);
        TwitterProfileInfoResponse profileInfoResponse = new TwitterProfileInfoResponse();
        Integer mediaCount = null;
        try {
            profileInfoResponse = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(() ->
                    twitterService.getProfileDataV2(twitterCreds,twitterAccount.getHandle().substring(1)));
            mediaCount = profileInfoResponse.getTweet_count();
        } catch (BirdeyeSocialException ex) {
            log.error("Unauthorised error while getting twitter profile info for account: {}",twitterAccount, ex);
            if (Objects.nonNull(ex.getCode()) && (ex.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value())) {
                commonService.handleExpiredTokenForTwitter(twitterAccount);
            }
        }  catch (Exception e) {
            log.error("Unknown error while getting twitter profile info for account: {}",twitterAccount, e);
        }
        DataModel engagementDataModel = DataModel.builder()
                .pageIds("\""+twitterAccount.getProfileId()+"\"")
                .build();
        InsightsESRequest engagementRequest = new InsightsESRequest(engagementDataModel, SearchTemplate.POST_INSIGHT_PAGE_ID_ENGAGEMENT_SUM);
        engagementRequest.setRouting(twitterAccount.getEnterpriseId().toString());
        engagementRequest.setIndex(ElasticConstants.POST_INSIGHTS.getName());
        Integer currEngagement = reportsEsService.getSumEngagementOnPageId(engagementRequest);
        engagementRequest.setSearchTemplate(SearchTemplate.POST_INSIGHT_PAGE_ID_IMPRESSION_SUM);
        Integer currImpression = reportsEsService.getSumImpressionOnPageId(engagementRequest);
        engagementRequest.setSearchTemplate(SearchTemplate.POST_INSIGHT_PAGE_ID_CLICK_COUNT_SUM);
        Integer currClickCount = reportsEsService.getSumClickCountOnPageId(engagementRequest);
        engagementRequest.setSearchTemplate(SearchTemplate.SUM_POST_INSIGHTS_PAGE_ID);
        Integer currVideoViews = getVideoViewsCount(twitterAccount);
        PostInsightsDataPerPage postInsightsDataPerPage = reportsEsService.getSumPostInsightsOnPageId(engagementRequest);

        Date endDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());

        SimpleDateFormat dateFormatter = new SimpleDateFormat(dateFormatterString);
        DataModel dataModel = DataModel.builder()
                .pageIds(twitterAccount.getProfileId().toString())
                .endDate("\""+dateFormatter.format(endDate)+"\"")
                .sourceIds(Integer.toString(SocialChannel.TWITTER.getId()))
                .build();
        InsightsESRequest insightsESRequest = new InsightsESRequest(dataModel, SearchTemplate.PAGE_INSIGHT_FOR_PREV_DATE);
        insightsESRequest.setIndex(ElasticConstants.TWITTER_PAGE_INSIGHTS.getName());
        insightsESRequest.setRouting(twitterAccount.getEnterpriseId().toString());
        List<PageInsightDataPoint> pageInsightDataPoints = reportsEsService.getPageInsightHitsDataFromEs(insightsESRequest);

        checkIfProfileInfoIsNullAndSetFollowers_count(pageInsightDataPoints, profileInfoResponse);

        mediaCount = checkIfMediaCountIsNullAndGetMediaCount(pageInsightDataPoints, mediaCount);

        List<PageLevelMetaData> pageLevelMetaDataList = new ArrayList<>();
        PageLevelMetaData pageLevelMetaData = new PageLevelMetaData();
        pageLevelMetaDataList.add(pageLevelMetaData);
        calculatePageLevelDelta(pageLevelMetaDataList, pageInsightDataPoints, mediaCount, currEngagement,currImpression,
                profileInfoResponse.getFollowers_count(), currClickCount, currVideoViews,postInsightsDataPerPage);

        if(CollectionUtils.isEmpty(pageLevelMetaDataList)) {
            log.info("could not proceed since pageLevelMetaDataList is null for twitter account{}", twitterAccount);
            return;
        }

        PageInsights pageInsights = new PageInsights(twitterAccount.getEnterpriseId(),String.valueOf(twitterAccount.getProfileId()),twitterAccount.getBusinessId(), SocialChannel.TWITTER.getName(),pageLevelMetaDataList);
        kafkaProducerService.sendObjectV1(Constants.TWITTER_PAGE_INSIGHTS,pageInsights);
        addInsightsToDB(pageInsights);
    }

    private int getVideoViewsCount(BusinessTwitterAccounts twitterAccount) {
        int videoViews = 0;
        try {

            List<BusinessPosts> businessPosts = businessPostsRepository
                    .findByExternalPageIdAndSourceIdAndVideoUrlsIsNotNullOrderByPublishDateDesc(twitterAccount.getProfileId().toString()
                            ,SocialChannel.TWITTER.getId());

            if(CollectionUtils.isEmpty(businessPosts)) {
                return videoViews;
            }

            if(businessPosts.size()> Constants.TWITTER_MAX_POST_VIDEO_INSIGHTS) {
                businessPosts = businessPosts.subList(0,Constants.TWITTER_MAX_POST_VIDEO_INSIGHTS);
            }

            List<String> postIds = businessPosts.stream().map(BusinessPosts::getPostId).collect(Collectors.toList());

            log.info("Video post found for postIds: {}", postIds);

            TwitterCreds twitterCreds = twitterCommonService.getTwitterCreds(twitterAccount);
            videoViews = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(() ->
                        twitterService.getTweetVideoViewsMetric(twitterCreds, String.join(",", postIds)));

        } catch (BirdeyeSocialException ex) {
            log.info("Authentication Exception occurred while getting video views count for pageId: {}", twitterAccount.getProfileId(), ex);
            if (Objects.nonNull(ex.getCode()) && (ex.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value())) {
                commonService.handleExpiredTokenForTwitter(twitterAccount);
            }
        } catch (Exception ex) {
            log.info("Unknown error occurred while getting video views count for pageId: {}", twitterAccount.getProfileId(), ex);
        }

        return videoViews;
    }

    private void checkIfProfileInfoIsNullAndSetFollowers_count(List<PageInsightDataPoint> pageInsightDataPoints, @NotNull TwitterProfileInfoResponse profileInfoResponse) {
        if(Objects.isNull(profileInfoResponse.getFollowers_count()) && CollectionUtils.isNotEmpty(pageInsightDataPoints)){
            profileInfoResponse.setFollowers_count(Objects.nonNull(pageInsightDataPoints.get(0).getTotalFollowers()) ? pageInsightDataPoints.get(0).getTotalFollowers() : 0);
        }
        if(Objects.isNull(profileInfoResponse.getFollowers_count()) ) {
         profileInfoResponse.setFollowers_count(0);
        }
    }

    private int checkIfMediaCountIsNullAndGetMediaCount(List<PageInsightDataPoint> pageInsightDataPoints, Integer mediaCount) {
        if(Objects.isNull(mediaCount) && CollectionUtils.isNotEmpty(pageInsightDataPoints)){
            mediaCount = Objects.nonNull(pageInsightDataPoints.get(0).getTotalPosts()) ? pageInsightDataPoints.get(0).getTotalPosts() : 0;
        }
        return Objects.isNull(mediaCount)? 0 : mediaCount;
    }


    private void calculatePageLevelDelta(List<PageLevelMetaData> pageLevelMetaDataList, List<PageInsightDataPoint> pageInsightDataPoints,
                                                    Integer mediaCount, Integer currEngagement, Integer currImpression, Integer currFollowerCount,
                                         Integer currClickCount, Integer currVideoViews,PostInsightsDataPerPage currentPostPageInsights) {
        Integer prevMediaCount = 0;
        Integer totalEngagement = 0;
        Integer totalImpression = 0;
        Integer prevFollowerCount = 0;
        Integer prevClickCount = 0;
        Integer totalVideoViews = 0;
        PagePostInsightsTotalData pagePostInsightsTotalData = new PagePostInsightsTotalData();

        if(CollectionUtils.isNotEmpty(pageInsightDataPoints)) {
            PageInsightDataPoint pageInsightDataPoint = pageInsightDataPoints.get(0);
            prevMediaCount = pageInsightDataPoint.getTotalPosts()==null?0:pageInsightDataPoint.getTotalPosts();
            totalEngagement = pageInsightDataPoint.getPostEngagementTotal()==null?0:pageInsightDataPoint.getPostEngagementTotal();
            totalImpression = pageInsightDataPoint.getPostImpressionTotal()==null?0:pageInsightDataPoint.getPostImpressionTotal();
            prevFollowerCount = pageInsightDataPoint.getTotalFollowers()==null?0:pageInsightDataPoint.getTotalFollowers();
            prevClickCount = pageInsightDataPoint.getClickCount() == null ? 0 : pageInsightDataPoint.getClickCount();
            totalVideoViews = pageInsightDataPoint.getTotalVideoViews() == null ? 0 : pageInsightDataPoint.getTotalVideoViews();
            pagePostInsightsTotalData = reportDataConverter.convertPageInsightDataPoint(pageInsightDataPoint);
        }
        Integer postGain = mediaCount - prevMediaCount;
        Integer engagementGain = totalEngagement.equals(currEngagement) ? 0 : currEngagement - totalEngagement;
        Integer impressionGain = totalImpression == currImpression ? 0 : currImpression - totalImpression;
        Integer followerGain =  currFollowerCount - prevFollowerCount;
        Integer clickCountGain = currClickCount - prevClickCount;
        Integer videoViewsGain = totalVideoViews == currVideoViews ? 0 : currVideoViews - totalVideoViews;
        PagePostInsightsGainData pagePostInsightsGainData = reportDataConverter.convertToPagePostInsightsGain(pagePostInsightsTotalData,currentPostPageInsights);
        for(PageLevelMetaData pageLevelMetaData: pageLevelMetaDataList) {
            pageLevelMetaData.setDate(new Date());
            pageLevelMetaData.setPostEngagementTotal(currEngagement);
            pageLevelMetaData.setPostTotalCount(mediaCount);
            pageLevelMetaData.setPostImpressionTotal(currImpression);
            pageLevelMetaData.setTotalFollower(currFollowerCount);
            pageLevelMetaData.setPostEngagements(engagementGain>=0?engagementGain:0);
            pageLevelMetaData.setPostCount(postGain>=0?postGain:0);
            pageLevelMetaData.setPostImpressions(impressionGain>=0?impressionGain:0);
            pageLevelMetaData.setFollowerGainCount(followerGain>=0?followerGain:0);
            pageLevelMetaData.setLinkClickCount(clickCountGain);
            pageLevelMetaData.setProfileVideoViews(videoViewsGain);
            pageLevelMetaData.setTotalProfileVideoViews(currVideoViews);
            pageLevelMetaData.setTotalPostLikeCount(currentPostPageInsights.getLikeCount());
            pageLevelMetaData.setTotalPostCommentCount(currentPostPageInsights.getCommentCount());
            pageLevelMetaData.setTotalPostShareCount(currentPostPageInsights.getShareCount());
            pageLevelMetaData.setPagePostLikeCount(pagePostInsightsGainData.getPostLikeCountGain());
            pageLevelMetaData.setCommentCount(pagePostInsightsGainData.getPostCommentCountGain());
            pageLevelMetaData.setShareCount(pagePostInsightsGainData.getPostShareCountGain());
        }
    }

    private void addInsightsToDB(PageInsights pageInsights) {
        TwitterPageInsight twitterPageInsight = dbDataConverter.convertPageInsightForTwitter(pageInsights);
        twitterPageInsightRepo.save(twitterPageInsight);
    }
    @Override
    public void backfillXInsight(BackfillInsightReq socialScanEventDTO) {
        Integer minBackfillDays= reportPropertyRepository.findMinDaysForSourceIdAndReportType(
                socialScanEventDTO.getSourceId(),"profile_matric");
        if(InsightsReportUtil.validateStartAndEndDate(socialScanEventDTO.getStartDate(),socialScanEventDTO.getEndDate(),minBackfillDays)) {
            List<BusinessTwitterAccounts> twitterAccounts = twitterAccountRepository.findByProfileId(Long.parseLong(socialScanEventDTO.getExternalId()));
            log.info("twitterAccount: {}",twitterAccounts);
            if(CollectionUtils.isEmpty(twitterAccounts) || twitterAccounts.get(0).getIsValid()!=1 || Objects.isNull(twitterAccounts.get(0).getBusinessId())) {
                log.info("Valid data not found for profile id: {}",socialScanEventDTO.getExternalId());
                return;
            }
            try {
                TwitterCreds twitterCreds = twitterCommonService.getTwitterCreds(twitterAccounts.get(0));
                long diff = socialScanEventDTO.getEndDate().getTime() - socialScanEventDTO.getStartDate().getTime();
                int daysLeft = (int) TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
                Date initialDate = socialScanEventDTO.getStartDate();
                List<MediaTweet> mediaTweets =  new ArrayList<>();
                do{
                    if (daysLeft <= 28) {
                         getTweetAndSendForInsights(twitterCreds, twitterAccounts, mediaTweets,
                                DateTimeUtils.getTwitterReportDateFormat(initialDate),
                                DateTimeUtils.getTwitterReportDateFormat(socialScanEventDTO.getEndDate()));
                        break;
                    }
                    Date newEndDate = DateUtils.addDays(initialDate, 28);
                    getTweetAndSendForInsights(twitterCreds, twitterAccounts, mediaTweets,
                            DateTimeUtils.getTwitterReportDateFormat(initialDate),
                            DateTimeUtils.getTwitterReportDateFormat(newEndDate));
                    initialDate = newEndDate;
                    daysLeft -= 28;
                }while (true);
            } catch (Exception e) {
                log.info("Exception occurred while getting post insight data of Twitter profile: {}",socialScanEventDTO.getExternalId());
            }
        }
    }

    @Override
    public void backFillXHistorical(HistoricalTweetInsight historicalTweetInsight) {
        List<BusinessTwitterAccounts> twitterAccounts = twitterAccountRepository.findByProfileId(historicalTweetInsight.getProfileId());
        BusinessTwitterAccounts twitterAccount = twitterAccounts.get(0);
        TwitterCreds twitterCreds = twitterCommonService.getTwitterCreds(twitterAccount);
        TwitterPostInsightRequest request = getPostInsightObject(historicalTweetInsight);
        log.info("Post insight request for twitter historical: {}",request);
        TwitterHistoricalPostInsightResponse postInsightResponse = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(() ->
                twitterService.getPostInsightHistorical(twitterCreds,request));
        if(Objects.nonNull(postInsightResponse) && Objects.nonNull(postInsightResponse.getInsights())) {
            List<PageLevelMetaData> pageLevelMetaDataList = reportDataConverter.prepareDayWiseDataXHistorical(postInsightResponse,
                    twitterAccount.getProfileId(), twitterAccount.getBusinessId());
            PageInsights pageInsights = new PageInsights(twitterAccount.getEnterpriseId(), String.valueOf(twitterAccount.getProfileId()),
                    twitterAccount.getBusinessId(), SocialChannel.TWITTER.getName(), pageLevelMetaDataList);
            kafkaProducerService.sendObjectV1(Constants.TWITTER_PAGE_INSIGHTS, pageInsights);
            addInsightsToDB(pageInsights);
        }
    }

    private void getTweetAndSendForInsights(TwitterCreds twitterCreds,
                                            List<BusinessTwitterAccounts> twitterAccounts, List<MediaTweet> mediaTweets,
                                            String startTime,String endTime) {
        List<MediaTweet> tweets = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(
                () -> twitterService.getUserTimelineV21(twitterCreds, null, String.valueOf(twitterAccounts.get(0).getProfileId()),
                        null, startTime, endTime, null)
        );
        if (CollectionUtils.isNotEmpty(tweets))
            mediaTweets.addAll(tweets);
        List<List<MediaTweet>> batches = Lists.partition(mediaTweets, Constants.TWITTER_MAX_POSTS_HISTORICAL);
        for (List<MediaTweet> tweetGroup:batches) {
            HistoricalTweetInsight pageInsights = getHistoricalTweetInsightObj(tweetGroup, twitterAccounts,startTime,endTime);
            kafkaProducerService.sendObjectV1(HISTORICAL_TWITTER_PAGE_INSIGHTS,pageInsights);
        }
    }

    @NotNull
    private HistoricalTweetInsight getHistoricalTweetInsightObj(List<MediaTweet> tweetGroup, List<BusinessTwitterAccounts> twitterAccounts,
                                                                String startTime, String endTime) {
        List<String> tweetIds = tweetGroup.stream().map(MediaTweet::getId).collect(Collectors.toList());
        return new HistoricalTweetInsight(tweetIds, twitterAccounts.get(0).getProfileId(),startTime.substring(0,10),endTime.substring(0,10));
    }

    private TwitterPostInsightRequest getPostInsightObject(HistoricalTweetInsight historicalTweetInsight) {
        TwitterPostInsightRequest postInsightRequest = new TwitterPostInsightRequest();
        postInsightRequest.setTweet_ids(historicalTweetInsight.getTweetIds());
        List<String> grouping = Arrays.asList(InsightsConstants.POST_GROUPING2_TWITTER,
                InsightsConstants.POST_GROUPING3_TWITTER);
        Insights insights = new Insights(grouping);
        InsightGrouping insightGrouping = new InsightGrouping(insights);
        postInsightRequest.setGroupings(insightGrouping);
        postInsightRequest.setEngagement_types(Arrays.asList(InsightsConstants.POST_ENGAGEMENTS_TWITTER,
                InsightsConstants.POST_IMPRESSIONS_TWITTER,InsightsConstants.POST_FAVORITES_TWITTER,
                InsightsConstants.POST_REPLIES_TWITTER,InsightsConstants.POST_RETWEETS_TWITTER,InsightsConstants.IG_VIDEO_VIEWS));
        postInsightRequest.setStart(historicalTweetInsight.getStart());
        postInsightRequest.setEnd(historicalTweetInsight.getEnd());
        return postInsightRequest;
    }
}
