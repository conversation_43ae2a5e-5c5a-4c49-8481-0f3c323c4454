package com.birdeye.social.service;

import com.birdeye.social.dto.GoogleMessagesAgentLiteDto;
import com.birdeye.social.entities.GoogleMessagesAgent;
import com.birdeye.social.model.*;
import com.google.api.services.businesscommunications.v1.model.Agent;
import com.google.api.services.businesscommunications.v1.model.Brand;
import org.springframework.data.domain.Page;

import java.util.Date;
import java.util.List;
import java.util.Set;

// Service-layer on top of GoogleMessagesAgentRepository
public interface GoogleMessagesAgentService {

	GoogleMessagesAgentLiteDto findDTOByEnterpriseId(Long enterpriseId);

	void deleteAgent(GoogleMessagesAgent gmAgent);

	void evictAgentCache(Long enterpriseId);

	Boolean agentExistsByEnterpriseId(Long enterpriseId);

	Boolean agentExistsByDisplayName(String displayName);

	Boolean agentExistsByEnterpriseIdAndStatus(Long enterpriseId, GoogleAgentStatus status);

	GoogleMessagesAgent saveAgentInfo(GoogleMessagesAgent gmAgent, GoogleAgentStatus status, SetupAgentRequest req, Agent agent, String comments);

	void saveAuditInfo(CreateGoogleMessagingAuditRequest req);

	void saveWidgetInfo(GoogleMessagesAgent gmAgent, EditWidgetRequest req);

	GoogleMessagesAgent updateAgentInfo(GoogleMessagesAgent gmAgent, UpdateAgentRequest req, String comments);

	GoogleMessagesAgent findByEnterpriseId(Long enterpriseId);

	List<GoogleMessagesAgent> findByEnterpriseIdIn(Long enterpriseId);

	GoogleMessagesAgent findByEnterpriseIdAndStatus(Long enterpriseId, String status);

	GoogleMessagesAgent updateStatus(Integer agentId, GoogleAgentStatus newStatus);

	void updateStatus(Integer agentId, String newStatus);

	GoogleMessagesAgent updateStatus(GoogleMessagesAgent gmAgent, GoogleAgentStatus newStatus);

	GoogleMessagesAgent updateComments(Long enterpriseId, String comments);

	GoogleMessagesAgent updateComments(GoogleMessagesAgent gmAgent, String comments);

	Boolean brandExistsByEnterpriseId(Long enterpriseId);

	Boolean brandExistsByDisplayName(String displayName);

	GoogleMessagesAgent addBrandInfo(GoogleMessagesAgent gmAgent, GoogleAgentStatus status, CreateBrandRequest req, Brand brand, String comments);

	void addAuditInfo(CreateGoogleMessagingAuditRequest req);

	String getBrandName(Integer agentId);

	void updateStatusAndName(GoogleMessagesAgent gmAgent);

    GoogleMessagesAgent getAgent(Long enterpriseId);

	Long findEnterpriseIdByAgentId(String agentId);

	GoogleMessagesAgent findByAgentId(String agentId);

	GoogleMessagesAgent getAgentById(Integer agentId);

	List<Long> findAllEnterpriseIds();

	List<Long> findAllByIds(int size, Date date);

    List<GoogleMessagesAgent> findByIdIn(Set<Integer> keySet);

    GoogleMessagesAgent findByAgentId(Integer agentId);

	List<Integer> findIdByEnterpriseId(Long id);

	List<GoogleMessagesAgent> findAllByEnterpriseId(Long enterpriseId);

	List<GoogleMessagesAgent> findByEnterpriseIdAndWidgetName(Long enterpriseId, String widgetName);

	GoogleMessagesAgent saveWidgetInfo(GoogleMessagesAgent agent);

	Page<GoogleMessagesAgent> findAllAgents(int page, int size);

	Page<GoogleMessagesAgent> findAgentsByEnterpriseIdIn(List<Long> enterpriseIds);

	void updateWidgetName(String brandDisplayName, Integer id);

	GoogleMessagesAgentLiteDto findDTOByAgentName(String agentName);
}
