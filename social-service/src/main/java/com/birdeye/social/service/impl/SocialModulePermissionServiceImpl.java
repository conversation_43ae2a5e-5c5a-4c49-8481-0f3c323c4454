package com.birdeye.social.service.impl;

import com.birdeye.social.dao.SocialModulePermissionRepo;
import com.birdeye.social.entities.PermissionMapping;
import com.birdeye.social.entities.SocialModulePermission;
import com.birdeye.social.service.ISocialModulePermissionService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@Service("SocialModulePermissionService")
public class SocialModulePermissionServiceImpl implements ISocialModulePermissionService {

    @Autowired
    private SocialModulePermissionRepo SocialModulePermissionRepo;

	@Override
	@Cacheable(value = "modulePermissions", key = "#sourceId+'_'+#module", unless = "#result == null") 
	public SocialModulePermission getPermissionsForChannelAndModule(Integer sourceId, String module) {
		List<SocialModulePermission> socialModulePermissions = SocialModulePermissionRepo.findBySourceIdAndModule(sourceId, module);
		if(CollectionUtils.isNotEmpty(socialModulePermissions)) {
            return socialModulePermissions.get(0);
        }
		return null;
	}

	@Override
	public List<SocialModulePermission> getPermissionsForChannel(Integer sourceId) {
		List<SocialModulePermission> socialModulePermissions = SocialModulePermissionRepo.findBySourceId(sourceId);
        return socialModulePermissions;
	}

	@Override
	public List<SocialModulePermission> getPermissionsForChannelAndModuleIn(Integer sourceId, Collection<String> module) {
		List<SocialModulePermission> socialModulePermissions = SocialModulePermissionRepo.findBySourceIdAndModuleIn(sourceId, module);
		return socialModulePermissions;
	}
}
