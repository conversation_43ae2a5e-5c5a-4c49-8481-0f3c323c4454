package com.birdeye.social.service.media;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SocialDomainAppCredsCache;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.entities.PermissionMapping;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.mediaupload.SocialAssetChunkInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadRequest;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.mediaupload.SocialAssetChunkInfoService;
import com.birdeye.social.external.mediaupload.SocialMediaUploadInfoService;
import com.birdeye.social.external.mediaupload.SocialMediaUploadRequestService;
import com.birdeye.social.external.request.google.GoogleCredInfo;
import com.birdeye.social.external.request.mediaupload.MediaUploadRequest;
import com.birdeye.social.external.request.mediaupload.MediaUploadChunkRequest;
import com.birdeye.social.external.request.mediaupload.MediaUploadRequestForAsset;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.external.service.PicturesqueGen;
import com.birdeye.social.external.service.SocialRawPageDetail;
import com.birdeye.social.model.MentionData;
import com.birdeye.social.service.*;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.Collections;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.*;
import static com.birdeye.social.constant.MediaUploadStatusEnum.*;
import static com.birdeye.social.exception.ErrorCodes.*;
import static com.birdeye.social.service.DtoToEntityConverter.convertToSocialAssetChunkInfo;
import static com.birdeye.social.service.DtoToEntityConverter.createOrUpdateSocialMediaUploadRequest;

@Service
public class MediaUploadServiceImpl implements MediaUploadService {

    private static final String INVALID_REQUEST = "Invalid Request";
    public static final String NO_PAGE_FOUND_WITH_REQUEST = "No page found with request : {}";
    @Autowired
    private SocialMediaUploadInfoService socialMediaUploadInfoService;
    @Autowired
    private SocialMediaUploadRequestService socialMediaUploadRequestService;
    @Autowired
    private SocialAssetChunkInfoService socialAssetChunkInfoService;
    @Autowired
    private SocialRawPageFactory socialRawPageFactory;
    @Autowired
    private PicturesqueGen picturesqueGen;
    @Autowired
    private KafkaProducerService kafkaProducerService;
    @Autowired
    private SocialPostPublishInfoService socialPostPublishInfoService;
    @Autowired
    private IPermissionMappingService permissionMappingService;
    @Autowired
    private IRedisExternalService redisExternalService;

    private static final Logger logger = LoggerFactory.getLogger(MediaUploadServiceImpl.class);

    @Override
    public void initiateMediaUpload(MediaUploadRequest mediaInitiateRequest) {
        String channel = SocialChannel.getSocialChannelNameById(mediaInitiateRequest.getSourceId());
        SocialMediaUploadService execute = socialRawPageFactory.getSocialRawPageDetails(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        SocialRawPageDetail socialRawPageDetail = execute.getPageDetails(mediaInitiateRequest.getPageId());
        if (Objects.isNull(socialRawPageDetail)) {
            logger.info(NO_PAGE_FOUND_WITH_REQUEST, mediaInitiateRequest);
            failureReasonUpdate(mediaInitiateRequest.getPublishInfoId(), ErrorCodes.SOCIAL_RAW_PAGE_NOT_FOUND, channel);
            return;
        }
        try {
            logger.info("Register media and send request to pictures for asset id:{} and publish info id :{} "
                    , mediaInitiateRequest.getAssetId(), mediaInitiateRequest.getPublishInfoId());
            registerMediaAndInitRequestForMediaUpload(mediaInitiateRequest, socialRawPageDetail, execute, channel);
        } catch (BirdeyeSocialException bse) {
            execute.evictPageCache(mediaInitiateRequest.getPageId());
            removeRedisKeys(mediaInitiateRequest);
            execute.birdeyeExceptionHandler(bse, mediaInitiateRequest.getPublishInfoId(), mediaInitiateRequest.getPageId());
        } catch (Exception ex) {
            execute.evictPageCache(mediaInitiateRequest.getPageId());
            removeRedisKeys(mediaInitiateRequest);
            execute.generalExceptionHandler(ex.getMessage(), mediaInitiateRequest.getPublishInfoId());
        }
    }

    private void removeRedisKeys(MediaUploadRequest mediaInitiateRequest) {
        removeValueForKey(mediaInitiateRequest.getAssetId(),mediaInitiateRequest.getRequestId(), mediaInitiateRequest.getSourceId());
    }

    private void registerMediaAndInitRequestForMediaUpload(MediaUploadRequest mediaInitiateRequest,
                                                           SocialRawPageDetail socialRawPageDetail,
                                                           SocialMediaUploadService execute, String channel) throws Exception {
        SocialMediaUploadRequest socialMediaUploadRequest = new SocialMediaUploadRequest();
        if (SocialChannel.LINKEDIN.getId() == mediaInitiateRequest.getSourceId()) {
            createOrUpdateSocialMediaUploadRequest(socialMediaUploadRequest, mediaInitiateRequest, MEDIA_INIT, socialRawPageDetail);
            socialMediaUploadRequestService.save(socialMediaUploadRequest);
            execute.registerMedia(socialMediaUploadRequest.getId(), socialRawPageDetail, mediaInitiateRequest);
        } else if (SocialChannel.TWITTER.getId() == mediaInitiateRequest.getSourceId()) {
            execute.registerMedia(null, socialRawPageDetail, mediaInitiateRequest);
            createOrUpdateSocialMediaUploadRequest(socialMediaUploadRequest, mediaInitiateRequest, MEDIA_INIT, socialRawPageDetail);
            socialMediaUploadRequestService.save(socialMediaUploadRequest);
        }
        logger.info("Request saved in DB for multiple upload : {}",socialMediaUploadRequest.getId());
        redisExternalService.set(requestIdWithTotalChunks(socialMediaUploadRequest.getId()),Long.valueOf(mediaInitiateRequest.getTotalParts()));
        if(Objects.equals(mediaInitiateRequest.getTotalParts(),
                socialAssetChunkInfoService.getCountOfAssetChunks(mediaInitiateRequest.getAssetId()))) {
            mediaUploadToSocialChannel(socialMediaUploadRequest.getId(),mediaInitiateRequest.getAssetId(), mediaInitiateRequest.getSourceId());
        } else {
            String requestAndAssetIdKey = putRequestIdWithAssetIdKey(mediaInitiateRequest.getAssetId(), mediaInitiateRequest.getSourceId());
            redisExternalService.setKeyAndValue(requestAndAssetIdKey, String.valueOf(socialMediaUploadRequest.getId()));
            logger.info("key :{}, value:{}", requestAndAssetIdKey, socialMediaUploadRequest.getId());
            String totalPartsKey = totalMediaChunkKeyForAsset(mediaInitiateRequest.getAssetId(), mediaInitiateRequest.getSourceId());
            logger.info("totalPartsKey : {}", totalPartsKey);
            if(!redisExternalService.isKeyPresent(totalPartsKey)) {
                redisExternalService.set(totalPartsKey, Long.valueOf(mediaInitiateRequest.getTotalParts()));
                downloadAndCreateChunkRequestToPictures(mediaInitiateRequest, socialMediaUploadRequest, channel);
            }
        }
        if(!MEDIA_UPLOAD_FAILED.name().equalsIgnoreCase(socialMediaUploadRequest.getUploadStatus()))
            createOrUpdateSocialMediaUploadRequest(socialMediaUploadRequest, mediaInitiateRequest,
                    MEDIA_UPLOAD_IN_PROGRESS, socialRawPageDetail);
        socialMediaUploadRequestService.save(socialMediaUploadRequest);
        mediaInitiateRequest.setRequestId(socialMediaUploadRequest.getId());
    }

    private String requestIdWithTotalChunks(Integer id) {
        return REQUEST_TOTAL_PART_CHUNKS + id;
    }

    private void downloadAndCreateChunkRequestToPictures(MediaUploadRequest mediaInitiateRequest,
                                                         SocialMediaUploadRequest socialMediaUploadRequest,
                                                         String channel) {
        String callbackUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                .getProperty(SystemPropertiesCache.MEDIA_UPLOAD_CALL_BACK_URL);
        String callbackId = getCallbackId(mediaInitiateRequest);
        Long chunkSize = Long.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                .getProperty(SystemPropertiesCache.MEDIA_UPLOAD_CHUNK_SIZE));
        if (picturesqueGen.uploadLargeVideoUrl(mediaInitiateRequest.getMediaUrl(), callbackUrl, callbackId, chunkSize)) {
            logger.info("Request successfully registered media on pictures service : {}", socialMediaUploadRequest.getId());
        } else {
            logger.info("Request failed to register media on pictures service : {}", socialMediaUploadRequest.getId());
            socialMediaUploadRequest.setUploadStatus(MEDIA_UPLOAD_FAILED.name());
            failureReasonUpdate(mediaInitiateRequest.getPublishInfoId(), ErrorCodes.UPLOAD_FAILED_AT_PICTURES_SERVICE, channel);
        }
    }

    @NotNull
    private static String totalMediaChunkKeyForAsset(Integer assetId, Integer sourceId) {
        return TOTAL_PART_CHUNKS + assetId + "_" + sourceId;
    }

    @NotNull
    private static String putRequestIdWithAssetIdKey(Integer assetId, Integer sourceId){
        return LIST_OF_REQUEST_IDS + assetId + "_" + sourceId;
    }

    private void failureReasonUpdate(Integer publishInfoId,ErrorCodes codes,String channel) {
        logger.info("Failed for publish info id :{} and with error code :{}",publishInfoId,codes);
        SocialPostPublishInfo socialPostPublishInfo = socialPostPublishInfoService.findById(publishInfoId);
        if(Objects.isNull(socialPostPublishInfo)
                || Objects.equals(socialPostPublishInfo.getIsPublished(),2)
                || Objects.equals(socialPostPublishInfo.getIsPublished(),1)  ){
            logger.info("No data found for the request in publish info:{}",publishInfoId);
            return;
        }
        PermissionMapping permissionMapping = permissionMappingService.getErrorMessageAndCode(codes.name(),
                codes.value(), SocialModuleEnum.PUBLISH.name());
        if(Objects.nonNull(permissionMapping)){
            socialPostPublishInfo.setBucket(permissionMapping.getBucket());
            socialPostPublishInfo.setFailureReason(permissionMapping.getErrorMessage());
        }
        socialPostPublishInfo.setIsPublished(2);
        socialPostPublishInfoService.save(socialPostPublishInfo);
        SocialMediaUploadService execute = socialRawPageFactory.getSocialRawPageDetails(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        SocialMediaUploadRequest socialMediaUploadRequest = socialMediaUploadRequestService.findByPublishInfoId(publishInfoId);
        redisUpdateAndCacheEvict(socialMediaUploadRequest,execute);
    }

    @Override
    public void saveMediaChunkFromPictures(MediaUploadChunkRequest mediaUploadChunkRequest) {
        if(StringUtils.isEmpty(mediaUploadChunkRequest.getId())) {
            logger.info("Request doesn't contains required data :{}",mediaUploadChunkRequest);
            return;
        }
        String[] mediaId = mediaUploadChunkRequest.getId().split("_");
        Integer assetId = Integer.valueOf(mediaId[1]);
        Integer sourceId = Integer.valueOf(mediaId[2]);
        if(mediaUploadChunkRequest.isError()){
            logger.info("Error from pictures while creating chunks for asset id:{}",assetId);
            failureHandlingForRequest(assetId, sourceId);
            return;
        }
        String totalMediaChunkKeyForAsset = totalMediaChunkKeyForAsset(assetId, sourceId);
        logger.info("[saveMediaChunkFromPictures] totalMediaChunkKeyForAsset: {}", totalMediaChunkKeyForAsset);
        Optional<Object> value = redisExternalService.get(totalMediaChunkKeyForAsset);
        if(!value.isPresent()){
            logger.info("No key is present for asset id : {}",assetId);
            // fail for all request id present in cache
            failureHandlingForRequest(assetId, sourceId);
            return;
        }
        try{
            logger.info("Key value of asset id: {} and value:{}", assetId, value);
            SocialAssetChunkInfo socialAssetChunkInfo = convertToSocialAssetChunkInfo(mediaUploadChunkRequest, assetId, sourceId);
            socialAssetChunkInfoService.save(socialAssetChunkInfo);
            Integer chunkLeft = redisExternalService.decrement(totalMediaChunkKeyForAsset(assetId, sourceId));
            logger.info("Chunks left :{} for asset id :{}", chunkLeft, assetId);
            if (chunkLeft == 0) {
                logger.info("Chunks are saved in DB for asset id : {}", socialAssetChunkInfo.getAssetId());
                //push to kafka to upload
                List<Integer> requestIds = getRedisValuesForKey(assetId, sourceId);
                if (CollectionUtils.isEmpty(requestIds)) return;
                logger.info("Request ids found :{}", requestIds);
                requestIds.forEach(requestId -> {
                    logger.info("Send Request to upload media for request id:{}", requestId);
                    mediaUploadToSocialChannel(requestId, assetId, sourceId);
                });
                logger.info("Delete key :{}", assetId);
                redisExternalService.delete(totalMediaChunkKeyForAsset(assetId, sourceId));
            }
        }catch (Exception e){
            logger.info("Exception occurred while saving data to DB :",e);
            failureHandlingForRequest(assetId, sourceId);
            throw e;
        }
    }

    private List<Integer> getRedisValuesForKey(Integer assetId, Integer sourceId) {
        Optional<Object> listOfRequestIds = redisExternalService.get(putRequestIdWithAssetIdKey(assetId, sourceId));
        logger.info("List of request ids : {}",listOfRequestIds);
        if(!listOfRequestIds.isPresent() || StringUtils.isEmpty(String.valueOf(listOfRequestIds.get()))){
            logger.info("listOfRequestIds is empty for asset id : {}",assetId);
            // fail for all request id present in cache
            failureHandlingForRequest(assetId, sourceId);
            return Collections.emptyList();
        }
        logger.info("List of request ids after null check:{} with asset id :{} ",listOfRequestIds,assetId);
        List<Integer> requestIds = splitStringAndConvertToArrayList(String.valueOf(listOfRequestIds.get()));
        if(CollectionUtils.isEmpty(requestIds)){
            failureHandlingForRequest(assetId, sourceId);
            return Collections.emptyList();
        }
        return requestIds;
    }

    private List<Integer> splitStringAndConvertToArrayList(String listOfRequestIds) {
        logger.info("Request ids :{}",listOfRequestIds);
        return Arrays.stream(listOfRequestIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
    }

    private void failureHandlingForRequest(Integer assetId, Integer sourceId){
        logger.info("Media upload failed for asset Id :{}",assetId);
        List<SocialMediaUploadRequest> socialMediaUploadRequestList = socialMediaUploadRequestService.findByAssetId(assetId);
        if(CollectionUtils.isEmpty(socialMediaUploadRequestList)){
            return;
        }
        socialMediaUploadRequestList.forEach(request -> {
            logger.info("Request and publish info is marked as failed for request id :{} ",request.getId());
            if(MEDIA_UPLOAD_IN_PROGRESS.name().equalsIgnoreCase(request.getUploadStatus()))
                failureReasonUpdate(request.getPublishInfoId(),ErrorCodes.VIDEO_UNAVAILABLE,SocialChannel.getSocialChannelNameById(request.getSourceId()));
        });
        redisExternalService.delete(totalMediaChunkKeyForAsset(assetId, sourceId));
    }

    private void mediaUploadToSocialChannel(Integer requestId, Integer assetId,Integer sourceId) {
        MediaUploadRequestForAsset request = new MediaUploadRequestForAsset();
        request.setAssetId(assetId);
        request.setRequestId(requestId);
        request.setSourceId(sourceId);
        kafkaProducerService.sendObjectWithKeyV1(String.valueOf(assetId),KafkaTopicEnum.SOCIAL_MEDIA_UPLOAD_CHUNK_REQUEST.getName(),request);
    }

    @Override
    public void processVideoChunkUpload(MediaUploadRequestForAsset request) {
        if (Objects.isNull(request)) {
            logger.info("Request is null");
            return;
        }

        String channel = SocialChannel.getSocialChannelNameById(request.getSourceId());
        SocialMediaUploadRequest socialMediaUploadRequest = socialMediaUploadRequestService.findById(request.getRequestId());

        if (Objects.isNull(socialMediaUploadRequest)) {
            updateFailureReasonAndMediaRequest(request.getRequestId(), channel, ErrorCodes.NO_MEDIA_UPLOAD_REQUEST_FOUND);
            return;
        }

        if (!MEDIA_UPLOAD_IN_PROGRESS.name().equals(socialMediaUploadRequest.getUploadStatus())) {
            updateFailureReasonAndMediaRequest(request.getRequestId(), channel, ErrorCodes.MEDIA_UPLOAD_ALREADY_COMPLETED);
            return;
        }

        int sourceId = socialMediaUploadRequest.getSourceId();
        if (sourceId == SocialChannel.LINKEDIN.getId()) {
            processLinkedInUpload(request, socialMediaUploadRequest);
        } else if (sourceId == SocialChannel.TWITTER.getId()) {
            processTwitterUpload(request, socialMediaUploadRequest);
        }
    }

    private void processLinkedInUpload(MediaUploadRequestForAsset request, SocialMediaUploadRequest socialMediaUploadRequest) {
        List<SocialMediaUploadInfo> socialMediaUploadInfoList = socialMediaUploadInfoService.findByUploadRequestId(request.getRequestId());

        if (CollectionUtils.isEmpty(socialMediaUploadInfoList)) {
            logger.info("No LinkedIn chunk information found for request ID: {}", request.getRequestId());
            return;
        }

        for (SocialMediaUploadInfo info : socialMediaUploadInfoList) {
            logger.info("LinkedIn Upload request for chunk number: {} and request ID: {}", info.getSequenceId(), request.getRequestId());

            request.setSequenceId(info.getSequenceId());
            request.setPageId(socialMediaUploadRequest.getPageId());
            request.setSourceId(socialMediaUploadRequest.getSourceId());

            kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_MEDIA_UPLOAD_CHUNK.getName(), request);
        }
    }

    private void processTwitterUpload(MediaUploadRequestForAsset request, SocialMediaUploadRequest socialMediaUploadRequest) {
        List<Integer> socialAssetChunkSequenceList = socialAssetChunkInfoService.findSequenceIdsByAssetIdAndSourceId(socialMediaUploadRequest.getAssetId(), SocialChannel.TWITTER.getId());

        if (CollectionUtils.isEmpty(socialAssetChunkSequenceList)) {
            logger.info("No Twitter chunk information found for request ID: {}", request.getRequestId());
            return;
        }

        for (Integer seqId : socialAssetChunkSequenceList) {
            logger.info("Twitter Upload request for chunk number: {} and request ID: {}", seqId, request.getRequestId());

            request.setSequenceId(seqId);
            request.setPageId(socialMediaUploadRequest.getPageId());
            request.setSourceId(socialMediaUploadRequest.getSourceId());

            kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_MEDIA_UPLOAD_CHUNK.getName(), request);
        }
    }

    public void  uploadVideoChunkForLinkedin(MediaUploadRequestForAsset request, boolean isV2request) {
        String channel = SocialChannel.getSocialChannelNameById(request.getSourceId());
        Optional<Object> value = redisExternalService.get(requestIdWithTotalChunks(request.getRequestId()));
        if(!value.isPresent()){
            logger.info("No value present in Redis for key:{}",value);
            updateFailureReasonAndMediaRequest(request.getRequestId(), channel,ErrorCodes.NO_MEDIA_CHUNK_FOUND_WITH_SEQUENCE_ID);
            return;
        }
        logger.info("Key value of asset id: {} and value:{}",request.getAssetId(),value);
        SocialMediaUploadInfo socialMediaUploadInfo =
                socialMediaUploadInfoService.findByUploadRequestIdAndSequenceId(request.getRequestId()
                        ,request.getSequenceId());
        if(Objects.isNull(socialMediaUploadInfo)){
            updateFailureReasonAndMediaRequest(request.getRequestId(), channel,ErrorCodes.NO_MEDIA_UPLOAD_REQUEST_FOUND);
            return;
        }
        SocialMediaUploadService execute = socialRawPageFactory.getSocialRawPageDetails(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        SocialRawPageDetail socialRawPageDetail = execute.getPageDetails(request.getPageId());
        if(Objects.isNull(socialRawPageDetail)){
            logger.info(NO_PAGE_FOUND_WITH_REQUEST,request.getPageId());
            failureReasonUpdate(socialMediaUploadInfo.getPublishInfoId(), ErrorCodes.SOCIAL_RAW_PAGE_NOT_FOUND,channel);
            return;
        }
        SocialAssetChunkInfo socialAssetChunkInfo =
                socialAssetChunkInfoService.findByAssetIdAndSequenceIdAndSourceId(request.getAssetId(),request.getSequenceId(), request.getSourceId());
        if(Objects.isNull(socialAssetChunkInfo)){
            updateFailureReasonAndMediaRequest(request.getRequestId(), channel,ErrorCodes.NO_MEDIA_CHUNK_FOUND_WITH_SEQUENCE_ID);
            return;
        }
        SocialMediaUploadRequest socialMediaUploadRequest = socialMediaUploadRequestService.findById(request.getRequestId());
        if(Objects.isNull(socialMediaUploadRequest)) {
            logger.info("Social media upload request not found for requestId: {}", request.getRequestId());
            updateFailureReasonAndMediaRequest(request.getRequestId(), channel, ErrorCodes.NO_DATA_FOUND);
            return;
        }

        try {
            execute.uploadChunk(socialAssetChunkInfo, socialMediaUploadInfo, socialRawPageDetail, socialMediaUploadRequest, isV2request);
            if(StringUtils.isEmpty(socialMediaUploadInfo.getETag())){
                logger.info("E-tag for request is empty :{}",request);
                updateFailureReasonAndMediaRequest(request.getRequestId(), channel,ErrorCodes.NO_MEDIA_CHUNK_FOUND_WITH_SEQUENCE_ID);
                return;
            }
            logger.info("Upload successfully done for sequence id : {} and request id :{}",
                    socialMediaUploadInfo.getSequenceId(),socialMediaUploadInfo.getUploadRequestId());
            socialMediaUploadInfoService.save(socialMediaUploadInfo);
            checkChunkSizeAndFinalizeVideo(socialMediaUploadRequest,request);
        }catch (BirdeyeSocialException bse) {
            updateMediaRequestInfoAndUpdateRedisKeys(request, socialMediaUploadRequest, execute);
            execute.birdeyeExceptionHandler(bse,socialMediaUploadInfo.getPublishInfoId(),request.getPageId());
        } catch (Exception ex) {
            updateMediaRequestInfoAndUpdateRedisKeys(request, socialMediaUploadRequest, execute);
            execute.generalExceptionHandler(ex.getMessage(),socialMediaUploadInfo.getPublishInfoId());
        }
    }

    public void uploadVideoChunkForTwitter(MediaUploadRequestForAsset request, boolean isV2request) {
        String channel = SocialChannel.getSocialChannelNameById(request.getSourceId());
        Optional<Object> value = redisExternalService.get(requestIdWithTotalChunks(request.getRequestId()));
        if(!value.isPresent()){
            logger.info("[uploadVideoChunkForTwitter] No value present in Redis for key:{}",value);
            updateFailureReasonAndMediaRequest(request.getRequestId(), channel,ErrorCodes.NO_MEDIA_CHUNK_FOUND_WITH_SEQUENCE_ID);
            return;
        }
        logger.info("[uploadVideoChunkForTwitter] Key value of asset id: {} and value:{}",request.getAssetId(),value);
        SocialAssetChunkInfo socialAssetChunkInfo = socialAssetChunkInfoService.findByAssetIdAndSequenceIdAndSourceId(request.getAssetId(),request.getSequenceId(), request.getSourceId());
        if(Objects.isNull(socialAssetChunkInfo)){
            updateFailureReasonAndMediaRequest(request.getRequestId(), channel,ErrorCodes.NO_MEDIA_CHUNK_FOUND_WITH_SEQUENCE_ID);
            return;
        }
        SocialMediaUploadRequest socialMediaUploadRequest = socialMediaUploadRequestService.findById(request.getRequestId());
        if(Objects.isNull(socialMediaUploadRequest)) {
            logger.info("[uploadVideoChunkForTwitter] Social media upload request not found for requestId: {}", request.getRequestId());
            updateFailureReasonAndMediaRequest(request.getRequestId(), channel, ErrorCodes.NO_DATA_FOUND);
            return;
        }
        SocialMediaUploadService execute = socialRawPageFactory.getSocialRawPageDetails(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        SocialRawPageDetail socialRawPageDetail = execute.getPageDetails(request.getPageId());
        if(Objects.isNull(socialRawPageDetail)){
            logger.info(NO_PAGE_FOUND_WITH_REQUEST,request.getPageId());
            failureReasonUpdate(socialMediaUploadRequest.getPublishInfoId(), ErrorCodes.SOCIAL_RAW_PAGE_NOT_FOUND,channel);
            return;
        }

        try {
            execute.uploadChunk(socialAssetChunkInfo, null, socialRawPageDetail, socialMediaUploadRequest, isV2request);
            logger.info("Upload successfully done for sequence id : {} and asset id :{}",
                    socialAssetChunkInfo.getSequenceId(), socialAssetChunkInfo.getAssetId());
            checkChunkSizeAndFinalizeVideo(socialMediaUploadRequest,request);
        }catch (BirdeyeSocialException bse) {
            updateMediaRequestInfoAndUpdateRedisKeys(request, socialMediaUploadRequest, execute);
            execute.birdeyeExceptionHandler(bse,socialMediaUploadRequest.getPublishInfoId(),request.getPageId());
        } catch (Exception ex) {
            updateMediaRequestInfoAndUpdateRedisKeys(request, socialMediaUploadRequest, execute);
            execute.generalExceptionHandler(ex.getMessage(),socialMediaUploadRequest.getPublishInfoId());
        }
    }

    @Override
    public void uploadChunkForMediaRequest(MediaUploadRequestForAsset request, boolean isV2request){
        if(request.getSourceId().equals(SocialChannel.LINKEDIN.getId())) {
            uploadVideoChunkForLinkedin(request, isV2request);
        } else if(request.getSourceId().equals(SocialChannel.TWITTER.getId())) {
            uploadVideoChunkForTwitter(request, isV2request);
        }
    }

    private void updateMediaRequestInfoAndUpdateRedisKeys(MediaUploadRequestForAsset request,
                                                          SocialMediaUploadRequest socialMediaUploadRequest,
                                                          SocialMediaUploadService execute) {
        removeValueForKey(socialMediaUploadRequest.getAssetId(),request.getRequestId(), socialMediaUploadRequest.getSourceId());
        socialMediaUploadRequestService.updateUploadStatusById(MEDIA_UPLOAD_FAILED.name(),request.getRequestId());
        execute.evictPageCache(request.getPageId());
    }

    private void removeValueForKey(Integer assetId, Integer requestId, Integer sourceId) {
        redisExternalService.delete(requestIdWithTotalChunks(requestId));
        redisExternalService.delete(totalMediaChunkKeyForAsset(assetId, sourceId));
        Optional<Object> object = redisExternalService.get(putRequestIdWithAssetIdKey(assetId, sourceId));
        if(!object.isPresent()){
            return;
        }
        List<Integer> requestIds = splitStringAndConvertToArrayList(String.valueOf(object.get()));
        if(CollectionUtils.isEmpty(requestIds)){
            return;
        }
        requestIds.remove(requestId);
        if(CollectionUtils.isEmpty(requestIds)){
            redisExternalService.delete(putRequestIdWithAssetIdKey(assetId, sourceId));
        }else {
            String s = org.apache.commons.lang3.StringUtils.join(requestIds,",");
            redisExternalService.set(putRequestIdWithAssetIdKey(assetId, sourceId), s);
        }
    }

    private void checkChunkSizeAndFinalizeVideo(SocialMediaUploadRequest socialMediaUploadRequest,MediaUploadRequestForAsset request) {
        Integer uploadRequestId = socialMediaUploadRequest.getId();
        Integer chunkLeft = redisExternalService.decrement(requestIdWithTotalChunks(uploadRequestId));
        logger.info("Chunk left to upload : {} for key : {}",chunkLeft, uploadRequestId);
        if (chunkLeft == 0) {
            logger.info("Final chunk uploaded to server");
            MediaUploadRequest mediaUploadRequest = MediaUploadRequest.convertToMediaUploadRequest(socialMediaUploadRequest,request);
            socialMediaUploadRequestService.updateUploadStatusById(MEDIA_CHUNK_UPLOAD_COMPLETE.name(), uploadRequestId);
            kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_FINALIZE_VIDEO.getName(), mediaUploadRequest);
            redisExternalService.delete(requestIdWithTotalChunks(uploadRequestId));
        }
    }

    private void updateFailureReasonAndMediaRequest(Integer requestId, String channel,ErrorCodes codes) {
        logger.info("Update failure reason and media request for request id: {}",requestId);
        SocialMediaUploadRequest socialMediaUploadRequest = socialMediaUploadRequestService.findById(requestId);
        failureReasonUpdate(socialMediaUploadRequest.getPublishInfoId(),codes, channel);
        socialMediaUploadRequestService.updateUploadStatusById(MEDIA_UPLOAD_FAILED.name(),
                socialMediaUploadRequest.getId());
    }

    @Override
    public MediaUploadRequest uploadCaptionAndThumbnail(MediaUploadRequest mediaUploadRequest) {
        SocialMediaUploadRequest socialMediaUploadRequest =
                socialMediaUploadRequestService.findById(mediaUploadRequest.getRequestId());
        if(Objects.isNull(socialMediaUploadRequest) ||
                !MEDIA_UPLOAD_IN_PROGRESS.name().equalsIgnoreCase(socialMediaUploadRequest.getUploadStatus())) {
            return mediaUploadRequest;
        }
        String channel = SocialChannel.getSocialChannelNameById(socialMediaUploadRequest.getSourceId());
        SocialMediaUploadService execute = socialRawPageFactory.getSocialRawPageDetails(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        SocialRawPageDetail socialRawPageDetail = execute.getPageDetails(socialMediaUploadRequest.getPageId());
        if(Objects.isNull(socialRawPageDetail)){
            logger.info(NO_PAGE_FOUND_WITH_REQUEST,mediaUploadRequest);
            return mediaUploadRequest;
        }
//        execute.uploadCaption(socialRawPageDetail,socialMediaUploadRequest);
        return mediaUploadRequest;
    }

    @Override
    public MediaUploadRequest finalizeUpload(MediaUploadRequest mediaUploadRequest) {
        String channel = SocialChannel.getSocialChannelNameById(mediaUploadRequest.getSourceId());
        SocialMediaUploadRequest socialMediaUploadRequest =
                socialMediaUploadRequestService.findById(mediaUploadRequest.getRequestId());
        if(Objects.isNull(socialMediaUploadRequest)) {
            failureReasonUpdate(mediaUploadRequest.getPublishInfoId(), NO_MEDIA_UPLOAD_REQUEST_FOUND, channel);
            return mediaUploadRequest;
        }
        if(!MEDIA_CHUNK_UPLOAD_COMPLETE.name().equalsIgnoreCase(socialMediaUploadRequest.getUploadStatus())) {
            failureReasonUpdate(mediaUploadRequest.getPublishInfoId(),MEDIA_UPLOAD_ALREADY_COMPLETED ,channel);
            return mediaUploadRequest;
        }
        SocialMediaUploadService execute = socialRawPageFactory.getSocialRawPageDetails(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        SocialRawPageDetail socialRawPageDetail = execute.getPageDetails(socialMediaUploadRequest.getPageId());
        if(Objects.isNull(socialRawPageDetail)){
            logger.info(NO_PAGE_FOUND_WITH_REQUEST,mediaUploadRequest);
            failureReasonUpdate(socialMediaUploadRequest.getPublishInfoId(), ErrorCodes.SOCIAL_RAW_PAGE_NOT_FOUND,channel);
            return mediaUploadRequest;
        }
        try {
            List<String> eTags = socialMediaUploadInfoService.findByRequestId(socialMediaUploadRequest.getId());
            logger.info("Number of eTags : {} for request id :{}", eTags.size(), socialMediaUploadRequest.getId());
            execute.finalizeVideoUpload(socialRawPageDetail, socialMediaUploadRequest, eTags);
            logger.info("Video finalized successfully for request id :{}", socialMediaUploadRequest.getId());
            socialMediaUploadRequest.setUploadStatus(MEDIA_UPLOAD_FINALIZE.name());
            socialMediaUploadRequestService.save(socialMediaUploadRequest);
            mediaUploadRequest.setPageId(socialMediaUploadRequest.getPageId());
            mediaUploadRequest.setMediaId(socialMediaUploadRequest.getVideoId());
            mediaUploadRequest.setStatus(false);
            return mediaUploadRequest;
        } catch (BirdeyeSocialException bse) {
            redisUpdateAndCacheEvict(socialMediaUploadRequest, execute);
            execute.birdeyeExceptionHandler(bse, mediaUploadRequest.getPublishInfoId(), mediaUploadRequest.getPageId());
        } catch (Exception ex) {
            redisUpdateAndCacheEvict(socialMediaUploadRequest, execute);
            execute.generalExceptionHandler(ex.getMessage(), mediaUploadRequest.getPublishInfoId());
        }
        return mediaUploadRequest;
    }

    @Override
    public MediaUploadRequest checkUploadStatus(MediaUploadRequest mediaUploadRequest) throws Exception {
        String channel = SocialChannel.getSocialChannelNameById(mediaUploadRequest.getSourceId());
        SocialMediaUploadService execute = socialRawPageFactory.getSocialRawPageDetails(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        SocialRawPageDetail socialRawPageDetail = execute.getPageDetails(mediaUploadRequest.getPageId());
        if(Objects.isNull(socialRawPageDetail)){
            logger.info(NO_PAGE_FOUND_WITH_REQUEST,mediaUploadRequest);
            failureReasonUpdate(mediaUploadRequest.getPublishInfoId(), ErrorCodes.SOCIAL_RAW_PAGE_NOT_FOUND,channel);
            return mediaUploadRequest;
        }
        logger.info("Check status for the request : {}",mediaUploadRequest);
        execute.checkStatus(socialRawPageDetail, mediaUploadRequest);
        mediaUploadRequest.setChannel(channel);
        if(!mediaUploadRequest.isStatus()){
            throw new BirdeyeSocialException(ErrorCodes.MEDIA_IN_PROGRESS,"Media is still in progress");
        }
        kafkaProducerService.sendObjectWithKeyV1(String.valueOf(mediaUploadRequest.getAssetId()),
                KafkaTopicEnum.SOCIAL_MEDIA_UPLOAD_CONTENT.getName(),mediaUploadRequest);
        return mediaUploadRequest;
    }

    @Override
    public void postContentWithMedia(MediaUploadRequest request) {
        if (handleFailureOrProcessState(request)) return;
        String channel = SocialChannel.getSocialChannelNameById(request.getSourceId());
        SocialMediaUploadRequest mediaUploadRequest = socialMediaUploadRequestService.findById(request.getRequestId());
        if(Objects.isNull(mediaUploadRequest)){
            failureReasonUpdate(request.getPublishInfoId(), NO_MEDIA_UPLOAD_REQUEST_FOUND, channel);
            return;
        }
        if(!MEDIA_UPLOAD_FINALIZE.name().equalsIgnoreCase(mediaUploadRequest.getUploadStatus())){
            failureReasonUpdate(request.getPublishInfoId(), MEDIA_UPLOAD_ALREADY_COMPLETED, channel);
            return;
        }
        SocialMediaUploadService execute = socialRawPageFactory.getSocialRawPageDetails(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        try {
            logger.info("Upload post content for request: {}",request.getRequestId());
            SocialPostPublishInfo publishInfo = updatePostText(mediaUploadRequest.getPublishInfoId());
            if(Objects.isNull(publishInfo)) {
                logger.info("Exiting process. No publish info found for id {} ", mediaUploadRequest.getPublishInfoId());
                return;
            }
            execute.postContentWithMedia(request, mediaUploadRequest.getPageId(), publishInfo);
            logger.info("Upload post content completed for request: {}",request.getRequestId());
            mediaUploadRequest.setUploadStatus(MEDIA_UPLOAD_COMPLETE.name());
            socialMediaUploadRequestService.save(mediaUploadRequest);
            execute.evictPageCache(mediaUploadRequest.getPageId());
            removeValueForKey(mediaUploadRequest.getAssetId(),mediaUploadRequest.getId(), mediaUploadRequest.getSourceId());
        }catch (BirdeyeSocialException bse) {
            redisUpdateAndCacheEvict(mediaUploadRequest,execute);
            execute.birdeyeExceptionHandler(bse,mediaUploadRequest.getPublishInfoId(),mediaUploadRequest.getPageId());
        } catch (Exception ex) {
            redisUpdateAndCacheEvict(mediaUploadRequest,execute);
            execute.generalExceptionHandler(ex.getMessage(),mediaUploadRequest.getPublishInfoId());
        }
    }

    private SocialPostPublishInfo updatePostText(Integer publishInfoId) {
        SocialPostPublishInfo publishInfo = socialPostPublishInfoService.findById(publishInfoId);
        if(Objects.isNull(publishInfo)){
            return null;
        }
        SocialPost socialPost = publishInfo.getSocialPost();
        List<MentionData> mentionDataList = new ArrayList<>();
        String postText = Objects.nonNull(publishInfo.getSocialPost().getPostText())?publishInfo.getSocialPost().getPostText():"";
        if(StringUtils.isNotEmpty(publishInfo.getSocialPost().getMentions())) {
            mentionDataList = JSONUtils.collectionFromJSON(publishInfo.getSocialPost().getMentions(), MentionData.class);
        }
        postText = socialPostPublishInfoService.getProfileData(publishInfo.getSocialPost().getPostText(),publishInfo.getBusinessId(),mentionDataList,publishInfo.getSourceId());
        socialPost.setPostText(postText);
        logger.info("Video posting for channel: {} with text: {} ", publishInfo.getSourceId(), socialPost.getPostText());
        publishInfo.setSocialPost(socialPost);
        return publishInfo;
    }

    private void redisUpdateAndCacheEvict(SocialMediaUploadRequest mediaUploadRequest, SocialMediaUploadService execute) {
        removeValueForKey(mediaUploadRequest.getAssetId(),mediaUploadRequest.getId(), mediaUploadRequest.getSourceId());
        execute.evictPageCache(mediaUploadRequest.getPageId());
        socialMediaUploadRequestService.updateUploadStatusById(MEDIA_UPLOAD_FAILED.name(), mediaUploadRequest.getId());
    }

    @Override
    public void exceptionHandler(MediaUploadRequest request) {
        handleFailureOrProcessState(request);
    }

    private boolean handleFailureOrProcessState(MediaUploadRequest request) {
        String channel = SocialChannel.getSocialChannelNameById(request.getSourceId());
        if(!request.isStatus()){
            logger.info("Video not available with request : {}", request);
            failureReasonUpdate(request.getPublishInfoId(),ErrorCodes.VIDEO_UNAVAILABLE, channel);
            return true;
        }
        return false;
    }

    @NotNull
    private static String getCallbackId(MediaUploadRequest mediaInitiateRequest) {
        return PUBLISH_MODULE+"_"+mediaInitiateRequest.getAssetId()+"_"+mediaInitiateRequest.getSourceId();
    }

    @Override
    public GoogleCredInfo getGoogleCredentials() {
        GoogleCredInfo googleCredInfo = new GoogleCredInfo();
        googleCredInfo.setType(CacheManager.getInstance().getCache(SocialDomainAppCredsCache.class).getProperty("type",""));
        googleCredInfo.setProject_id(CacheManager.getInstance().getCache(SocialDomainAppCredsCache.class).getProperty("project_id",""));
        googleCredInfo.setPrivate_key_id(CacheManager.getInstance().getCache(SocialDomainAppCredsCache.class).getProperty("private_key_id",""));
        googleCredInfo.setPrivate_key(CacheManager.getInstance().getCache(SocialDomainAppCredsCache.class).getProperty("private_key",""));
        googleCredInfo.setClient_email(CacheManager.getInstance().getCache(SocialDomainAppCredsCache.class).getProperty("client_email",""));
        googleCredInfo.setClient_id(CacheManager.getInstance().getCache(SocialDomainAppCredsCache.class).getProperty("client_id",""));
        googleCredInfo.setAuth_uri(CacheManager.getInstance().getCache(SocialDomainAppCredsCache.class).getProperty("auth_uri",""));
        googleCredInfo.setToken_uri(CacheManager.getInstance().getCache(SocialDomainAppCredsCache.class).getProperty("token_uri",""));
        googleCredInfo.setAuth_provider_x509_cert_url(CacheManager.getInstance().getCache(SocialDomainAppCredsCache.class).getProperty("auth_provider_x509_cert_url",""));
        googleCredInfo.setClient_x509_cert_url(CacheManager.getInstance().getCache(SocialDomainAppCredsCache.class).getProperty("client_x509_cert_url",""));
        googleCredInfo.setUniverse_domain(CacheManager.getInstance().getCache(SocialDomainAppCredsCache.class).getProperty("universe_domain",""));

        return googleCredInfo;
    }
}
