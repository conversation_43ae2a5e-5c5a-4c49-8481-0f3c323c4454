package com.birdeye.social.service.SocialTrendsReportService;

import com.birdeye.social.constant.TrendsLocationReportSortOption;
import com.birdeye.social.trends.*;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.sort.SortOrder;

import java.io.IOException;
import java.util.List;

public interface SocialTrendsEsService {
    SearchResponse getTrendsResponseSummaryEsResponse(List<String> pageIds, TrendsReportRequest request);

    TrendsReportSummaryResponse convertTrendsEsSummaryResponse(SearchResponse searchResponse, List<String> messages);

    SocialTrendsReportResponse convertTrendsEsByUserResponse(SearchResponse searchResponse, TrendsReportRequest request);

    SearchResponse getResponseTimeByUserEsResponse(List<String> pageIds,
                                                   TrendsReportRequest request,SortOrder sortingOrder) throws IOException;

    SearchResponse getResponseTimeByChannelEsResponse(List<String> pageIds, TrendsReportRequest request);

    SocialTrendsReportResponse convertTrendsEsByChannelResponse(SearchResponse searchResponse,TrendsReportRequest request);

    SearchResponse getResponseRateByChannelEsResponse(List<String> pageIds, TrendsReportRequest request);

    SocialTrendsReportResponse convertResponseRateByChannelEsResponse(SearchResponse searchResponse, TrendsReportRequest request);

    SearchResponse getIncomingMessagesListFromEs(List<String> pageIds,TrendsReportRequest request);

    TrendsOverviewResponseDTO getResponseOverviewResponse(List<String> pageIds, TrendsReportRequest request, SearchResponse incomingMessageResponse);

    SearchResponse getLeaderboardSummaryEsResponse(List<String> pageIds, TrendsReportRequest request, TrendsLocationReportSortOption sortingCriteria, SortOrder order);

    TrendsLocationLeaderboardResponse convertLeaderboardEsSummaryResponse(SearchResponse searchResponse);

}
