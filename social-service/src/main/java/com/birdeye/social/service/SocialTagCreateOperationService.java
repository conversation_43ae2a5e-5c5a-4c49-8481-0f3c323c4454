package com.birdeye.social.service;

import com.birdeye.social.AbstractSocialTagOperationService;
import com.birdeye.social.constant.SocialTagOperation;
import com.birdeye.social.entities.SocialTag;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.sro.AbstractSocialTagOperation;
import com.birdeye.social.sro.SocialTagOperationResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR> on 22/12/23
 */
@Service
@Slf4j
public class SocialTagCreateOperationService extends AbstractSocialTagOperationService {

    @Autowired
    private SocialTagDBService socialTagDBService;

    @Override
    public SocialTagOperationResponse performOperation(Integer accountId, Long accountNum, Long userId,
                                                       Set<AbstractSocialTagOperation.SocialTagOperationDetail> tagOperationDetails, boolean throwError) {
        Map<String, Long> existingTagToIdMap = socialTagDBService.getAllLowerCaseSocialTagNameToTagIdMap(accountId);

        // If there already exists a tag from the same name then do NOT create a new tag
        // Ignoring the case by lower casing the existing and the new tag while comparing
        SocialTagOperationResponse businessTagOperationResponse = new SocialTagOperationResponse(SocialTagOperation.CREATE);
        Set<AbstractSocialTagOperation.SocialTagOperationDetail> tags = new HashSet<>();
        List<SocialTag> newBusinessTags = new LinkedList<>();
        for (AbstractSocialTagOperation.SocialTagOperationDetail tagOperationDetail : tagOperationDetails) {
            String tagName = StringUtils.strip(tagOperationDetail.getName());
            Long existingTagId = existingTagToIdMap.get(StringUtils.lowerCase(tagName));
            if (Objects.nonNull(existingTagId)) {
                log.info("[SocialTagCreateOperationService] tag :{} for account:{} already exists hence skipping", tagName, accountId);
                if (throwError) {
                    throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Tag already exists with the same name!");
                }
                // This is done so that caller gets to know the ID of this tag passed
                tags.add(new AbstractSocialTagOperation.SocialTagOperationDetail(existingTagId, tagName));
            } else {
                SocialTag newTag = new SocialTag();

                newTag.setName(tagName);
                newTag.setAccountId(accountId);
                newTag.setAccountNumber(accountNum);
                newTag.setCreatedBy(userId);
                newTag.setUpdatedBy(userId);

                newBusinessTags.add(newTag);
            }
        }

        boolean isOperationExecuted = false;
        if (CollectionUtils.isNotEmpty(newBusinessTags)) {
            isOperationExecuted = true;
            newBusinessTags = socialTagDBService.saveAllAndFlushTags(newBusinessTags);
            newBusinessTags.forEach(tag -> tags.add(new AbstractSocialTagOperation.SocialTagOperationDetail(tag.getId(), tag.getName())));
        }

        businessTagOperationResponse.setTags(tags);
        businessTagOperationResponse.setIsOperationExecuted(isOperationExecuted);

        return businessTagOperationResponse;
    }
}
