/**
 * 
 */
package com.birdeye.social.service.impl;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.model.CheckIntegrationRequest.CheckIntegrationInputDTO;
import com.birdeye.social.model.FBCheckIntegrationResponse;
import com.birdeye.social.model.GMBCheckIntegrationResponse;
import com.birdeye.social.service.IAggregationService;
import com.birdeye.social.service.IBusinessAggregationCheckService;
import com.birdeye.social.sro.BusinessAggregationInfo;

/**
 * <AUTHOR>
 *
 */
@Service
public class BusinessAggregationCheckService implements IBusinessAggregationCheckService {
	
	private static final Logger LOG =  LoggerFactory.getLogger(BusinessAggregationCheckService.class);
	
	@Autowired
	IAggregationService aggregationService;
	
	/*
	 * (non-Javadoc)
	 * @see com.birdeye.social.external.service.IBusinessAggregationCheckService#checkBusinessAggregation(java.lang.String, java.lang.String)
	 */
	@Override
	public BusinessAggregationInfo checkBusinessAggregation(String channel, String integrationId, Integer businessId, Integer businessAggregationId) {
		BusinessAggregationInfo response = null;
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			// call to check fb integration
			response = aggregationService.checkFacebookAggregation(businessId, integrationId);
			
		} else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			// call to check gmb integration
			response = aggregationService.checkGMBAggregation(businessId, integrationId, businessAggregationId);
		}
		return response;
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.IBusinessAggregationCheckService#checkIntegration(java.util.Set)
	 */
	@Override
	public Map<Integer, String> checkIntegration(
			Set<CheckIntegrationInputDTO> input) {
		if(CollectionUtils.isEmpty(input)) {
			LOG.warn("Empty input for integration check");
			return Collections.emptyMap();
		}
		Collection<CheckIntegrationInputDTO> fbRequests =  new HashSet<>();
		Collection<CheckIntegrationInputDTO> gmbRequests =  new HashSet<>();
		Map<Integer, String> result =  new ConcurrentHashMap<>();
		input.forEach( dto -> {
			result.put(dto.getBusinessAggregationId(), "false");
			SocialChannel channel = SocialChannel.getSocialChannelByName(dto.getChannel());
			if(SocialChannel.FACEBOOK == channel) {
				fbRequests.add(dto);
			}else if(SocialChannel.GMB == channel) {
				gmbRequests.add(dto);
			}else {
				LOG.warn("Invalid Social channel for request input :{}",dto);
			}
			
		});
		//Can we do in parallel ? is parallelism really required?
		//TODO MKM Think and implement on Above comment
		processFBCheck(fbRequests, result);
		processGMBCheck(gmbRequests, result);
		LOG.info("Returning mapping :{} ",result);
		return result;
	}

	/**
	 * @param gmbRequests
	 * @param result
	 */
	private void processGMBCheck(
			Collection<CheckIntegrationInputDTO> gmbRequests,
			Map<Integer, String> result) {
		if(!CollectionUtils.isEmpty(gmbRequests)) {
			List<GMBCheckIntegrationResponse> gmbCheckIntegrationResponse = aggregationService.checkGMBIntegration(gmbRequests,false);
			if(!gmbCheckIntegrationResponse.isEmpty()) {
				gmbCheckIntegrationResponse.forEach(dto -> 
				result.put(dto.getBusinessAggregationId(), dto.getIntegrationDetail())
			);
			}
		}
	}

	/**
	 * @param fbRequests
	 * @param result
	 */
	private void processFBCheck(
			Collection<CheckIntegrationInputDTO> fbRequests,
			Map<Integer, String> result) {
		if(!CollectionUtils.isEmpty(fbRequests)) {
			List<FBCheckIntegrationResponse> fbCheckIntegrationResponse = aggregationService.checkFBIntegration(fbRequests,false);
			if(!fbCheckIntegrationResponse.isEmpty()) {
				fbCheckIntegrationResponse.forEach(dto -> 
					result.put(dto.getBusinessAggregationId(), dto.getIntegrationDetail())
				);
			}
		}
	}

}
