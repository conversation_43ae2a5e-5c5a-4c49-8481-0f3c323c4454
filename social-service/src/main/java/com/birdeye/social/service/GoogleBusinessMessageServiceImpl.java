package com.birdeye.social.service;

import com.birdeye.platform.dto.GoogleMessageOutput;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.service.IGoogleService;
import com.birdeye.social.google.GoogleBusinessCommAccessTokenService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.map.PassiveExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Service
public class GoogleBusinessMessageServiceImpl implements IGoogleBusinessMessageService{

    @Autowired
    private IGoogleService googleService;

    @Autowired
    private GoogleBusinessCommAccessTokenService googleBusinessCommAccessTokenService;

    private static Logger logger = LoggerFactory.getLogger(SocialAccountServiceImpl.class);
    private static PassiveExpiringMap<String, String> map = new PassiveExpiringMap<>(30, TimeUnit.MINUTES);

    @Override
    @Deprecated
    public void sendToGoogle(GoogleMessageOutput googleMessageOutput) throws Exception {
        logger.info("[GMSG] request received to send reply for conversation id and name {} : {}",googleMessageOutput.getName(),googleMessageOutput.getMessageId());
        String accessToken = getAccessToken();
        if(StringUtils.isBlank(accessToken)){
            logger.error("[GMSG] Failed to fetch access token to send message {}",new ObjectMapper().writeValueAsString(googleMessageOutput));
            throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_TOKEN_FETCH_ERROR,"Failed to fetch access token for gMsg");
        }
        googleService.sendMessage(googleMessageOutput, accessToken);
    }

    private String getAccessToken() throws IOException {
        if (map.containsKey("accessToken")) {
            return map.get("accessToken");
        }
        try {
            return googleBusinessCommAccessTokenService.getAppAccessToken();
        }
        catch(Exception e) {
            logger.info("[GMSG] exception occurred while generating access token {}", e);
            throw e;
        }
    }
}
