package com.birdeye.social.service.ratelimit.impl;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.service.ratelimit.ChannelRateLimitService;
import org.springframework.stereotype.Service;

@Service
public class GmbRateLimitServiceImpl implements ChannelRateLimitService {

    public static final String SOCIAL_LOCAL_POST = "mybusiness.googleapis.com/v4/accounts/%s/locations/%s/localPosts";
    public static final String GMB_FOOD_MENUS = "mybusiness.googleapis.com/v4/accounts/%s/locations/%s/foodMenus";
    public static final String  GMB_NOTIFICATION="mybusinessnotifications.googleapis.com/v1/notificationSetting";
    public static final String  V4_NOTIFICATION="mybusiness.googleapis.com/v4/accounts/notification";
    public static final String  CATEGORIES="mybusinessbusinessinformation.googleapis.com/v1/categories";
    public static final String  ACCOUNTS="mybusinessaccountmanagement.googleapis.com/v1/accounts";
    public static final String  BUSINESS_MESSAGES="businessmessages.googleapis.com/v1/conversations/";
    public static final String  REVIEWS="mybusiness.googleapis.com/v4/accounts/reviews";
    public static final String  REVIEWS_REPLY="mybusiness.googleapis.com/v4/accounts/reviews/reply";
    public static final String  REPORT_INSIGHT_PAGE_ANALYTICS="businessprofileperformance.googleapis.com/v1/locations/getDailyMetricsTimeSeries";
    public static final String  REPORT_INSIGHT_KEYWORD_ANALYTICS="businessprofileperformance.googleapis.com/v1/locations/searchkeywords/impressions/monthly";
    public static final String  REPORT_POSTS_INSIGHTS="mybusiness.googleapis.com/v4/localPosts:reportInsights";
    public static final String  REPORT_PAGE_INSIGHTS="mybusiness.googleapis.com/v4/accounts/locations:reportInsights";
    public static final String  VOICEOFMERCHANT= "mybusinessbusinessinformation.googleapis.com/v1/locations/VoiceOfMerchantState";
    public static final String  LOCATION_DETAILS_READ= "mybusinessbusinessinformation.googleapis.com/v1/locations/read_mask";
    public static final String  MEDIA_URL= "mybusiness.googleapis.com/v4/media";
    public static final String  EDIT_POST= "mybusiness.googleapis.com/v4/accounts/localPosts?updateMask=summary,media";
    public static final String  LOCATION_URL="mybusinessbusinessinformation.googleapis.com/v1/locations";
    public static final String  LOCATION_UPDATE="mybusinessbusinessinformation.googleapis.com/v1/locations/update";
    public static final String  GMB_PLACE_ACTION="mybusinessplaceactions.googleapis.com/v1/";
    public static final String  UPDATE_ATTRIBUTES="mybusinessbusinessinformation.googleapis.com/v1/locations/UpdateAttributes";
    public static final String  GET_GOOGLE_UPDATED_ATTRIBUTES="mybusinessbusinessinformation.googleapis.com/v1/attributes:getGoogleUpdated";
    public static final String  LIST_ATTRIBUTE_METADATA="mybusinessbusinessinformation.googleapis.com/v1/attributes";
    public static final String  GOOGLE_SEARCH_LOCATIONS="mybusinessbusinessinformation.googleapis.com/v1/googleLocations:search";

    @Override
    public SocialChannel channelName() {
        return SocialChannel.GMB;
    }

    @Override
    public String getCacheUrl(String apiName, String method) {
        String api = null;
        if (apiName != null) {
            if (apiName.contains("/reply")) {
                // https://mybusiness.googleapis.com/v4/accounts/105247142833187364092/locations/10874319773092602823/reviews/AIe9_BGErQpX2pRNZrXBy5NOsH0el6bEmHFAZkTAbLrZdznXJFVDycYYHO8gkbVbbSehAVQXColsE2p2wR3JOTEV2pLOE1SdHHV7kchHg9Urhj7m01kDlew/reply
                api = REVIEWS_REPLY;//done
            } else if (apiName.contains("/reviews")) {
                // https://mybusiness.googleapis.com/v4/accounts/100512838622732727086/locations/11535992802279343333/reviews?pageSize=200
                api = REVIEWS;//done
            } else if (apiName.contains("/foodMenus")) {
                api = GMB_FOOD_MENUS;
            } else if (apiName.contains("/notificationSetting")) {
                api = GMB_NOTIFICATION;//done
            } else if (apiName.contains("?updateMask=summary,media")) {
                //https://mybusiness.googleapis.com/v4/accounts/111665400866701181021/locations/5781504852190752948/media
                api = EDIT_POST;//done
            } else if (apiName.contains("media")) {
                //https://mybusiness.googleapis.com/v4/accounts/111665400866701181021/locations/5781504852190752948/media
                api = MEDIA_URL;//done
            } else if (apiName.contains("/conversations")) {
                //https://businessmessages.googleapis.com/v1/conversations/7206e260-f817-4da5-9718-a5506dd3f996/messages
                api = BUSINESS_MESSAGES;//done
            } else if (apiName.contains("getDailyMetricsTimeSeries")) {
                //https://businessprofileperformance.googleapis.com/v1/locations/15625292819159037420:getDailyMetricsTimeSeries
                api = REPORT_INSIGHT_PAGE_ANALYTICS;//done
            } else if (apiName.contains("/searchkeywords")) {
                api = REPORT_INSIGHT_KEYWORD_ANALYTICS;//done
            } else if (apiName.contains("/localPosts") && !apiName.contains(":reportInsights")) {
                //https://mybusiness.googleapis.com/v4/accounts/117009032617899517275/locations/10818926604158456714/localPosts
                api = SOCIAL_LOCAL_POST;//done
            } else if (apiName.contains("localPosts:reportInsights")) {
                api = REPORT_POSTS_INSIGHTS;//done
            } else if (apiName.contains("locations:reportInsights")) {
                api = REPORT_PAGE_INSIGHTS;//done
            } else if ((apiName.contains("read_mask") || apiName.contains("readMask")) && apiName.contains("/locations")) {
                //https://mybusinessbusinessinformation.googleapis.com/v1/locations/8913468471185321530?read_mask=name,phoneNumbers,title,storeCode,categories,storefrontAddress,websiteUri,regularHours,specialHours,serviceArea,labels,adWordsLocationExtensions,latlng,openInfo,metadata,profile,relationshipData,moreHours,serviceItems,languageCode
                api = LOCATION_DETAILS_READ;//done
            } else if (apiName.contains("/VoiceOfMerchantState")) {
                //https://mybusinessbusinessinformation.googleapis.com/v1/locations/11412471759800789465/VoiceOfMerchantState
                api = VOICEOFMERCHANT;//done
            } else if (apiName.contains("/notifications")) {
                //https://mybusiness.googleapis.com/v4/accounts/118352327669871471786/notifications
                api = V4_NOTIFICATION;//done
            } else if (apiName.contains("attributes:getGoogleUpdated")) {
                //https://mybusinessbusinessinformation.googleapis.com/v1/locations/15097011423958701550/attributes:getGoogleUpdated
                api = GET_GOOGLE_UPDATED_ATTRIBUTES;//done
            } else if (apiName.contains("/attributes") && apiName.contains("/locations")) {
                api = UPDATE_ATTRIBUTES;//done
            } else if (apiName.contains(LIST_ATTRIBUTE_METADATA)) {
                // https://mybusinessbusinessinformation.googleapis.com/v1/attributes?categoryName=categories/gcid:bakery&languageCode=en&regionCode=US
                api = LIST_ATTRIBUTE_METADATA;
            } else if (apiName.contains(CATEGORIES)) {
                // https://mybusinessbusinessinformation.googleapis.com/v1/categories
                api = CATEGORIES;//done
            } else if (apiName.contains(ACCOUNTS)) {
                api = ACCOUNTS;//done
            } else if (apiName.contains(LOCATION_URL) && apiName.contains("?updateMask=")) {
                api = LOCATION_UPDATE;
            } else if (apiName.contains(GMB_PLACE_ACTION)) {
                api = GMB_PLACE_ACTION;
            } else if(apiName.contains(GOOGLE_SEARCH_LOCATIONS)) {
                api = GOOGLE_SEARCH_LOCATIONS;
            }
        }
        return api;

    }
}
