package com.birdeye.social.service.locationmovement;

import com.birdeye.social.locationmovement.LocationMovementRequest;
import com.birdeye.social.service.SocialPostCalendarService;
import com.birdeye.social.service.SocialPostPublishInfoService;
import com.birdeye.social.service.SocialPostScheduleInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Transactional
@Service
@Slf4j
public class LocationMovementServiceImpl implements LocationMovementService{

    @Autowired
    private SocialPostPublishInfoService socialPostPublishInfoService;

    @Autowired
    private SocialPostScheduleInfoService socialPostScheduleInfoService;

    @Autowired
    private SocialPostCalendarService socialPostCalendarService;

    @Override
    public void smbToEnterpriseLocation(Boolean excludeApprovalPosts,LocationMovementRequest request) {
        List<Integer> postIds;
        if (!excludeApprovalPosts){
            log.info("Approvals not considered while updating the post with request :{}",request);
            socialPostScheduleInfoService.updateEnterpriseIdWhereEnterpriseId(request.getTargetEnterpriseId(),request.getSourceBusinessId());
            socialPostPublishInfoService.updateEnterpriseIdWhereEnterpriseId(request.getTargetEnterpriseId(),
                    request.getSourceBusinessId(),request.getTargetBusinessId());
            postIds = socialPostScheduleInfoService.findDistinctByEnterpriseId(request.getTargetEnterpriseId());
        }else{
            log.info("Posts with approvals will not be considered while updating the post with request :{}",request);
            socialPostScheduleInfoService.updateEnterpriseIdWhereEnterpriseIdAndApprovalIdIsNull(request.getTargetEnterpriseId(),request.getSourceBusinessId());
            socialPostPublishInfoService.updateEnterpriseIdWhereEnterpriseIdAndApprovalIdIsNull(request.getTargetEnterpriseId(),
                    request.getSourceBusinessId(),request.getTargetBusinessId());
            postIds = socialPostScheduleInfoService.findDistinctByEnterpriseIdAndApprovalWorkflowIsNull(request.getTargetEnterpriseId());
        }
        log.info("List of post ids to be updated in ES:{}",postIds);
        if(CollectionUtils.isEmpty(postIds)) return;
        socialPostCalendarService.syncRecordsOnEsBySocialPostIds(postIds);
    }
}
