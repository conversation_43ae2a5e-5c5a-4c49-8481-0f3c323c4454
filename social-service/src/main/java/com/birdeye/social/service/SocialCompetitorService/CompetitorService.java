package com.birdeye.social.service.SocialCompetitorService;

import com.birdeye.social.constant.CompetitorPostsSortField;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dto.PicturesqueMediaCallback;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.model.*;
import com.birdeye.social.model.competitorProfile.*;
import com.birdeye.social.model.linkinbio.BusinessPropertyEventRequest;
import org.elasticsearch.search.sort.SortOrder;

import java.io.IOException;
import java.util.List;

public interface CompetitorService {

    void fetchCompetitorPosts(CompetitorRequestDTO requestDTO);

    void fetchCompetitorAccounts(SocialChannel channel, List<String> accountIdentifier, Long enterpriseId);

    void fetchAndSaveCompetitorAccounts(AddCompetitorRequest addCompetitorRequest, Long businessNumber, Integer enterpriseId, Integer userId);

    void updateCache(SocialChannel channel, Long businessNumber);

    CompetitorListResponse getCompetitorList(String channel, Long businessNumber);

    AllCompetitorListResponse getAllCompetitorList(Long businessNumber);

    void deleteCompetitor(DeleteCompetitorDetailRequest deleteRequest, Long businessNumber, Integer enterpriseId, Integer userId) throws SocialBirdeyeException;
    void syncCompetitorPostsWithEs(SyncCompetitorPostEsRequest request) throws IOException;

    void scanPages(SocialChannel channel);

    CompetitorPostResponse getAllPosts(CompetitorGetPostRequest request, Integer startIndex, Integer pageSize, CompetitorPostsSortField sortBy, SortOrder sortOrder, Long businessNumber);

    void updatePostUrl(PicturesqueMediaCallback callback);

    void updatePageUrl(PicturesqueMediaCallback callback);

    void callPicturesQue(PicturesqueCompRequest request);

    CompetitorPageDetailResponse getPageSummary(String channel, String pageId, Long businessNumber);

    CompetitorPageCountResponse getPageCount(Long businessNumber);

    void checkSocialEnable(BusinessPropertyEventRequest request);

    void fetchCompetitorProfile(CompetitorRequestDTO requestDTO);

    void syncCompetitorProfileInsightsWithEs(CompetitorProfileInsightsES request);


    CompetitorProfileDetailsResponseDTO getCompetitorProfileFromEs(CompetitorProfileDetailsRequestDTO requestDTO);

    CompetitorPublishingResponseDTO getCompetitorPublishingBehaviourFromEs(CompetitorProfileDetailsRequestDTO requestDTO);

    CompetitorEngagementResponseDTO getCompetitorEngagementFromEs(CompetitorProfileDetailsRequestDTO requestDTO);

    CompetitorSummaryResponse getCompetitorSummaryFromEs(CompetitorProfileDetailsRequestDTO requestDTO);

    CompetitorPostResponse getCompetitorAndSelfProfileTopPosts(CompetitorAllProfilesDetailsRequestDTO request, Integer pageSize);

    CompetitorExcelResponse getCompetitorExcelFromEs(CompetitorAllProfilesDetailsRequestDTO requestDTO, Integer pageSize, Integer startIndex);

    CompetitorPageDetailsResponse getPageDetails(CompetitorProfileDetailsRequest request);

    void backFillSelfPageInsightsWithEs(Integer size, Integer from) throws IOException;
}
