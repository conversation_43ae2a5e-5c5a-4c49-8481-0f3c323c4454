package com.birdeye.social.service.tiktok;

import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessTiktokAccountsRepository;
import com.birdeye.social.dao.EngageFeedDetailsRepo;
import com.birdeye.social.entities.BusinessTiktokAccounts;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.FbNotification.FreshPostNotificationRequest;
import com.birdeye.social.model.engageV2.message.EventUpdateRequest;
import com.birdeye.social.model.tiktok.TikTokCommentEventRequest;
import com.birdeye.social.model.tiktok.TikTokVideoEventRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@RequiredArgsConstructor
@Service
public class TiktokNotificationServiceImpl implements TiktokNotificationService {

    private final BusinessTiktokAccountsRepository businessTiktokAccountsRepository;
    private final EngageFeedDetailsRepo engageFeedDetailsRepo;
    private final KafkaProducerService kafkaProducerService;

    @Override
    public void processTiktokVideoEngageEvent(TikTokVideoEventRequest tiktokVideoEventRequest) {
        log.info("Tiktok video engage notification received {}", tiktokVideoEventRequest.toString());
        String profileId = tiktokVideoEventRequest.getUserOpenid();
        BusinessTiktokAccounts tiktokAccounts = businessTiktokAccountsRepository.findFirstByProfileIdAndIsValid(profileId, 1);
        if (Objects.isNull(tiktokAccounts)) {
            log.error("No tiktok account found for engage request with user openId: {} and postId: {}",
                    tiktokVideoEventRequest.getUserOpenid(), tiktokVideoEventRequest.getContent().getPostId());
            return;
        }
        String postId = tiktokVideoEventRequest.getContent().getPostId();
        boolean postExists = Objects.nonNull(engageFeedDetailsRepo.findFirstByEngageIdAndTypeAndPageId(postId, EngageV2FeedTypeEnum.POST.name(), profileId));
        if (!postExists) {
            FreshPostNotificationRequest request = new FreshPostNotificationRequest();
            request.setPageId(profileId);
            request.setPostId(postId);
            request.setType(EngageV2FeedTypeEnum.POST.name());
            request.setChannel(SocialChannel.TIKTOK.getName());
            kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_GENERATE_TOPIC.getName(), request);
        }
    }

    @Override
    public void processTiktokCommentEvent(TikTokCommentEventRequest tikTokCommentRequest) {
        log.info("Tiktok comment engage notification received {}", tikTokCommentRequest);

        String profileId = tikTokCommentRequest.getUserOpenid();
        String commentId = tikTokCommentRequest.getContent().getCommentId();
        String commentAction = tikTokCommentRequest.getContent().getCommentAction();

        if (!isValidTiktokAccount(profileId, tikTokCommentRequest.getContent().getVideoId())) {
            return;
        }

        switch (commentAction.toLowerCase()) {
            case "delete":
                handleCommentAction(commentId, EngageActionsEnum.DELETE_CONTENT);
                break;

            case "set_to_hidden":
                handleCommentAction(commentId, EngageActionsEnum.HIDE_COMMENT);
                break;

            case "set_to_public":
                processCommentEngagement(tikTokCommentRequest, profileId, commentId);
                break;

            default:
                break;
        }
    }

    private boolean isValidTiktokAccount(String profileId, String videoId) {
        if (Objects.isNull(businessTiktokAccountsRepository.findFirstByProfileIdAndIsValid(profileId, 1))) {
            log.error("No TikTok account found for engage request with user openId: {} and postId: {}", profileId, videoId);
            return false;
        }
        return true;
    }

    private void handleCommentAction(String commentId, EngageActionsEnum action) {
        EventUpdateRequest eventUpdateRequest = EventUpdateRequest.builder()
                .eventId(commentId)
                .eventType(action)
                .sourceId(SocialChannel.TIKTOK.getId())
                .build();

        kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_SAVE_REACTION.getName(), eventUpdateRequest);
    }

    private void processCommentEngagement(TikTokCommentEventRequest tikTokCommentRequest, String profileId, String commentId) {
        if (Objects.isNull(engageFeedDetailsRepo.findFirstByFeedIdAndPageId(commentId, profileId))) {
            createNewEngageFeedDetails(tikTokCommentRequest, profileId);
        }
    }

    private void createNewEngageFeedDetails(TikTokCommentEventRequest tikTokCommentRequest, String profileId) {
        FreshPostNotificationRequest request = new FreshPostNotificationRequest();
        request.setPageId(profileId);
        request.setPostId(tikTokCommentRequest.getContent().getVideoId());
        request.setType(EngageV2FeedTypeEnum.POST.name());
        request.setChannel(SocialChannel.TIKTOK.getName());
        request.setNewCommentId(Objects.nonNull(tikTokCommentRequest.getContent()) ? tikTokCommentRequest.getContent().getCommentId() : null);
        kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_GENERATE_TOPIC.getName(), request);
    }
}
