package com.birdeye.social.service.SocialCompetitorService.Twitter;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dto.PicturesqueMediaCallback;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.model.*;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetails;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetailsDTO;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface TwitterCompetitorService {

    void fetchCompetitorPosts(CompetitorRequestDTO competitorRequestDTO) throws SocialBirdeyeException;

    void fetchCompetitorAccounts(List<String> accountIdentifier, Long enterpriseId) throws SocialBirdeyeException;

    void updateCompCache(SocialChannel channel, Long businessNumber);

    CompetitorListResponse getCompetitorList(Long businessNumber);

    void deleteCompetitor(DeleteCompetitorDetailRequest deleteRequest, Long enterpriseId);
    String getPageIdOnCompId(Integer rawCompId);

    void scanPages();

    List<CompetitorBasicDetail> getCompetitorsBasicDetails(List<String> pageIds);

    public void updateProfilePictureUrl(PicturesqueMediaCallback picturesqueMediaCallback, Integer rawCompId);

    void callPicturesQueForPage(PicturesqueCompRequest request);

    CompetitorPageDetailResponse getPageSummary(String pageId, Long businessNumber);

    void proceedToUnmapPage(Long businessNumber);

    Optional<Integer> fetchCompetitorProfileFollowerCount(CompetitorRequestDTO competitorRequestDTO);

    Map<String, CompetitorPageDetails> getPageNameByPageId(List<String> pageIds);

    List<CompetitorPageDetailsDTO> getPageDetails(List<String> pageIds);

    Integer getCompetitorAccounts(Long businessNumber);
}
