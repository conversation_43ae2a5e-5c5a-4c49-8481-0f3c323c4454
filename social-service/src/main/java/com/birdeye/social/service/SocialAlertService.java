package com.birdeye.social.service;

import com.birdeye.social.sro.BrokenEnterprisesList;
import com.birdeye.social.sro.EmailAlertDetailResponse;
import com.birdeye.social.sro.EmailAlertResponse;

import java.io.IOException;
import java.util.List;

public interface SocialAlertService {
    EmailAlertResponse getAllUserDataForEmail(Long enterpriseId, String type);

    List<EmailAlertResponse> getAllUserDataForEmailMultiple(List<Long> enterpriseId);

    /**
     * Optimized method to get user data for multiple enterprises with bulk operations
     * and parallel processing to reduce response time
     */
    List<EmailAlertResponse> getAllUserDataForEmailMultipleOptimized(List<Long> enterpriseIds);

    BrokenEnterprisesList getAllBrokenIntegrationEnterprises() throws IOException;

    void initEmailForBrokenIntegration() throws IOException;

	List<EmailAlertDetailResponse> getIntegrationData(Long parentBusinessNumber, List<Integer> businessIds);

	void submitEventForAllAllBrokenIntegrationEnt();

	void submitInitEmailForBrokenIntegration();

}
