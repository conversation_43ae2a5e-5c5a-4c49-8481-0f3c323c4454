package com.birdeye.social.service;


import com.birdeye.social.constant.MappingStatus;
import com.birdeye.social.constant.PageSortDirection;
import com.birdeye.social.constant.ResellerSearchType;
import com.birdeye.social.constant.ResellerSortType;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.elasticdto.SocialElasticDto;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.entities.SocialPagesAudit;
import com.birdeye.social.model.*;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetails;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetailsDTO;
import com.birdeye.social.model.instagram.TokenUpdateRequest;
import com.birdeye.social.model.notification.SocialNotificationAudit;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.sro.*;

import java.net.URISyntaxException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface FacebookSocialAccountService
{
	public void saveLocationPageMapping(Integer locationId,String fbPageId, Integer userId, String type, Long resellerId) throws Exception;

	public void saveMapping(BusinessFBPage fbPage,Integer locationId, Integer userId) throws Exception;

	public void cleanupPageMappings(List<FacebookRemovePageMappingCleanupRequest> fbRemovePageMappingCleanupReqs) throws Exception;

	public void removeFbLocationPageMapping(List<LocationPageMappingRequest> locationPageMappingRequests, String type, boolean unlink) throws Exception;
	
	public ChannelPageCount getCountObj(Business business);
	
	public ConnectedPages getConnectedPages(Map<Integer, BusinessEntity> idToBusinessMap, Long enterpriseId);
	
	public ReviewOptionsInfo getReviewShareOptions(Long enterpriseId,Map<Integer, BusinessEntity> idToBusinessMap,
			Map<Integer, String> locationToAddressMap, Integer startIndex, Integer count);
	
	public ReviewOptionDto updateReviewSharingOptions(Integer businessId, String pageId, Boolean autoPostingEnabled, Integer starVal,
			Integer postVal, Integer userId) throws Exception;
	
	public LocationPageMapping getLocationMappingPages(Long businessId, Integer userId, List<Integer> businessIds, Set<String> status,Integer page,Integer size,String search, List<String> includeModules, Integer accountId)  throws Exception;

	public void removeFbPage(Map<String, LocationPageMappingRequest> pageToMappingMap);

	void removeFacebookPages(List<BusinessFBPage> fbPages);

	public void removeResellerFbPage(List<String> pageIds,Integer limit);

	/**
	 * Get list of unmapped fb pages
	 *
	 * @param enterpriseId
	 * @return
	 */
	List<SocialPageListInfo> getUnmappedFbPagesByEnterpriseId(Long enterpriseId);
	
	public void copyPageToEnterprise();

	public void removeDuplicatePages();

	public void removeMultiPage();

	public void copyPageToEnterpriseWithNullUser();

	public void copyPageToEnterprise(Long enterpriseId);

	public void updateUser();

	void syncBusinessFacebookPageWithRawTableForEnterprise(Long enterpriseId);

	public void cancelRequest(String channel, Long businessId, Boolean forceCancel);

	void submitFetchPageRequest(ChannelAuthRequest request, String type) ;

	SSOResponse getFbSSOAuthDetails(FacebookSSOAuthRequest fbSSOAuthRequest);

	SSOResponse getFacebookUserDetailsByAccessToken(String accessToken);

	public void submitSystemUserFetchPageRequest(ChannelAuthRequest authRequest, String type) throws Exception;

	ChannelPageInfo connectPagesV1(List<String> pageIds, Long enterpriseId, Integer accountId) throws Exception;

	ChannelPageInfo connectResellerPages(List<String> pageIds, Long resellerId,Boolean selectAll, String searchStr);

	public Boolean checkForAutoMapping(List<String> pageIds, Long enterpriseId);

	public void triggerAutoMapping(Long enterpriseId);

	public void removeInactiveIntegration(String channel, Long businessId);

	void processDeletePageEvent(String channel, List<BusinessFBPage> existingPages);

	public void updateInvalidPageCounter(String pageId, Object details);

	public void markFBIntegrationInvalid();

	@Deprecated
	ChannelPageInfo getFBIntegrationRequestStatus(Long businessId, String key) throws Exception;

	void reconnectFBPagesEnhancedFlow(Long businessId, ChannelAllPageReconnectRequest request, Integer userId);

	void reconnectResellerFBPages(Long resellerId, ChannelAllPageReconnectRequest pageRequest, Integer userId, Integer limit);

	ChannelLocationInfo getSingleLocationMappingPages(Integer businessId);

	public void updateFBTokenPermissionsForAllPages();

	public String fetchPagePermissionSet(String pageId);

	public void backupFBPages(SocialPagesAudit socialPagesAudit);

	public String getFacebookIntegrationStatus(Integer businessId);

	boolean checkIfPageExistsByAccountId(Long accountId);

	ConnectedPages getPages(Long enterpriseId, PageConnectionStatus pageConnectionStatus);

	ConnectedPages getPagesForPostReconnect(Long enterpriseId, PageConnectionStatus pageConnectionStatus, SocialPostPageConnectRequest request);

	void updateAddress(SocialScanEventDTO socialScanEventDTO);

	PaginatedConnectedPages getResellerPages(Long enterpriseId, PageConnectionStatus pageConnectionStatus, Integer page, Integer size,
											 String search, ResellerSearchType searchType, PageSortDirection sortDirection,
											 ResellerSortType sortType, List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds,
											 Boolean locationFilterSelected);

	/**
	 * Method to check if any existing profile_id/place_id of a business is one of 'pageId'
	 * @param locationId
	 * @param pageId
	 * @param userId
	 * @return true if any existing profile_id/place_id of the business contains pageId that we are about to map
	 * @throws Exception
	 */
	public boolean isPageBusinessPlaceIdSame(Integer locationId, String pageId, Integer userId) throws Exception;

	public void processAutoMappingInitFbRequest(Long enterpriseId);

	public void processAutoMappingMatchedRequest(AutoMappingMatchedRequest request);

	public void fetchDisconnectedAndStore() ;

	public String getConnectCTA();

	public ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId);

	public ChannelSetupStatus.PageSetupStatus getPageConnectionSetupStatus(Long enterpriseId);

	BusinessIntegrationStatus.ChannelIntegrationInfo getPageIntegrationStatus(Integer businessId);

	OpenUrlFetchPageResponse submitFetchPageRequestForOpenURL(Long businessId,  ChannelAuthOpenUrlRequest authRequest) throws Exception;

	public OpenUrlPagesInfo getPagesFetchedByOpenUrl(Long enterpriseId) throws Exception;

	public SmbUnmappedDataResponse getUnmappedfbPagesForSmb();

	public OpenUrlPagesInfo connectPagesFetchedByOpenUrl(Long enterpriseId, OpenUrlConnectRequest connectRequest, Integer userId) throws Exception;
    
	void removeBusinessInactiveIntegration(String channel, Integer businessId);

	void moveFBAccountLocation(Long sourceEnterpriseNumber, Long targetEnterpriseNumber, Integer businessId, boolean isMultiLocationSource, Integer accountId);

	void removeUnmappedByEnterprise(Long enterpriseNumber, Integer businessId);

	void restoreBusinessInactiveIntegration(String name, Integer businessId);

	List<Integer> getFbActiveLocations();

	FbLocationsWithPermissions getFbLocationsWithPermissions(Long entNumber);

	Integer getUnmappedLocationCount(UnmappedLocationMappingReq request);

	List<BusinessFBPage> fetchRawPages(List<String> integrationIds);

    List<Number> fetchRawPagesId(List<String> integrationIds);

	List<SocialElasticDto> fetchPagesEsDto();

	List<SocialElasticDto> fetchPagesEsDto(Integer id);

	void checkFbTokenAndUpdate(List<Integer> rawId);

	List<BusinessFBPage> fetchRawPagesById(List<Integer> rawId);

	Map<Integer,List<BusinessFBPage>> fetchPagesByTokenValidity(List<BusinessFBPage> businessFBPages);

	Map<String, List<ChannelAccountInfo>> getPages(BusinessGetPageRequest businessGetPageRequest, Long enterpriseId);

	Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer pageNumber, Integer pageSize,String search);

	void updateAccessToken(TokenUpdateRequest request);

    void scheduleFbValidation();

    void validateAccessToken(FbDebugToken fbDebugToken);

    void saveMapping(LocationPageMappingRequest locationPageMappingRequest);

    void updateFbBusinessIsValid(SocialEsValidRequest socialEsValidRequest);

	void removePlatformMapping(List<LocationPageMappingRequest> input);

	void removePlatformEntry(List<ChannelPageRemoved> input);

    List<Integer> migrateForRatings(Integer limit);

    List<BusinessFBPage> findByBusinessId(Integer businessId);

	List<Integer> findBusinessIdsByEnterpriseId(Long businessIds, Integer isValid);

	List<BusinessFBPage> findByFacebookPageIdAndIsValid(String facebookPageId, Integer valid);

    void updatePlatformMapping(SocialChannelSyncRequest socialChannelSyncRequest);

	/**
	 * Given an accountId and List of Birdeye LocationIds return invalid/unmapped locations.
	 * This function will fetch relevent data from business_fb_page table and compute the unmapped pages
	 * based on received data(socialBusinessIds) from table and input values(BusinessIds)
	 * @param accountId : enterpriseId
	 * @param businessIds : input businessIds for filter
	 * @return
	 */
    Integer getIntegrationStatus(Long accountId, List<Integer> businessIds);

//    Page<BusinessFBPage> search(SearchDTO request, PageRequest pageRequest);

	ConnectPagesResponse getPagesAfterConnect(Long resellerId, Integer size, Integer page);

	void validityCheckForFB(Collection<String> locationIds);

	void updateEnterpriseWithNoReseller();

	void validityCheckForAllFBPages();

	void updatedFbPagesWithPhone();

	void removePageMap(Integer businessId);

    List<SocialNotificationAudit> auditNotifications(Object notificationObject);

	void getFbPageMentionsData(FacebookPageDataFetchRequest request);

	void getFbPageMentionsDataForListing(FacebookPageDataFetchRequest request);

	void initiateDPSync();

	void syncFacebookDP(DpSyncRequest facebookDpSyncRequest);

	void removeFacebookPagesByPageIds(List<String> pageIds);
	List<ApprovalPageInfo> findByFacebookPageId(String pageId);
	String checkPageIntegrationStatusByBusinessId(Integer businessId);

    List<String> findByBusinessIdIn(List<Integer> businessIds);

	List<SocialBusinessPageInfo> findByBusinessIds(List<Integer> businessIds);

	Map<String, CompetitorPageDetails> findByFacebookPageIdsIn(List<String> pageIds);

	List<CompetitorPageDetailsDTO> getPageDetails(List<String> pageId);

	List<Integer> getMappedResellerLeafLocations(List<Integer> resellerLeafLocationIds);

	FacebookPageInfo getFbPageInfo(Integer businessId) throws Exception;

	String getAuthorizationUrl(String origin) throws URISyntaxException;

	List<String> getMappedRequestIds(Set<String> requestIds);
	boolean isTokenValid(String accessToken);

	Validity fetchValidityAndErrorMessage(BusinessFBPage page, Boolean isWebChatEnabled, String fbPermissions);

	void getAcntsInfo(List<BusinessFBPage> fetchedPages, List<ChannelAccountInfo> managed, Long enterpriseId);

}