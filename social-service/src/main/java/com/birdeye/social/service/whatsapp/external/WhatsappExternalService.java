package com.birdeye.social.service.whatsapp.external;

import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.birdeye.social.service.whatsapp.dto.MetaBusinessVerificationStatus;
import com.birdeye.social.service.whatsapp.dto.PhoneNumberInformation;
import com.birdeye.social.service.whatsapp.dto.RegisterPhoneRequest;
import com.birdeye.social.service.whatsapp.dto.WABA.WABADetails;
import com.birdeye.social.service.whatsapp.dto.WhatsappMediaDataResponse;
import com.birdeye.social.service.whatsapp.dto.messages.MessageRequest;
import com.birdeye.social.service.whatsapp.dto.messages.response.MessageResponse;
import com.birdeye.social.service.whatsapp.dto.templates.MessageTemplateResponse;

public interface WhatsappExternalService {

    MessageTemplateResponse getTemplateDetailsByWabaId(String wabaId, String accessToken);


    MessageResponse sendWhatsappMesaage(String accessToken, String phoneNumberId, MessageRequest message);

    WABADetails getWabaInformationByWABAId(String extendedToken, String wabaId);

    PhoneNumberInformation getPhoneNumberDetailsByWABAId(String extendedToken, String wabaId);

    FacebookBaseResponse subscribeNotificationWebhook(String wabaId, String accessToken);

    FacebookBaseResponse unSubscribeNotificationWebhook(String wabaId, String accessToken);

    FacebookBaseResponse registerPhoneNumber(String phoneNumberId, String accessToken, RegisterPhoneRequest registerPhoneRequest);

    MetaBusinessVerificationStatus businessVerificationStatus(String metaBusinessId, String accessToken);

    byte[] downloadMedia(String accessToken, String mediaUrl);

    WhatsappMediaDataResponse retrieveMediaUrl(String accessToken, String mediaId);
}
