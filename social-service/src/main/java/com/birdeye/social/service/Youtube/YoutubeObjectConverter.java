package com.birdeye.social.service.Youtube;

import com.birdeye.social.dto.TokensAndUrlAuthData;
import com.birdeye.social.model.Youtube.YoutubeCategory.YoutubeCategory;
import com.birdeye.social.model.Youtube.YoutubeCategory.YoutubeCategoryItems;
import com.birdeye.social.model.Youtube.YoutubeCategory.YoutubeCategoryResponse;
import com.birdeye.social.model.Youtube.YoutubePlaylist.YoutubePlaylist;
import com.birdeye.social.model.Youtube.YoutubePlaylist.YoutubePlaylistItems;
import com.birdeye.social.sro.GoogleAuthToken;

import java.util.List;

public interface YoutubeObjectConverter {

    List<YoutubePlaylist> convertPlaylistItems(YoutubePlaylistItems items);

    List<YoutubeCategory> convertCategoriesItems(YoutubeCategoryItems items);

    TokensAndUrlAuthData convertAuthTokenResponse(GoogleAuthToken googleAuthToken);
}
