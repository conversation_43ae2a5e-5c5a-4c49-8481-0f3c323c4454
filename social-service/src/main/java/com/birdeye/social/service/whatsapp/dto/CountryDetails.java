package com.birdeye.social.service.whatsapp.dto;

public enum CountryDetails {

    TZ_UNKNOWN(0, "Unknown"),
    TZ_AMERICA_LOS_ANGELES(1, "United States"),
    TZ_AMERICA_DENVER(2, "United States"),
    TZ_PACIFIC_HONOLULU(3, "United States"),
    TZ_AMERICA_ANCHORAGE(4, "United States"),
    TZ_AMERICA_PHOENIX(5, "United States"),
    TZ_AMERICA_CHICAGO(6, "United States"),
    TZ_AMERICA_NEW_YORK(7, "United States"),
    TZ_ASIA_DUBAI(8, "United Arab Emirates"),
    TZ_AMERICA_ARGENTINA_SAN_LUIS(9, "Argentina"),
    TZ_AMERICA_ARGENTINA_BUENOS_AIRES(10, "Argentina"),
    TZ_AMERICA_ARGENTINA_SALTA(11, "Argentina"),
    TZ_EUROPE_VIENNA(12, "Austria"),
    T<PERSON>_AUSTRALIA_PERTH(13, "Australia"),
    TZ_AUSTRALIA_BROKEN_HILL(14, "Australia"),
    TZ_AUSTRALIA_SYDNEY(15, "Australia"),
    TZ_EUROPE_SARAJEVO(16, "Bosnia and Herzegovina"),
    TZ_ASIA_DHAKA(17, "Bangladesh"),
    TZ_EUROPE_BRUSSELS(18, "Belgium"),
    TZ_EUROPE_SOFIA(19, "Bulgaria"),
    TZ_ASIA_BAHRAIN(20, "Bahrain"),
    TZ_AMERICA_LA_PAZ(21, "Bolivia"),
    TZ_AMERICA_NORONHA(22, "Brazil"),
    TZ_AMERICA_CAMPO_GRANDE(23, "Brazil"),
    TZ_AMERICA_BELEM(24, "Brazil"),
    TZ_AMERICA_SAO_PAULO(25, "Brazil"),
    TZ_AMERICA_NASSAU(26, "Bahamas"),
    TZ_AMERICA_DAWSON(27, "Canada"),
    TZ_AMERICA_VANCOUVER(28, "Canada"),
    TZ_AMERICA_DAWSON_CREEK(29, "Canada"),
    TZ_AMERICA_EDMONTON(30, "Canada"),
    TZ_AMERICA_RAINY_RIVER(31, "Canada"),
    TZ_AMERICA_REGINA(32, "Canada"),
    TZ_AMERICA_ATIKOKAN(33, "Canada"),
    TZ_AMERICA_IQALUIT(34, "Canada"),
    TZ_AMERICA_TORONTO(35, "Canada"),
    TZ_AMERICA_BLANC_SABLON(36, "Canada"),
    TZ_AMERICA_HALIFAX(37, "Canada"),
    TZ_AMERICA_ST_JOHNS(38, "Canada"),
    TZ_EUROPE_ZURICH(39, "Switzerland"),
    TZ_PACIFIC_EASTER(40, "Chile"),
    TZ_AMERICA_SANTIAGO(41, "Chile"),
    TZ_ASIA_SHANGHAI(42, "China"),
    TZ_AMERICA_BOGOTA(43, "Colombia"),
    TZ_AMERICA_COSTA_RICA(44, "Costa Rica"),
    TZ_ASIA_NICOSIA(45, "Cyprus"),
    TZ_EUROPE_PRAGUE(46, "Czech Republic"),
    TZ_EUROPE_BERLIN(47, "Germany"),
    TZ_EUROPE_COPENHAGEN(48, "Denmark"),
    TZ_AMERICA_SANTO_DOMINGO(49, "Dominican Republic"),
    TZ_PACIFIC_GALAPAGOS(50, "Ecuador"),
    TZ_AMERICA_GUAYAQUIL(51, "Ecuador"),
    TZ_EUROPE_TALLINN(52, "Estonia"),
    TZ_AFRICA_CAIRO(53, "Egypt"),
    TZ_ATLANTIC_CANARY(54, "Spain"),
    TZ_EUROPE_MADRID(55, "Spain"),
    TZ_EUROPE_HELSINKI(56, "Finland"),
    TZ_EUROPE_PARIS(57, "France"),
    TZ_EUROPE_LONDON(58, "United Kingdom"),
    TZ_AFRICA_ACCRA(59, "Ghana"),
    TZ_EUROPE_ATHENS(60, "Greece"),
    TZ_AMERICA_GUATEMALA(61, "Guatemala"),
    TZ_ASIA_HONG_KONG(62, "Hong Kong"),
    TZ_AMERICA_TEGUCIGALPA(63, "Honduras"),
    TZ_EUROPE_ZAGREB(64, "Croatia"),
    TZ_EUROPE_BUDAPEST(65, "Hungary"),
    TZ_ASIA_JAKARTA(66, "Indonesia"),
    TZ_ASIA_MAKASSAR(67, "Indonesia"),
    TZ_ASIA_JAYAPURA(68, "Indonesia"),
    TZ_EUROPE_DUBLIN(69, "Ireland"),
    TZ_ASIA_JERUSALEM(70, "Israel"),
    TZ_ASIA_KOLKATA(71, "India"),
    TZ_ASIA_BAGHDAD(72, "Iraq"),
    TZ_ATLANTIC_REYKJAVIK(73, "Iceland"),
    TZ_EUROPE_ROME(74, "Italy"),
    TZ_AMERICA_JAMAICA(75, "Jamaica"),
    TZ_ASIA_AMMAN(76, "Jordan"),
    TZ_ASIA_TOKYO(77, "Japan"),
    TZ_AFRICA_NAIROBI(78, "Kenya"),
    TZ_ASIA_SEOUL(79, "South Korea"),
    TZ_ASIA_KUWAIT(80, "Kuwait"),
    TZ_ASIA_BEIRUT(81, "Lebanon"),
    TZ_ASIA_COLOMBO(82, "Sri Lanka"),
    TZ_EUROPE_VILNIUS(83, "Lithuania"),
    TZ_EUROPE_LUXEMBOURG(84, "Luxembourg"),
    TZ_EUROPE_RIGA(85, "Latvia"),
    TZ_AFRICA_CASABLANCA(86, "Morocco"),
    TZ_EUROPE_SKOPJE(87, "North Macedonia"),
    TZ_EUROPE_MALTA(88, "Malta"),
    TZ_INDIAN_MAURITIUS(89, "Mauritius"),
    TZ_INDIAN_MALDIVES(90, "Maldives"),
    TZ_AMERICA_TIJUANA(91, "Mexico"),
    TZ_AMERICA_HERMOSILLO(92, "Mexico"),
    TZ_AMERICA_MAZATLAN(93, "Mexico"),
    TZ_AMERICA_MEXICO_CITY(94, "Mexico"),
    TZ_ASIA_KUALA_LUMPUR(95, "Malaysia"),
    TZ_AFRICA_LAGOS(96, "Nigeria"),
    TZ_AMERICA_MANAGUA(97, "Nicaragua"),
    TZ_EUROPE_AMSTERDAM(98, "Netherlands"),
    TZ_EUROPE_OSLO(99, "Norway"),
    TZ_PACIFIC_AUCKLAND(100, "New Zealand"),
    TZ_ASIA_MUSCAT(101, "Oman"),
    TZ_AMERICA_PANAMA(102, "Panama"),
    TZ_AMERICA_LIMA(103, "Peru"),
    TZ_ASIA_MANILA(104, "Philippines"),
    TZ_ASIA_KARACHI(105, "Pakistan"),
    TZ_EUROPE_WARSAW(106, "Poland"),
    TZ_AMERICA_PUERTO_RICO(107, "Puerto Rico"),
    TZ_ASIA_GAZA(108, "Palestinian Territories"),
    TZ_ATLANTIC_AZORES(109, "Portugal"),
    TZ_EUROPE_LISBON(110, "Portugal"),
    TZ_AMERICA_ASUNCION(111, "Paraguay"),
    TZ_ASIA_QATAR(112, "Qatar"),
    TZ_EUROPE_BUCHAREST(113, "Romania"),
    TZ_EUROPE_BELGRADE(114, "Serbia"),
    TZ_EUROPE_KALININGRAD(115, "Russia"),
    TZ_EUROPE_MOSCOW(116, "Russia"),
    TZ_EUROPE_SAMARA(117, "Russia"),
    TZ_ASIA_YEKATERINBURG(118, "Russia"),
    TZ_ASIA_OMSK(119, "Russia"),
    TZ_ASIA_KRASNOYARSK(120, "Russia"),
    TZ_ASIA_IRKUTSK(121, "Russia"),
    TZ_ASIA_YAKUTSK(122, "Russia"),
    TZ_ASIA_VLADIVOSTOK(123, "Russia"),
    TZ_ASIA_MAGADAN(124, "Russia"),
    TZ_ASIA_KAMCHATKA(125, "Russia"),
    TZ_ASIA_RIYADH(126, "Saudi Arabia"),
    TZ_EUROPE_STOCKHOLM(127, "Sweden"),
    TZ_ASIA_SINGAPORE(128, "Singapore"),
    TZ_EUROPE_LJUBLJANA(129, "Slovenia"),
    TZ_EUROPE_BRATISLAVA(130, "Slovakia"),
    TZ_AMERICA_EL_SALVADOR(131, "El Salvador"),
    TZ_ASIA_BANGKOK(132, "Thailand"),
    TZ_AFRICA_TUNIS(133, "Tunisia"),
    TZ_EUROPE_ISTANBUL(134, "Turkey"),
    TZ_AMERICA_PORT_OF_SPAIN(135, "Trinidad and Tobago"),
    TZ_ASIA_TAIPEI(136, "Taiwan"),
    TZ_EUROPE_KIEV(137, "Ukraine"),
    TZ_AMERICA_MONTEVIDEO(138, "Uruguay"),
    TZ_AMERICA_CARACAS(139, "Venezuela"),
    TZ_ASIA_HO_CHI_MINH(140, "Vietnam"),
    TZ_AFRICA_JOHANNESBURG(141, "South Africa"),
    TZ_AMERICA_WINNIPEG(142, "Canada"),
    TZ_AMERICA_DETROIT(143, "United States"),
    TZ_AUSTRALIA_MELBOURNE(144, "Australia"),
    TZ_ASIA_KATHMANDU(145, "Nepal"),
    TZ_ASIA_BAKU(146, "Azerbaijan"),
    TZ_AFRICA_ABIDJAN(147, "Ivory Coast"),
    TZ_AFRICA_ADDIS_ABABA(148, "Ethiopia"),
    TZ_AFRICA_ALGIERS(149, "Algeria"),
    TZ_AFRICA_ASMARA(150, "Eritrea"),
    TZ_AFRICA_BAMAKO(151, "Mali"),
    TZ_AFRICA_BANGUI(152, "Central African Republic"),
    TZ_AFRICA_BANJUL(153, "Gambia"),
    TZ_AFRICA_BISSAU(154, "Guinea-Bissau"),
    TZ_AFRICA_BLANTYRE(155, "Malawi"),
    TZ_AFRICA_BRAZZAVILLE(156, "Republic of the Congo"),
    TZ_AFRICA_BUJUMBURA(157, "Burundi"),
    TZ_AFRICA_CEUTA(158, "Spain"),
    TZ_AFRICA_CONAKRY(159, "Guinea"),
    TZ_AFRICA_DAKAR(160, "Senegal"),
    TZ_AFRICA_DAR_ES_SALAAM(161, "Tanzania"),
    TZ_AFRICA_DJIBOUTI(162, "Djibouti"),
    TZ_AFRICA_DOUALA(163, "Cameroon"),
    TZ_AFRICA_EL_AAIUN(164, "Western Sahara"),
    TZ_AFRICA_FREETOWN(165, "Sierra Leone"),
    TZ_AFRICA_GABORONE(166, "Botswana"),
    TZ_AFRICA_HARARE(167, "Zimbabwe"),
    TZ_AFRICA_JUBA(168, "South Sudan"),
    TZ_AFRICA_KAMPALA(169, "Uganda"),
    TZ_AFRICA_KHARTOUM(170, "Sudan"),
    TZ_AFRICA_KIGALI(171, "Rwanda"),
    TZ_AFRICA_KINSHASA(172, "Democratic Republic of the Congo"),
    TZ_AFRICA_LIBREVILLE(173, "Gabon"),
    TZ_AFRICA_LOME(174, "Togo"),
    TZ_AFRICA_LUANDA(175, "Angola"),
    TZ_AFRICA_LUBUMBASHI(176, "Democratic Republic of the Congo"),
    TZ_AFRICA_LUSAKA(177, "Zambia"),
    TZ_AFRICA_MALABO(178, "Equatorial Guinea"),
    TZ_AFRICA_MAPUTO(179, "Mozambique"),
    TZ_AFRICA_MASERU(180, "Lesotho"),
    TZ_AFRICA_MBABANE(181, "Eswatini"),
    TZ_AFRICA_MOGADISHU(182, "Somalia"),
    TZ_AFRICA_MONROVIA(183, "Liberia"),
    TZ_AFRICA_NDJAMENA(184, "Chad"),
    TZ_AFRICA_NIAMEY(185, "Niger"),
    TZ_AFRICA_NOUAKCHOTT(186, "Mauritania"),
    TZ_AFRICA_OUAGADOUGOU(187, "Burkina Faso"),
    TZ_AFRICA_PORTO_NOVO(188, "Benin"),
    TZ_AFRICA_SAO_TOME(189, "São Tomé and Príncipe"),
    TZ_AFRICA_TRIPOLI(190, "Libya"),
    TZ_AFRICA_WINDHOEK(191, "Namibia"),
    TZ_AMERICA_ADAK(192, "United States"),
    TZ_AMERICA_ANGUILLA(193, "Anguilla"),
    TZ_AMERICA_ANTIGUA(194, "Antigua and Barbuda"),
    TZ_AMERICA_ARAGUAINA(195, "Brazil"),
    TZ_AMERICA_ARGENTINA_CATAMARCA(196, "Argentina"),
    TZ_AMERICA_ARGENTINA_CORDOBA(197, "Argentina"),
    TZ_AMERICA_ARGENTINA_JUJUY(198, "Argentina"),
    TZ_AMERICA_ARGENTINA_LA_RIOJA(199, "Argentina"),
    TZ_AMERICA_ARGENTINA_MENDOZA(200, "Argentina"),
    TZ_AMERICA_ARGENTINA_RIO_GALLEGOS(201, "Argentina"),
    TZ_AMERICA_ARGENTINA_SAN_JUAN(202, "Argentina"),
    TZ_AMERICA_ARGENTINA_TUCUMAN(203, "Argentina"),
    TZ_AMERICA_ARGENTINA_USHUAIA(204, "Argentina"),
    TZ_AMERICA_ARUBA(205, "Aruba"),
    TZ_AMERICA_BAHIA(206, "Brazil"),
    TZ_AMERICA_BAHIA_BANDERAS(207, "Mexico"),
    TZ_AMERICA_BARBADOS(208, "Barbados"),
    TZ_AMERICA_BELIZE(209, "Belize"),
    TZ_AMERICA_BOA_VISTA(210, "Brazil"),
    TZ_AMERICA_BOISE(211, "United States"),
    TZ_AMERICA_CAMBRIDGE_BAY(212, "Canada"),
    TZ_AMERICA_CANCUN(213, "Mexico"),
    TZ_AMERICA_CAYENNE(214, "French Guiana"),
    TZ_AMERICA_CAYMAN(215, "Cayman Islands"),
    TZ_AMERICA_CHIHUAHUA(216, "Mexico"),
    TZ_AMERICA_CRESTON(217, "Canada"),
    TZ_AMERICA_CUIABA(218, "Brazil"),
    TZ_AMERICA_CURACAO(219, "Curacao"),
    TZ_AMERICA_DANMARKSHAVN(220, "Greenland"),
    TZ_AMERICA_DOMINICA(221, "Dominica"),
    TZ_AMERICA_EIRUNEPE(222, "Brazil"),
    TZ_AMERICA_FORT_NELSON(223, "Canada"),
    TZ_AMERICA_FORTALEZA(224, "Brazil"),
    TZ_AMERICA_GLACE_BAY(225, "Canada"),
    TZ_AMERICA_GODTHAB(226, "Greenland"),
    TZ_AMERICA_GOOSE_BAY(227, "Canada"),
    TZ_AMERICA_GRAND_TURK(228, "Turks and Caicos Islands"),
    TZ_AMERICA_GRENADA(229, "Grenada"),
    TZ_AMERICA_GUADELOUPE(230, "France"),
    TZ_AMERICA_GUYANA(231, "Guyana"),
    TZ_AMERICA_HAVANA(232, "Cuba"),
    TZ_AMERICA_INDIANA_INDIANAPOLIS(233, "United States"),
    TZ_AMERICA_INDIANA_KNOX(234, "United States"),
    TZ_AMERICA_INDIANA_MARENGO(235, "United States"),
    TZ_AMERICA_INDIANA_PETERSBURG(236, "United States"),
    TZ_AMERICA_INDIANA_TELL_CITY(237, "United States"),
    TZ_AMERICA_INDIANA_VEVAY(238, "United States"),
    TZ_AMERICA_INDIANA_VINCENNES(239, "United States"),
    TZ_AMERICA_INDIANA_WINAMAC(240, "United States"),
    TZ_AMERICA_INDIANAPOLIS(241, "United States"),
    TZ_AMERICA_INUVIK(242, "Canada"),
    TZ_AMERICA_JUNEAU(243, "United States"),
    TZ_AMERICA_KENTUCKY_LOUISVILLE(244, "United States"),
    TZ_AMERICA_KENTUCKY_MONTICELLO(245, "United States"),
    TZ_AMERICA_KRALENDIJK(246, "Bonaire, Sint Eustatius and Saba"),
    TZ_AMERICA_LOWER_PRINCES(247, "Sint Eustatius and Saba"),
    TZ_AMERICA_MACEIO(248, "Brazil"),
    TZ_AMERICA_MANAUS(249, "Brazil"),
    TZ_AMERICA_MARIGOT(250, "Saint Martin"),
    TZ_AMERICA_MARTINIQUE(251, "Martinique"),
    TZ_AMERICA_MATAMOROS(252, "Mexico"),
    TZ_AMERICA_MENOMINEE(253, "United States"),
    TZ_AMERICA_MERIDA(254, "Mexico"),
    TZ_AMERICA_METLAKATLA(255, "Canada"),
    TZ_AMERICA_MIQUELON(256, "Saint Pierre and Miquelon"),
    TZ_AMERICA_MONCTON(257, "Canada"),
    TZ_AMERICA_MONTERREY(258, "Mexico"),
    TZ_AMERICA_MONTREAL(259, "Canada"),
    TZ_AMERICA_MONTSERRAT(260, "Montserrat"),
    TZ_AMERICA_NIPIGON(261, "Canada"),
    TZ_AMERICA_NOME(262, "United States"),
    TZ_AMERICA_NORTH_DAKOTA_BEULAH(263, "United States"),
    TZ_AMERICA_NORTH_DAKOTA_CENTER(264, "United States"),
    TZ_AMERICA_NORTH_DAKOTA_NEW_SALEM(265, "United States"),
    TZ_AMERICA_OJINAGA(266, "Mexico"),
    TZ_AMERICA_PANGNIRTUNG(267, "Canada"),
    TZ_AMERICA_PARAMARIBO(268, "Suriname"),
    TZ_AMERICA_PORT_AU_PRINCE(269, "Haiti"),
    TZ_AMERICA_PORTO_VELHO(270, "Brazil"),
    TZ_AMERICA_PUNTA_ARENAS(271, "Chile"),
    TZ_AMERICA_RANKIN_INLET(272, "Canada"),
    TZ_AMERICA_RECIFE(273, "Brazil"),
    TZ_AMERICA_RESOLUTE(274, "Canada"),
    TZ_AMERICA_RIO_BRANCO(275, "Brazil"),
    TZ_AMERICA_SANTAREM(276, "Brazil"),
    TZ_AMERICA_SCORESBYSUND(277, "Greenland"),
    TZ_AMERICA_SITKA(278, "United States"),
    TZ_AMERICA_ST_BARTHELEMY(279, "Saint Barthélemy"),
    TZ_AMERICA_ST_KITTS(280, "Saint Kitts and Nevis"),
    TZ_AMERICA_ST_LUCIA(281, "Saint Lucia"),
    TZ_AMERICA_ST_THOMAS(282, "United States Virgin Islands"),
    TZ_AMERICA_ST_VINCENT(283, "Saint Vincent and the Grenadines"),
    TZ_AMERICA_SWIFT_CURRENT(284, "Canada"),
    TZ_AMERICA_THULE(285, "Greenland"),
    TZ_AMERICA_THUNDER_BAY(286, "Canada"),
    TZ_AMERICA_TORTOLA(287, "British Virgin Islands"),
    TZ_AMERICA_WHITEHORSE(288, "Canada"),
    TZ_AMERICA_YAKUTAT(289, "United States"),
    TZ_AMERICA_YELLOWKNIFE(290, "Canada"),
    TZ_ANTARCTICA_CASEY(291, "Australia"),
    TZ_ANTARCTICA_DAVIS(292, "Australia"),
    TZ_ANTARCTICA_DUMONTDURVILLE(293, "France"),
    TZ_ANTARCTICA_MACQUARIE(294, "Australia"),
    TZ_ANTARCTICA_MAWSON(295, "Australia"),
    TZ_ANTARCTICA_MCMURDO(296, "United States"),
    TZ_ANTARCTICA_PALMER(297, "United States"),
    TZ_ANTARCTICA_ROTHERA(298, "United Kingdom"),
    TZ_ANTARCTICA_SYOWA(299, "Japan"),
    TZ_ANTARCTICA_TROLL(300, "Norway"),
    TZ_ANTARCTICA_VOSTOK(301, "Russia"),
    TZ_ARCTIC_LONGYEARBYEN(302, "Norway"),
    TZ_ASIA_ADEN(303, "Yemen"),
    TZ_ASIA_ALMATY(304, "Kazakhstan"),
    TZ_ASIA_ANADYR(305, "Russia"),
    TZ_ASIA_AQTAU(306, "Kazakhstan"),
    TZ_ASIA_AQTOBE(307, "Kazakhstan"),
    TZ_ASIA_ASHGABAT(308, "Turkmenistan"),
    TZ_ASIA_ATYRAU(309, "Kazakhstan"),
    TZ_ASIA_BARNAUL(310, "Russia"),
    TZ_ASIA_BISHKEK(311, "Kyrgyzstan"),
    TZ_ASIA_BRUNEI(312, "Brunei"),
    TZ_ASIA_CHITA(313, "Russia"),
    TZ_ASIA_CHOIBALSAN(314, "Mongolia"),
    TZ_ASIA_DAMASCUS(315, "Syria"),
    TZ_ASIA_DILI(316, "Timor-Leste"),
    TZ_ASIA_DUSHANBE(317, "Tajikistan"),
    TZ_ASIA_FAMAGUSTA(318, "Cyprus"),
    TZ_ASIA_HEBRON(319, "Palestine"),
    TZ_ASIA_HOVD(320, "Mongolia"),
    TZ_ASIA_ISTANBUL(321, "Turkey"),
    TZ_ASIA_KABUL(322, "Afghanistan"),
    TZ_ASIA_KHANDYGA(323, "Russia"),
    TZ_ASIA_KUCHING(324, "Malaysia"),
    TZ_ASIA_MACAU(325, "Macau"),
    TZ_ASIA_NOVOKUZNETSK(326, "Russia"),
    TZ_ASIA_NOVOSIBIRSK(327, "Russia"),
    TZ_ASIA_ORAL(328, "Kazakhstan"),
    TZ_ASIA_PHNOM_PENH(329, "Cambodia"),
    TZ_ASIA_PONTIANAK(330, "Indonesia"),
    TZ_ASIA_PYONGYANG(331, "North Korea"),
    TZ_ASIA_QOSTANAY(332, "Kazakhstan"),
    TZ_ASIA_QYZYLORDA(333, "Kazakhstan"),
    TZ_ASIA_SAKHALIN(334, "Russia"),
    TZ_ASIA_SAMARKAND(335, "Uzbekistan"),
    TZ_ASIA_SREDNEKOLYMSK(336, "Russia"),
    TZ_ASIA_TASHKENT(337, "Uzbekistan"),
    TZ_ASIA_TBILISI(338, "Georgia"),
    TZ_ASIA_TEHRAN(339, "Iran"),
    TZ_ASIA_THIMPHU(340, "Bhutan"),
    TZ_ASIA_TOMSK(341, "Russia"),
    TZ_ASIA_ULAANBAATAR(342, "Mongolia"),
    TZ_ASIA_URUMQI(343, "China"),
    TZ_ASIA_UST_NERA(344, "Russia"),
    TZ_ASIA_VIENTIANE(345, "Laos"),
    TZ_ASIA_YANGON(346, "Myanmar"),
    TZ_ASIA_YEREVAN(347, "Armenia"),
    TZ_ATLANTIC_BERMUDA(348, "Bermuda"),
    TZ_ATLANTIC_CAPE_VERDE(349, "Cape Verde"),
    TZ_ATLANTIC_FAROE(350, "Faroe Islands"),
    TZ_ATLANTIC_MADEIRA(351, "Portugal"),
    TZ_ATLANTIC_SOUTH_GEORGIA(352, "South Georgia and the South Sandwich Islands"),
    TZ_ATLANTIC_ST_HELENA(353, "Saint Helena"),
    TZ_ATLANTIC_STANLEY(354, "Falkland Islands"),

    TZ_AUSTRALIA_ADELAIDE(355, "Australia"),
    TZ_AUSTRALIA_BRISBANE(356, "Australia"),
    TZ_AUSTRALIA_CURRIE(357, "Australia"),
    TZ_AUSTRALIA_DARWIN(358, "Australia"),
    TZ_AUSTRALIA_EUCLA(359, "Australia"),
    TZ_AUSTRALIA_HOBART(360, "Australia"),
    TZ_AUSTRALIA_LINDEMAN(361, "Australia"),
    TZ_AUSTRALIA_LORD_HOWE(362, "Australia"),

    TZ_CET(363, "Central Europe"),
    TZ_CST6CDT(364, "United States"),
    TZ_EET(365, "Eastern Europe"),
    TZ_EST(366, "United States"),
    TZ_EST5EDT(367, "United States"),

    TZ_ETC_GMT(368, "Universal"),
    TZ_ETC_GMT_PLUS_0(369, "Universal"),
    TZ_ETC_GMT_PLUS_1(370, "Universal"),
    TZ_ETC_GMT_PLUS_10(371, "Universal"),
    TZ_ETC_GMT_PLUS_11(372, "Universal"),
    TZ_ETC_GMT_PLUS_12(373, "Universal"),
    TZ_ETC_GMT_PLUS_2(374, "Universal"),
    TZ_ETC_GMT_PLUS_3(375, "Universal"),
    TZ_ETC_GMT_PLUS_4(376, "Universal"),
    TZ_ETC_GMT_PLUS_5(377, "Universal"),
    TZ_ETC_GMT_PLUS_6(378, "Universal"),
    TZ_ETC_GMT_PLUS_7(379, "Universal"),
    TZ_ETC_GMT_PLUS_8(380, "Universal"),
    TZ_ETC_GMT_PLUS_9(381, "Universal"),

    TZ_ETC_GMT_MINUS_0(382, "Universal"),
    TZ_ETC_GMT_MINUS_1(383, "Universal"),
    TZ_ETC_GMT_MINUS_10(384, "Universal"),
    TZ_ETC_GMT_MINUS_11(385, "Universal"),
    TZ_ETC_GMT_MINUS_12(386, "Universal"),
    TZ_ETC_GMT_MINUS_13(387, "Universal"),
    TZ_ETC_GMT_MINUS_14(388, "Universal"),
    TZ_ETC_GMT_MINUS_2(389, "Universal"),
    TZ_ETC_GMT_MINUS_3(390, "Universal"),
    TZ_ETC_GMT_MINUS_4(391, "Universal"),
    TZ_ETC_GMT_MINUS_5(392, "Universal"),
    TZ_ETC_GMT_MINUS_6(393, "Universal"),
    TZ_ETC_GMT_MINUS_7(394, "Universal"),
    TZ_ETC_GMT_MINUS_8(395, "Universal"),
    TZ_ETC_GMT_MINUS_9(396, "Universal"),

    TZ_ETC_GMT0(397, "Universal"),
    TZ_ETC_GREENWICH(398, "Greenwich"),
    TZ_ETC_UNIVERSAL(399, "Universal"),
    TZ_ETC_ZULU(400, "Zulu"),
    TZ_EUROPE_ANDORRA(401, "Andorra"),
    TZ_EUROPE_ASTRAKHAN(402, "Serbia"),
    TZ_EUROPE_BUSINGEN(403, "Germany"),
    TZ_EUROPE_CHISINAU(404, "Moldova"),
    TZ_EUROPE_GIBRALTAR(405, "Gibraltar"),
    TZ_EUROPE_GUERNSEY(406, "Guernsey"),
    TZ_EUROPE_ISLE_OF_MAN(407, "Isle of Man"),
    TZ_EUROPE_JERSEY(408, "Jersey"),
    TZ_EUROPE_KIROV(409, "Russia"),
    TZ_EUROPE_MARIEHAMN(410, "Åland Islands"),
    TZ_EUROPE_MINSK(411, "Belarus"),
    TZ_EUROPE_MONACO(412, "Monaco"),
    TZ_EUROPE_NICOSIA(413, "Cyprus"),
    TZ_EUROPE_PODGORICA(414, "Montenegro"),
    TZ_EUROPE_SAN_MARINO(415, "San Marino"),
    TZ_EUROPE_SARATOV(416, "Russia"),
    TZ_EUROPE_SIMFEROPOL(417, "Ukraine"),
    TZ_EUROPE_TIRANE(418, "Albania"),
    TZ_EUROPE_ULYANOVSK(419, "Russia"),
    TZ_EUROPE_UZHGOROD(420, "Ukraine"),
    TZ_EUROPE_VADUZ(421, "Liechtenstein"),
    TZ_EUROPE_VATICAN(422, "Vatican City"),
    TZ_EUROPE_VOLGOGRAD(423, "Russia"),
    TZ_EUROPE_ZAPOROZHYE(424, "Ukraine"),

    TZ_GMT(425, "Greenwich"),
    TZ_HST(426, "United States (Hawaii)"),

    TZ_INDIAN_ANTANANARIVO(427, "Madagascar"),
    TZ_INDIAN_CHAGOS(428, "British Indian Ocean Territory"),
    TZ_INDIAN_CHRISTMAS(429, "Christmas Island"),
    TZ_INDIAN_COCOS(430, "Cocos Islands"),
    TZ_INDIAN_COMORO(431, "Comoros"),
    TZ_INDIAN_KERGUELEN(432, "French Southern Territories"),
    TZ_INDIAN_MAHE(433, "Seychelles"),
    TZ_INDIAN_MAYOTTE(434, "Mayotte"),
    TZ_INDIAN_REUNION(435, "Réunion"),

    TZ_MET(436, "Middle Europe Time (generic)"),
    TZ_MST(437, "United States"),
    TZ_MST7MDT(438, "United States"),
    TZ_PST8PDT(439, "United States"),

    TZ_PACIFIC_APIA(440, "Samoa"),
    TZ_PACIFIC_BOUGAINVILLE(441, "Papua New Guinea"),
    TZ_PACIFIC_CHATHAM(442, "New Zealand"),
    TZ_PACIFIC_CHUUK(443, "Micronesia"),
    TZ_PACIFIC_EFATE(444, "Vanuatu"),
    TZ_PACIFIC_ENDERBURY(445, "Kiribati"),
    TZ_PACIFIC_FAKAOFO(446, "Tokelau"),
    TZ_PACIFIC_FIJI(447, "Fiji"),
    TZ_PACIFIC_FUNAFUTI(448, "Tuvalu"),
    TZ_PACIFIC_GAMBIER(449, "French Polynesia"),
    TZ_PACIFIC_GUADALCANAL(450, "Solomon Islands"),
    TZ_PACIFIC_GUAM(451, "Guam"),
    TZ_PACIFIC_KIRITIMATI(452, "Kiribati"),
    TZ_PACIFIC_KOSRAE(453, "Micronesia"),
    TZ_PACIFIC_KWAJALEIN(454, "Marshall Islands"),
    TZ_PACIFIC_MAJURO(455, "Marshall Islands"),
    TZ_PACIFIC_MARQUESAS(456, "French Polynesia"),
    TZ_PACIFIC_MIDWAY(457, "United States (Midway Islands)"),
    TZ_PACIFIC_NAURU(458, "Nauru"),
    TZ_PACIFIC_NIUE(459, "Niue"),
    TZ_PACIFIC_NORFOLK(460, "Norfolk Island"),
    TZ_PACIFIC_NOUMEA(461, "New Caledonia"),
    TZ_PACIFIC_PAGO_PAGO(462, "American Samoa"),
    TZ_PACIFIC_PALAU(463, "Palau"),
    TZ_PACIFIC_PITCAIRN(464, "Pitcairn Islands"),
    TZ_PACIFIC_POHNPEI(465, "Micronesia"),
    TZ_PACIFIC_PORT_MORESBY(466, "Papua New Guinea"),
    TZ_PACIFIC_RAROTONGA(467, "Cook Islands"),
    TZ_PACIFIC_SAIPAN(468, "Northern Mariana Islands"),
    TZ_PACIFIC_TAHITI(469, "French Polynesia"),
    TZ_PACIFIC_TARAWA(470, "Kiribati"),
    TZ_PACIFIC_TONGATAPU(471, "Tonga"),
    TZ_PACIFIC_WAKE(472, "United States (Wake Island)"),
    TZ_PACIFIC_WALLIS(473, "Wallis and Futuna"),

    TZ_UTC(474, "Universal Time Coordinated"),
    TZ_WET(475, "Western European Time"),

    TZ_ASIA_CALCUTTA(476, "India"),
    TZ_ASIA_KATMANDU(477, "Nepal"),

    TZ_AMERICA_NUUK(478, "Greenland"),
    TZ_AMERICA_BUENOS_AIRES(479, "Argentina");





    ;


    private final Integer id;
    private final String value;

    CountryDetails(Integer id,String value) {
        this.id = id;
        this.value = value;
    }

    public Integer getId() {
        return id;
    }

    public String getValue() {
        return value;
    }

    public static String getValueById(Integer  id) {
        for (CountryDetails mapping : values()) {
            if (mapping.id == id) {
                return mapping.value;
            }
        }
        return null;
    }
}
