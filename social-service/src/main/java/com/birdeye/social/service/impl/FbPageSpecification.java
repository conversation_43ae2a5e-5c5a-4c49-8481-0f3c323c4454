package com.birdeye.social.service.impl;

import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.model.FilterPageRequest;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

public class FbPageSpecification {
    public static Specification<BusinessFBPage> getBusinessFBPages(FilterPageRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (request.getBusinessId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("businessId"), request.getBusinessId()));
            }

            if (request.getSocialId() != null && !request.getSocialId().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("facebookPageId"), request.getSocialId()));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}