package com.birdeye.social.service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.dao.GoogleMessagesAgentRepository;
import com.birdeye.social.dao.SocialGoogleMessagingRepository;
import com.birdeye.social.dto.GoogleMessagesAgentLiteDto;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.entities.GoogleMessagesAgent;
import com.birdeye.social.entities.SocialGoogleMessagingAudit;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.*;
import com.birdeye.social.utils.StringUtils;
import com.google.api.services.businesscommunications.v1.model.Agent;
import com.google.api.services.businesscommunications.v1.model.Brand;

import java.util.Date;
import java.util.Objects;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@Service
public class GoogleMessagesAgentServiceImpl implements GoogleMessagesAgentService {

	private final Logger logger = LoggerFactory.getLogger(GoogleMessagesAgentServiceImpl.class);

	@Autowired
	private GoogleMessagesAgentRepository agentRepo;

	@Autowired
	private SocialGoogleMessagingRepository socialGoogleMessagingRepository;

	@Autowired
	private GoogleMyBusinessPageService			googleMyBusinessPageService;

	@Autowired
	private KafkaProducerService producer;

	@Override
	@Cacheable(value = "googleMessagesAgent", key = "#enterpriseId.toString()", unless = "#result == null")
	public GoogleMessagesAgentLiteDto findDTOByEnterpriseId(Long enterpriseId) {
		return agentRepo.findDTOByEnterpriseId(enterpriseId);
	}

	@Override
	public void deleteAgent(GoogleMessagesAgent gmAgent) {
		if (gmAgent != null) {
			agentRepo.delete(gmAgent);
		}
	}

	@Override
	@CacheEvict(value = "googleMessagesAgent", key = "#enterpriseId.toString()")
	public void evictAgentCache(Long enterpriseId) { }

	@Override
	public Boolean agentExistsByEnterpriseId(Long enterpriseId) {
		return agentRepo.existsByEnterpriseIdAndAgentNameNotNull(enterpriseId);
	}

	@Override
	public Boolean agentExistsByDisplayName(String displayName) {
		return agentRepo.existsByAgentDisplayName(displayName);
	}

	@Override
	public Boolean agentExistsByEnterpriseIdAndStatus(Long enterpriseId, GoogleAgentStatus status) {
		return agentRepo.existsByEnterpriseIdAndStatusAndAgentNameNotNull(enterpriseId, status.name());
	}

	@Override
	public GoogleMessagesAgent saveAgentInfo(GoogleMessagesAgent gmAgent, GoogleAgentStatus status, SetupAgentRequest req, Agent agent, String comments) {
		if (gmAgent != null) {
			if (status != null) gmAgent.setStatus(status.name());
			if (agent != null) gmAgent.setAgentName(agent.getName());
			gmAgent.setLogoUrl(req.getLogoUrl());
			gmAgent.setWelcomeMsg(req.getWelcomeMessage());
			gmAgent.setOfflineMsg(req.getOfflineMessage());
			gmAgent.setPrivacyUrl(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getGoogleMessagesPrivacyPolicyUrl());
			gmAgent.setAgentDisplayName(req.getDisplayName());
			gmAgent.setBrandDisplayName(req.getDisplayName());
			gmAgent.setComments(comments);
			agentRepo.saveAndFlush(gmAgent);
		}
		return gmAgent;
	}

	public void saveAuditInfo(CreateGoogleMessagingAuditRequest req){
		SocialGoogleMessagingAudit audit=new SocialGoogleMessagingAudit();
		audit.setBrandName(req.getBrandName());
		audit.setAgentName(req.getAgentName());
		audit.setgMsgLocationComment(req.getGsmbComment());
		audit.setPlaceId(req.getPlaceId());
		audit.setLocationId(req.getLocationId());
		audit.setgMsgLocationName(req.getGsmbLocationName());
		audit.setStatus(req.getGsmbLocationStatus());
		audit.setAgentId(req.getAgentId());
		audit.setAgentStatus(req.getAgentStatus());
		socialGoogleMessagingRepository.saveAndFlush(audit);
	}

	@Override
	public void saveWidgetInfo(GoogleMessagesAgent gmAgent, EditWidgetRequest req){
		if (gmAgent != null) {
			gmAgent.setWidgetName(req.getWidgetName());
			agentRepo.saveAndFlush(gmAgent);
		}
	}

	@Override
	public GoogleMessagesAgent updateAgentInfo(GoogleMessagesAgent gmAgent, UpdateAgentRequest req, String comments) {
		if (gmAgent != null) {
			gmAgent.setWelcomeMsg(req.getWelcomeMessage());
			gmAgent.setOfflineMsg(req.getOfflineMessage());
			gmAgent.setComments(comments);
			agentRepo.saveAndFlush(gmAgent);
		}
		return gmAgent;
	}

	@Override
	public GoogleMessagesAgent findByEnterpriseId(Long enterpriseId) {
		return agentRepo.findFirstByEnterpriseId(enterpriseId);
	}

	@Override
	public List<GoogleMessagesAgent> findByEnterpriseIdIn(Long enterpriseId) {
		return agentRepo.findByEnterpriseIdIn(enterpriseId);
	}

	@Override
	public GoogleMessagesAgent findByEnterpriseIdAndStatus(Long enterpriseId, String status) {
		return agentRepo.findFirstByEnterpriseIdAndStatus(enterpriseId, status);
	}

	@Override
	public GoogleMessagesAgent updateStatus(Integer agentId, GoogleAgentStatus newStatus) {
		GoogleMessagesAgent gmAgent = agentRepo.findOne(agentId);
		gmAgent.setStatus(newStatus.name());
		agentRepo.saveAndFlush(gmAgent);
		return gmAgent;
	}

	@Override
	public void updateStatus(Integer agentId, String newStatus) {
		agentRepo.updateAgentWithStatus(agentId,newStatus);
	}

	@Override
	public GoogleMessagesAgent updateStatus(GoogleMessagesAgent gmAgent, GoogleAgentStatus newStatus) {
		gmAgent.setStatus(newStatus.name());
		agentRepo.saveAndFlush(gmAgent);
		return gmAgent;
	}

	@Override
	public GoogleMessagesAgent updateComments(Long enterpriseId, String comments) {
		GoogleMessagesAgent gmAgent = agentRepo.findFirstByEnterpriseId(enterpriseId);
		gmAgent.setComments(comments);
		agentRepo.saveAndFlush(gmAgent);
		return gmAgent;
	}

	@Override
	public GoogleMessagesAgent updateComments(GoogleMessagesAgent gmAgent, String comments) {
		if (gmAgent != null) {
			gmAgent.setComments(comments);
			agentRepo.saveAndFlush(gmAgent);
		}
		return gmAgent;
	}

	@Override
	public Boolean brandExistsByEnterpriseId(Long enterpriseId) {
		return agentRepo.existsByEnterpriseIdAndBrandNameNotNull(enterpriseId);
	}

	@Override
	public Boolean brandExistsByDisplayName(String displayName) {
		return agentRepo.existsByBrandDisplayName(displayName);
	}


	@Override
	public GoogleMessagesAgent addBrandInfo(GoogleMessagesAgent gmAgent, GoogleAgentStatus status, CreateBrandRequest req,
											Brand brand, String comments) {
		if (gmAgent == null)
			gmAgent = new GoogleMessagesAgent();
		if (brand != null)
			gmAgent.setBrandName(brand.getName());
		if (status != null)
			gmAgent.setStatus(status.name());
		if (StringUtils.isNotEmpty(comments))
			gmAgent.setComments(comments);
		if (brand != null && StringUtils.isNotEmpty(brand.getDisplayName()))
			gmAgent.setBrandDisplayName(brand.getDisplayName());
		else
			gmAgent.setBrandDisplayName(req.getDisplayName());
		gmAgent.setCreatedBy(req.getUserId());
		gmAgent.setEnterpriseId(req.getEnterpriseId());
		agentRepo.saveAndFlush(gmAgent);
		return gmAgent;
	}

	public void addAuditInfo (CreateGoogleMessagingAuditRequest req){
		producer.sendObjectV1(Constants.MESSAGING_AUDIT, req);
	}

	@Override
	public String getBrandName(Integer agentId) {
		GoogleMessagesAgent gmAgent = agentRepo.findOne(agentId);
		if (gmAgent != null)
			return gmAgent.getBrandName();
		return null;
	}

	@Override
	public void updateStatusAndName(GoogleMessagesAgent gmAgent) {
		if(gmAgent != null){
			agentRepo.saveAndFlush(gmAgent);
		}
		return;
	}

	@Override
	public GoogleMessagesAgent getAgent(Long enterpriseId) {
		if(enterpriseId != null){
			GoogleMessagesAgent gma = agentRepo.findFirstByEnterpriseId(enterpriseId);
			return gma;
		}
		return null;
	}

	@Override
	public Long findEnterpriseIdByAgentId(String agentId) {
		if (Objects.nonNull(agentId)) {
			List<Long> enterpriseIds = agentRepo.findEnterpriseByAgentId(agentId);
			if (!enterpriseIds.isEmpty()) {
				return enterpriseIds.get(0);
			}
		}
		return null;
	}

	@Override
	public GoogleMessagesAgent findByAgentId(String agentId) {
		if (Objects.nonNull(agentId)) {
			List<GoogleMessagesAgent> agents = agentRepo.findByAgentId(agentId);
			if (!agents.isEmpty()) {
				return agents.get(0);
			}
		}
		return null;
	}

	@Override
	public GoogleMessagesAgent getAgentById(Integer id) {
		if(id != null){
			GoogleMessagesAgent gma = agentRepo.findOne(id);
			return gma;
		}
		return null;
	}

	@Override
	public List<Long> findAllEnterpriseIds() {
		return agentRepo.findAllEnterpriseIds();
	}

	@Override
	public List<Long> findAllByIds(int size, Date date) {
		return agentRepo.findAllByEnterpriseIds(date, new PageRequest(0, size));
	}

	@Override
	public List<GoogleMessagesAgent> findByIdIn(Set<Integer> ids) {
		return agentRepo.findByIdIn(ids);
	}

	@Override
	public GoogleMessagesAgent findByAgentId(Integer agentId) {
		return agentRepo.findOne(agentId);
	}

	@Override
	public List<Integer> findIdByEnterpriseId(Long id) {
		return agentRepo.findIdByEnterpriseId(id);
	}

	public List<GoogleMessagesAgent> findAllByEnterpriseId(Long enterpriseId){
		return agentRepo.findAllAgentByEnterprise(enterpriseId);
	}

	@Override
	public List<GoogleMessagesAgent> findByEnterpriseIdAndWidgetName(Long enterpriseId, String widgetName) {
		return agentRepo.findByEnterpriseIdAndWidgetName(enterpriseId,widgetName);
	}

	@Override
	public GoogleMessagesAgent saveWidgetInfo(GoogleMessagesAgent agent) {
		GoogleMessagesAgent googleMessagesAgent = agentRepo.save(agent);
		return googleMessagesAgent;
	}

	@Override
	public Page<GoogleMessagesAgent> findAllAgents(int page, int size) {
		return agentRepo.findAllAgentsWithPageAndSize(new PageRequest(page,size));
	}

	@Override
	public Page<GoogleMessagesAgent> findAgentsByEnterpriseIdIn(List<Long> enterpriseIds) {
		return agentRepo.findAgentsByEnterpriseIdIn(enterpriseIds,new PageRequest(0,enterpriseIds.size()));
	}

	@Override
	public void updateWidgetName(String brandDisplayName, Integer agentId) {
		agentRepo.updateWidgetNameWhereId(brandDisplayName,agentId);
	}

	@Override
	@Cacheable(value = "googleMessagesAgent", key = "#agentName.toString()", unless = "#result == null")
	public GoogleMessagesAgentLiteDto findDTOByAgentName(String agentName) {
		return agentRepo.findDTOByAgentName(agentName);
	}
}
