package com.birdeye.social.service.appleMaps;

import com.birdeye.social.service.AppleConnectService;
import com.birdeye.social.sro.appleMaps.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class AppleMapsMediaServiceImpl implements AppleMapsMediaService{

    private final AppleConnectService appleConnectService;
    @Override
    public AppleResponseDto handleBrandMedia(AppleBrandMediaDto dto){
        log.info("Request received to handle appleBrandMedia for enterpriseId:{}, body:{}", dto.getEnterpriseId(), dto);
        AppleDto appleDto = null;
        AppleResponseDto appleResponseDto = null;
        switch (dto.getOperationType()){
            case MEDIA_UPLOAD:
                appleDto = AppleDto.builder().imageDetails(AppleImageDetails.builder().url(dto.getMediaUrl()).build()).build();
                log.info("Calling AppleConnectService to upload media for enterpriseId:{}", dto.getEnterpriseId());
                appleResponseDto = this.appleConnectService.uploadMedia(dto.getAppleCompanyId(), appleDto);
                break;
            case BUSINESS_ASSET_CREATE:
                AppleBusinessAssetDetails appleBusinessCreateAssetDetails = AppleBusinessAssetDetails.builder()
                    .imageId(dto.getAppleMediaId()).partnersAssetId(dto.getMediaUrl()).intent(dto.getBusinessAssetType().name()).build();
                appleDto = AppleDto.builder().businessAssetDetails(appleBusinessCreateAssetDetails).build();
                log.info("Calling AppleConnectService to create businessAsset for enterpriseId:{}", dto.getEnterpriseId());
                appleResponseDto = this.appleConnectService.createBusinessAsset(dto.getAppleCompanyId(), dto.getAppleBusinessId(), appleDto);
                break;
            case BUSINESS_ASSET_UPDATE:
                AppleBusinessAssetDetails appleBusinessUpdateAssetDetails = AppleBusinessAssetDetails.builder()
                        .imageId(dto.getAppleMediaId()).partnersAssetId(dto.getMediaUrl()).intent(dto.getBusinessAssetType().name()).build();
                appleDto = AppleDto.builder().businessAssetDetails(appleBusinessUpdateAssetDetails).build();
                log.info("Calling AppleConnectService to update businessAsset for enterpriseId:{}", dto.getEnterpriseId());
                appleResponseDto = this.appleConnectService.updateBusinessAsset(dto.getAppleCompanyId(), dto.getAppleBusinessId(), dto.getAppleBusinessAssetId(), appleDto);
                break;
            case BUSINESS_ASSET_DELETE:
                log.info("Calling AppleConnectService to delete businessAsset for enterpriseId:{}", dto.getEnterpriseId());
                this.appleConnectService.deleteBusinessAsset(dto.getAppleCompanyId(), dto.getAppleBusinessId(), dto.getAppleBusinessAssetId());
                break;
        }
        return appleResponseDto;
    }

    @Override
    public AppleResponseDto getImageMetaData(String appleCompanyId, String appleMediaId) {
        return this.appleConnectService.getImageMetaData(appleCompanyId, appleMediaId);
    }

    @Override
    public AppleResponseDto getBusinessAsset(String appleCompanyId, String appleBusinessId, String businessAssetId) {
        return this.appleConnectService.getBusinessAsset(appleCompanyId, appleBusinessId, businessAssetId);
    }

    @Override
    public AppleResponseDto getBusiness(String appleCompanyId, String appleBusinessId) {
        return this.appleConnectService.getBusiness(appleCompanyId, appleBusinessId);
    }
}
