package com.birdeye.social.service.SocialUnSubscribeService;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.SocialSetupAuditEnum;
import com.birdeye.social.entities.SocialSetupAudit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class TiktokUnSubscribe implements SocialUnSubscribe {

    private static final Logger LOGGER = LoggerFactory.getLogger(TiktokUnSubscribe.class);


    @Override
    public String channelName() {
        return SocialChannel.TIKTOK.getName();
    }

    @Override
    public void unsubscribeNotification(SocialSetupAudit socialSetupAudit) throws Exception {
        // TikTok does not have page level subscription
    }

    @Override
    public List<SocialSetupAudit> getSocialSetupAuditIds(Date fromDate, SocialSetupAuditEnum action) {
        return Collections.emptyList();
    }
}
