package com.birdeye.social.service;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessYoutubeChannelRepository;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessYoutubeChannel;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.model.Feed;
import com.birdeye.social.model.PageDetail;
import com.birdeye.social.model.SocialTimeline;
import com.birdeye.social.model.tiktok.TikTokHashtagResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service("YoutubePostServiceImpl")
public class YoutubePostServiceImpl implements ChannelPostService{

    @Autowired
    private BusinessYoutubeChannelRepository youtubeChannelRepository;

    @Override
    public SocialChannel channelName() {
        return SocialChannel.YOUTUBE;
    }

    @Override
    public SocialTimeline getFeedData(Date lastPostDate, SocialScanEventDTO data) {
        return null;
    }

    @Override
    public List<Object[]> findPageIdAndEnterpriseIdbyPageIds(List<String> channelIds) {
        return youtubeChannelRepository.findChannelIdAndEnterpriseIdByChannelIds(channelIds);
    }

    @Override
    public List<PageDetail> getPageDetails(List<String> pageIds) {
        List<PageDetail> pageDetails = new ArrayList<>();
        List<BusinessYoutubeChannel> youtubeChannels = youtubeChannelRepository.findByChannelIdIn(pageIds);
        if (CollectionUtils.isNotEmpty(youtubeChannels)) {
            pageDetails.addAll(youtubeChannels.stream().map(s -> new PageDetail(s.getChannelId(), s.getChannelName(),
                    s.getPictureUrl(), s.getBusinessId())).collect(Collectors.toList()));
        }
        return pageDetails;
    }

    @Override
    public Feed getPostFeedDetails(SocialPostPublishInfo request, SocialPost socialPost) {
        return new Feed();
    }

    @Override
    public SocialScanEventDTO prepareSocialScanEventDto(String pageId) {
        return null;
    }

    @Override
    public List<TikTokHashtagResponse.HashtagResponse> fetchRecommendedHashtags(String keyword, Integer businessId) throws Exception {
        return Collections.emptyList();
    }
}
