package com.birdeye.social.service;

import com.birdeye.social.dto.ApprovalPageInfo;
import com.birdeye.social.dto.SocialBusinessPageInfo;
import com.birdeye.social.dto.SocialTokenValidationDTO;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.model.GranularScopeUpdateRequest;
import com.birdeye.social.model.notification.SocialNotificationAudit;

import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

public interface InstagramSocialService {

    boolean getInstagramPermission(Integer accountId, List<String> modules);
    boolean getInstagramPostPermission(List<BusinessInstagramAccount> instagramPages, List<String> modules);

    void validateToken(SocialTokenValidationDTO socialTokenValidationDTO);

    List<SocialNotificationAudit> auditNotification(Object notificationObject);

    Map<String, Boolean> getInstagramPostPermissionPageWise(List<BusinessInstagramAccount> igAccounts, List<String> engage);
    void updateInstagramGranularScope(GranularScopeUpdateRequest request);

    List<ApprovalPageInfo> findByInstagramPageId(String pageId);

    List<String> findByBusinessIdIn(List<Integer> businessIds);

    List<SocialBusinessPageInfo> findByBusinessIds(List<Integer> businessIds);
}
