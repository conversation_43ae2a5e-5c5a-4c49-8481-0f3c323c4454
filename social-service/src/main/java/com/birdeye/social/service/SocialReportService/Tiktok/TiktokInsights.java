package com.birdeye.social.service.SocialReportService.Tiktok;

import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.PageInsightV2EsData;
import com.birdeye.social.insights.Facebook.PostDataAndInsightResponse;
import com.birdeye.social.tiktok.TikTokFeedData;

import java.util.List;

public interface TiktokInsights {

    void getPostInsights(BusinessPosts businessPosts, Boolean isFreshRequest);

    void tiktokPostDataAndInsightsToEs(PostData postData);

    PostDataAndInsightResponse getTiktokInsightsForPost(InsightsRequest insights, int startIndex, int pageSize, String sortParam,
                                                           String sortOrder, boolean excelDownload);

    void getPageInsightsFromTiktok(SocialScanEventDTO socialScanEventDTO);

    void postTiktokPageInsightToES(PageInsights pageInsights);

    PerformanceSummaryResponse getTiktokPerformanceData(InsightsRequest insightsRequest);

    PageInsightV2EsData getTiktokInsightsESData(InsightsRequest insightsRequest);

    PageInsightV2EsData getTiktokInsightsForPublishPost(InsightsRequest insightsRequest);

    Object getTiktokInsightsReportData(InsightsRequest insightsRequest);

    PostData createPostData(BusinessPosts businessPosts, TikTokFeedData tikTokFeedData) throws Exception;

    PageInsightV2EsData getTiktokInsightsForMessageSent(InsightsRequest insightsRequest);

     PageInsightsResponse getTiktokInsightsForPage(InsightsRequest insights) throws Exception;

    PageInsightsResponse getDemographicsInsights(InsightsRequest insights) throws Exception;

    Integer getTiktokPublishedPostInsights(InsightsRequest insightsRequest);

    List<ProfilePerformanceExcelResponse> getTiktokDemographicReportData(InsightsRequest insightsRequest,
                                                                         String channel);

    List<ProfilePerformanceExcelResponse> getTiktokAudienceGrowthReportData(InsightsRequest insightsRequest,
                                                                         String channel);

}