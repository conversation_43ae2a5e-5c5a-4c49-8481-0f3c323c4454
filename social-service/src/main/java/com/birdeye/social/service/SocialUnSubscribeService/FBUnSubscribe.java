package com.birdeye.social.service.SocialUnSubscribeService;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.SocialSetupAuditEnum;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.dao.SocialSetupAuditRepository;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.entities.SocialSetupAudit;
import com.birdeye.social.instagram.InstagramExternalService;
import com.birdeye.social.service.SocialUnSubscribeService.SocialUnSubscribe;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class FBUnSubscribe implements SocialUnSubscribe {
    @Autowired
    private InstagramExternalService instagramExternalService;
    @Autowired
    private BusinessInstagramAccountRepository instagramAccountRepository;
    @Autowired
    private SocialSetupAuditRepository auditRepository;
    @Autowired
    private SocialFBPageRepository fbPageRepository;
    private static final Logger LOGGER = LoggerFactory.getLogger(FBUnSubscribe.class);


    @Override
    public String channelName() {
        return SocialChannel.FACEBOOK.getName();
    }

    @Override
    public void unsubscribeNotification(SocialSetupAudit socialSetupAudit) throws Exception {
        BusinessFBPage fbPage = JSONUtils.fromJSON(socialSetupAudit.getEntity(),BusinessFBPage.class);
        if(Objects.nonNull(fbPage)) {
            List<BusinessInstagramAccount> instagramAccounts = instagramAccountRepository.findByFacebookPageIdAndBusinessIdIsNotNull(fbPage.getFacebookPageId());
            if (CollectionUtils.isEmpty(instagramAccounts)) {
                instagramExternalService.unSubscribeToFbApp(fbPage.getPageAccessToken());
            }
            LOGGER.info("FB page unsubscribed for pageId: {}", fbPage.getFacebookPageId());
        }
    }

    @Override
    public List<SocialSetupAudit> getSocialSetupAuditIds(Date fromDate, SocialSetupAuditEnum action) {
        List<SocialSetupAudit> auditList = auditRepository.findByChannelAndActionAndCreatedGreaterThanEqualOrderByIdDesc(SocialChannel.FACEBOOK.getName(),action.name(),fromDate);
        LOGGER.info("Entries found in Social setup audit: {}",auditList.size());
        if(CollectionUtils.isNotEmpty(auditList)) {
            List<String> pageIds = auditList.stream().map(SocialSetupAudit::getIntegrationId).collect(Collectors.toList());
            List<BusinessFBPage> businessFBPages = new ArrayList<>();
            if(action.equals(SocialSetupAuditEnum.REMOVE_PAGE)) {
                businessFBPages = fbPageRepository.findByFacebookPageIdIn(pageIds);
            } else if(action.equals(SocialSetupAuditEnum.REMOVE_MAPPING)) {
                businessFBPages = fbPageRepository.findByFacebookPageIdInAndBusinessIdIsNotNull(pageIds);
            }
            Set<String> existingPageIds = businessFBPages.stream().map(BusinessFBPage::getFacebookPageId).collect(Collectors.toSet());
            LOGGER.info("Existing page count: {}", existingPageIds.size());
            auditList.removeIf(audit -> existingPageIds.contains(audit.getIntegrationId()));
            LOGGER.info("Final setup audit list size: {}", auditList.size());
        }
        return auditList;
    }
}
