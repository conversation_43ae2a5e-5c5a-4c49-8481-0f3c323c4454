package com.birdeye.social.service.Youtube;

import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.insights.ES.PageInsightV2EsData;
import com.birdeye.social.insights.ES.PageReportEsData;
import com.birdeye.social.insights.ES.ReportSortingCriteria;
import com.birdeye.social.insights.InsightsRequest;
import com.birdeye.social.insights.LeadershipByPostsDataPoints;
import com.birdeye.social.insights.PageInsightsRequest;
import com.birdeye.social.insights.PerformanceSummaryResponse;
import org.elasticsearch.search.sort.SortOrder;

public interface YoutubeInsights {

    PageInsightV2EsData getYoutubeInsightsESData(InsightsRequest insights) throws Exception;

    PerformanceSummaryResponse getYoutubePerformanceData(InsightsRequest insights);

    PageReportEsData getYoutubeInsightsReportData(InsightsRequest insights) throws Exception;

    PageInsightV2EsData getYoutubeInsightsForPublishPost(InsightsRequest insights) throws Exception;

    PageInsightV2EsData getYoutubeInsightsForMessageVolume(InsightsRequest insights) throws Exception;


    boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights);

    LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest,
                                                        ReportSortingCriteria postSortingCriteria, SortOrder order,
                                                        Integer startIndex, Integer pageSize);
}
