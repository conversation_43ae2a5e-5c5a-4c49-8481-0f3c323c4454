package com.birdeye.social.service;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessLinkedinPageRepository;
import com.birdeye.social.dto.report.MonthlyScanDTO;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessLinkedinPage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("LinkedInAdminServiceImpl")
public class LinkedInAdminServiceImpl implements ChannelsAdminService {

    @Autowired
    private BusinessLinkedinPageRepository businessLinkedinPageRepository;
    private static final String MYSQL_DATE_FORMAT = "yyyy/MM/dd HH:mm:ss";
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(MYSQL_DATE_FORMAT);
    private static final long MILLIS_IN_A_DAY = 1000 * 60 * 60 * 24;
    private static final Logger log = LoggerFactory.getLogger(LinkedInAdminServiceImpl.class);
    private static final String ORGANIZATION = "organization";
    @Override
    public List<SocialScanEventDTO> fetchEligibleRecords(Integer count, Integer startId, Date date) throws ParseException {
        Page<BusinessLinkedinPage> pages = businessLinkedinPageRepository.findByIsValidAndBusinessIdNotNullAndNextSyncDateIsLessThanEqualOrderByNextSyncDateAsc(1, date, new PageRequest(startId, count));
        log.info("LinkedIn Pages fetched to scan {}", pages);
        //TODO LINKEDIN_INSIGHTS_PERMISSION needs to be checked
        Date nextDate = simpleDateFormat.parse(simpleDateFormat.format(nextScanDate(date)));
        if(CollectionUtils.isEmpty(pages.getContent())){
            return new ArrayList<>();
        }
        List<BusinessLinkedinPage> eligiblePages = pages.getContent().stream().filter(page ->
                (Objects.nonNull(page.getUrn()) && page.getUrn().toLowerCase().contains(ORGANIZATION))).collect(Collectors.toList());
        List<Integer> pageIds = pages.getContent().stream().map(BusinessLinkedinPage::getId).collect(Collectors.toList());
        updateNextSyncDate(pageIds, nextDate);
        return conversionToScanEventDTO(eligiblePages);
    }

    private static Date nextScanDate(Date date){
        return new Date(date.getTime() + MILLIS_IN_A_DAY);
    }


    private List<SocialScanEventDTO> conversionToScanEventDTO(List<BusinessLinkedinPage> linkedInPages) {
        List<SocialScanEventDTO> scanEventList = new ArrayList<>();

        if(CollectionUtils.isEmpty(linkedInPages)){
            return scanEventList;
        }
        for(BusinessLinkedinPage linkedInPage : linkedInPages) {
            SocialScanEventDTO scanEventDTO = new SocialScanEventDTO();
            scanEventDTO.setChannelPrimaryId(linkedInPage.getId());
            scanEventDTO.setBusinessId(linkedInPage.getBusinessId());
            scanEventDTO.setEnterpriseId(linkedInPage.getEnterpriseId());
            scanEventDTO.setExternalId(linkedInPage.getProfileId());
            scanEventDTO.setAccountId(linkedInPage.getAccountId());
            if("company".equalsIgnoreCase(linkedInPage.getPageType())){
                scanEventDTO.setPageName(linkedInPage.getCompanyName());
            } else {
                String fullName = null;
                if(StringUtils.isNotBlank(linkedInPage.getFirstName()) && StringUtils.isNotBlank(linkedInPage.getLastName())){
                    fullName = linkedInPage.getFirstName() + " "+ linkedInPage.getLastName() ;
                } else {
                    fullName =linkedInPage.getFirstName();
                }
                scanEventDTO.setPageName(fullName);
            }
            scanEventDTO.setSourceName(SocialChannel.LINKEDIN.getName());
            scanEventDTO.setSourceId(SocialChannel.LINKEDIN.getId());


            scanEventList.add(scanEventDTO);
        }
        log.info("BusinessLinkedinPage Scan event dto ready for {}", scanEventList);

        return scanEventList;
    }

    @Override
    public void updateNextSyncDate(List<Integer> eligibleIds, Date nextDate) {
        businessLinkedinPageRepository.updateNextSyncDate(nextDate, eligibleIds);
    }

    @Override
    public SocialChannel channelName() {
       return SocialChannel.LINKEDIN;
    }

    @Override
    public List<MonthlyScanDTO> fetchEligibleRecordsMonthly() throws ParseException {
        return null;
    }

    @Override
    public SocialScanEventDTO fetchChannelDetails(String channelId, Date date) throws ParseException {
        return null;
    }
}
