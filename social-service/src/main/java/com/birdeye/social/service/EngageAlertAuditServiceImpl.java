package com.birdeye.social.service;

import com.birdeye.social.dao.EngageAlertAuditRepo;
import com.birdeye.social.entities.EngageAlertNotificationAudit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Transactional
public class EngageAlertAuditServiceImpl implements EngageAlertAuditService{
    @Autowired
    private EngageAlertAuditRepo engageAlertAuditRepo;


    @Override
    public void saveAudit(Long enterpriseId, Integer businessId, String feedId, List<String> receiverEmails, Boolean isRealTimeNotification, Map<String,Object> payload, String comment, String status) {
        EngageAlertNotificationAudit engageAlertNotificationAudit=EngageAlertNotificationAudit.builder().enterpriseId(enterpriseId).businessId(businessId)
                .feedId(Objects.isNull(feedId)?null:feedId).receiverEmails(receiverEmails.toString()).isRealTimeNotification(isRealTimeNotification).payload(Objects.isNull(payload)?null:payload.toString())
                .comment(comment).status(status).build();
        engageAlertAuditRepo.saveAndFlush(engageAlertNotificationAudit);
    }
}
