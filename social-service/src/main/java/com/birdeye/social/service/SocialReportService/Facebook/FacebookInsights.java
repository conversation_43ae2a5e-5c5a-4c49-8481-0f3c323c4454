package com.birdeye.social.service.SocialReportService.Facebook;

import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.facebook.FacebookFeedV2;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.*;
import com.birdeye.social.insights.ES.Request.EsFacebookPageInsightMetricsResponse;
import com.birdeye.social.insights.Facebook.PostDataAndInsightResponse;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import org.elasticsearch.search.sort.SortOrder;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface FacebookInsights {

    PageInsightsResponse getFacebookInsightsForPage(InsightsRequest insights) throws Exception;

    PageInsightV2EsData getFacebookInsightsESData(InsightsRequest insights) throws Exception;

    PageInsightV2EsData getFacebookInsightsForMessageSent(InsightsRequest insights) throws Exception;

    PageInsightV2EsData getFacebookInsightsForPublishPost(InsightsRequest insights, SearchTemplate searchTemplate) throws Exception;

    List<ProfilePerformanceExcelResponse> getPublishedPostBehaviourReportData(InsightsRequest insights, SearchTemplate searchTemplate) throws Exception;

    PageInsightV2EsData getFacebookInsightsForVideos(InsightsRequest insights) throws Exception;

    EsFacebookPageInsightMetricsResponse getFacebookInsightsForPageByLocation(InsightsRequest insights) throws Exception;

    PostDataAndInsightResponse getFacebookInsightsForPost(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam,
                                                          String sortOrder, boolean excelDownload);

    void postFacebookPageInsightToES(PageInsights pageInsights);

    void getPostInsights(BusinessPosts businessPosts, Boolean isFreshRequest);

    void postFacebookPostInsightsToEs(PostData postData);

    void updateToPostAndPageIndexEs(NewFbPostData newFbPostData,BusinessPosts businessPosts) throws IOException;

    void startScanForPosts(String pageId);

    void saveCDNPostToES(BusinessPosts businessPosts);

//    void postFacebookPageInsights(PageInsights pageInsights);

    PerformanceSummaryResponse getFacebookPerformanceData(InsightsRequest insights);

    boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights);

    // facebook reporting data used on profile performance tab
    PageReportEsData getFacebookInsightsReportData(InsightsRequest insights) throws Exception;

    PageReportEsData getMessageVolumeInsightsReportData(InsightsRequest insights) throws Exception;

    PostData createPostData(BusinessPosts businessPosts, Map<String,Object> insightData, FacebookFeedV2 engagementData) throws Exception;

    void backfillEngagementBreakdown(BackfillInsightReq backfillInsightReq);

    void backfillEngagementBreakdownPageWise(BackfillRequest backfillRequest);


    PostDataAndInsightResponse getFacebookInsightsForTopPost(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam,
                                                          String sortOrder, boolean excelDownload);

    LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest,
                                                        ReportSortingCriteria postSortingCriteria,
                                                        SortOrder order, Integer startIndex, Integer pageSize);

    List<ProfilePerformanceExcelResponse> getFacebookInsightsReportingData(InsightsRequest insights);

}
