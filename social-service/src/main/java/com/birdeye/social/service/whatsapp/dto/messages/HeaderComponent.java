package com.birdeye.social.service.whatsapp.dto.messages;

import com.birdeye.social.service.whatsapp.dto.messages.type.ComponentType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)

public class HeaderComponent extends Component<HeaderComponent> {
    /**
     * Instantiates a new Component.
     */
    public HeaderComponent() {
        super(ComponentType.HEADER);
    }


}
