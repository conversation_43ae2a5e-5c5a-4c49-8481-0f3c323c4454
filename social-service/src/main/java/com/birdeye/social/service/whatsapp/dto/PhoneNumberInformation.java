package com.birdeye.social.service.whatsapp.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


@JsonIgnoreProperties(ignoreUnknown = true)
public class PhoneNumberInformation  {

    private String id;
    private String display_phone_number;
    private String verified_name;
    private String status;
    private String quality_rating;
    private String code_verification_status;
    private String messaging_limit_tier;


    public String getMessaging_limit_tier() {
        return messaging_limit_tier;
    }

    public void setMessaging_limit_tier(String messaging_limit_tier) {
        this.messaging_limit_tier = messaging_limit_tier;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDisplay_phone_number() {
        return display_phone_number;
    }

    public void setDisplay_phone_number(String display_phone_number) {
        this.display_phone_number = display_phone_number;
    }

    public String getVerified_name() {
        return verified_name;
    }

    public void setVerified_name(String verified_name) {
        this.verified_name = verified_name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getQuality_rating() {
        return quality_rating;
    }

    public void setQuality_rating(String quality_rating) {
        this.quality_rating = quality_rating;
    }

    public String getCode_verification_status() {
        return code_verification_status;
    }

    public void setCode_verification_status(String code_verification_status) {
        this.code_verification_status = code_verification_status;
    }
}
