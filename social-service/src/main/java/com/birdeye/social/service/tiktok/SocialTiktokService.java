package com.birdeye.social.service.tiktok;

import com.birdeye.social.entities.BusinessTiktokAccounts;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.model.ChannelAuthOpenUrlRequest;
import com.birdeye.social.model.OpenUrlConnectRequest;
import com.birdeye.social.model.OpenUrlFetchPageResponse;
import com.birdeye.social.model.tiktok.TikTokCommentResponse;
import com.birdeye.social.sro.OpenUrlPagesInfo;
import com.birdeye.social.insights.tiktok.TiktokInsightRequest;
import com.birdeye.social.tiktok.TikTokAccountAccessInfo;
import com.birdeye.social.tiktok.TikTokFeedData;
import com.birdeye.social.tiktok.TikTokPostStatusResponse;
import com.birdeye.social.tiktok.TikTokPageData;

import java.util.List;

public interface SocialTiktokService {
    void postOnTikTok(SocialPostPublishInfo publishInfo) throws Exception;

    OpenUrlPagesInfo getPagesFetchedByOpenUrl(Long enterpriseId) throws Exception;

    OpenUrlPagesInfo connectPagesFetchedByOpenUrl(Long enterpriseId, OpenUrlConnectRequest connectRequest, Integer userId);

    OpenUrlFetchPageResponse submitFetchPageRequestForOpenURL(Long enterpriseId, ChannelAuthOpenUrlRequest authRequest);

    TikTokFeedData getTikTokUserVideos(TikTokAccountAccessInfo accessInfo, Long nextCursor);

    TikTokFeedData getTikTokUserVideosPaginated(TikTokAccountAccessInfo accessInfo, Long nextCursor, int limit);

    TikTokFeedData getTiktokPostData(String pageId, String videoId, String accessInfo);

    TikTokPostStatusResponse getPostStatus(Integer businessId, String postId, BusinessTiktokAccounts tiktokAccounts);
    TikTokPageData getTiktokPageData(TiktokInsightRequest tiktokInsightRequest);

    String getAccessToken(BusinessTiktokAccounts tiktokAccount, boolean cacheEvict);

    List<TikTokCommentResponse.TikTokComment> getCommentsOnTiktokVideo(String accessToken, String businessId, String videoId);
}
