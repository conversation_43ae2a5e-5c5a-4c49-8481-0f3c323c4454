package com.birdeye.social.service.doup;

import com.birdeye.social.constant.*;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.dto.BusinessFBPageLite;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.SocialStreams;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.SocialStreamConnectRequest;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.FacebookPageService;
import com.birdeye.social.service.FacebookSocialAccountService;
import com.birdeye.social.service.SocialErrorMessagePageService;
import com.birdeye.social.sro.LocationPageMappingRequest;
import com.birdeye.social.sro.socialReseller.*;
import com.birdeye.social.sro.socialenterprise.SocialEnterpriseBulkImportDTO;
import com.birdeye.social.sro.socialenterprise.SocialEnterpriseBulkStatusDTO;
import com.birdeye.social.sro.socialenterprise.SocialEnterpriseReportUploadDTO;
import com.birdeye.social.sro.socialenterprise.SocialEnterpriseReportUnmappedFacebookPages;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.SOCIAL_STREAM_CONNECT;

@Service
public class FBDoupConsumeRecords implements DoupConsumeRecords{

    @Autowired
    private SocialFBPageRepository socialFbRepo;
    @Autowired
    private SocialErrorMessagePageService socialErrorMessageService;
    @Autowired
    private KafkaProducerService kafkaProducer;
    @Autowired
    private FacebookPageService fbPageService;
    @Autowired
    private CommonService commonService;

    @Autowired
    private FacebookSocialAccountService facebookSocialAccountService;
    private static Logger logger	= LoggerFactory.getLogger(FBDoupConsumeRecords.class);


    @Override
    public String channelName() {
        return SocialChannel.FACEBOOK.getName();
    }

    @Override
    public SocialResellerReportUploadDTO processMappingIntegrationReport(Long resellerId, Integer size, Integer page) {
        try {
            SocialResellerReportUploadDTO unmappedFbPages = new SocialResellerReportUploadDTO();
            Page<BusinessFBPageLite> unmappedBusinessFbPage = socialFbRepo.findByResellerIdAndBusinessIdIsNull(resellerId, new PageRequest(page, size));
            if (Objects.isNull(unmappedBusinessFbPage.getContent()) ) {
                return null;
            }

            List<SocialResellerReportUploadFacebookDatum> socialResellerReportUploadFacebookDatum =
                    unmappedBusinessFbPage.getContent().stream().map(data -> {
                        SocialResellerReportUploadFacebookDatum conf = new SocialResellerReportUploadFacebookDatum();
                        conf.setFacebookPageId(data.getFacebookPageId());
                        conf.setFacebookPageAddress(data.getSingleLineAddress());
                        conf.setFacebookPageName(data.getFacebookPageName());
                        return conf;
                    } ).collect(Collectors.toList());

            unmappedFbPages.setData(socialResellerReportUploadFacebookDatum);
            unmappedFbPages.setPageCount(unmappedBusinessFbPage.getTotalPages());
            unmappedFbPages.setTotalCount(unmappedBusinessFbPage.getTotalElements());

            return unmappedFbPages;

        } catch (Exception e) {
            logger.error("something went wrong while fetching the location mapping data for resellerId {}, error {}", resellerId, e);
            return null;
        }
    }

    @Override
    public  List<Integer> processLocationUnMappingIntegration(Long resellerId) {
         return socialFbRepo.findByResellerIdAndBusinessIdIsNotNull(resellerId);
    }


    @Override
    public SocialResellerBulkStatusDTO mapPageWithLocation(SocialResellerBulkImportDTO data, Long resellerId, BusinessLiteDTO businessDetails,
                                                           Long enterpriseId, Integer accountId) {
        SocialResellerBulkStatusDTO socialResellerBulkStatus = new SocialResellerBulkStatusDTO();
        try {
            socialResellerBulkStatus.setEventId(data.getEventId());
            socialResellerBulkStatus.setOperation("ADD");

            String pageId= data.getData().getSocialPageId();
            Integer businessId = businessDetails.getBusinessId();


            List<BusinessFBPage> pageDetails = socialFbRepo.findByFacebookPageId(pageId);
            BusinessFBPage businessFBPageLite = pageDetails.isEmpty() ? null : pageDetails.get(0);

            if(Objects.isNull(businessFBPageLite) ) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.PAGE_NOT_FOUND.name(),SocialChannel.FACEBOOK.getLabel());
                return createFBDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            } else if(Objects.nonNull(businessFBPageLite.getBusinessId())) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.PAGE_ALREADY_MAPPED_ERR.name(),SocialChannel.FACEBOOK.getLabel());
                return createFBDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.DUPLICATE.name());
            } else if (!businessFBPageLite.getResellerId().equals(resellerId)) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.ACCOUNT_ACCESS_ERR.name(),SocialChannel.FACEBOOK.getLabel());
                return createFBDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            } else if (socialFbRepo.existsByBusinessId(businessId)) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.BUSINESS_MAPPED_ERR.name(),SocialChannel.FACEBOOK.getLabel());
                return createFBDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            }

            businessFBPageLite.setBusinessId(businessId);
            businessFBPageLite.setEnterpriseId(enterpriseId);
            businessFBPageLite.setAccountId(accountId);

            socialFbRepo.saveAndFlush(businessFBPageLite);
            socialResellerBulkStatus.setStatus(ResellerMappingStatusEnum.SUCCESS.name());
            socialResellerBulkStatus.setErrorMessage("");
            kafkaProducer.sendObject(Constants.FB_PAGE_MAPPING_ADDED, new LocationPageMappingRequest(businessFBPageLite.getBusinessId(),businessFBPageLite.getFacebookPageId() ));
            fbPageService.performPostMappingAction(businessFBPageLite);
            commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(), Arrays.asList(businessFBPageLite), null, businessFBPageLite.getBusinessId(), businessFBPageLite.getEnterpriseId());

            // trigger create stream
            if(commonService.checkBusinessSMB(businessDetails)) {
                createSocialStreamForSmb(businessFBPageLite.getId(), SocialChannel.FACEBOOK.getName());
            }
            return socialResellerBulkStatus;
        } catch (Exception e) {
            logger.error("RESELLER_MAPPING_EVENT: FOR facebook, cannot ap location with reseller {} {}", resellerId, e.toString() );
            String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.UNKNOWN_ERR.name(),SocialChannel.FACEBOOK.getLabel());
            socialResellerBulkStatus.setErrorMessage(errMessage);
            return createFBDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
        }
    }

    @Override
    public SocialEnterpriseBulkStatusDTO mapPageWithEnterpriseLocation(SocialEnterpriseBulkImportDTO data, BusinessLiteDTO businessDetails,
                                                                       Long enterpriseId, Integer accountId) {
        SocialEnterpriseBulkStatusDTO socialEnterpriseBulkStatus = new SocialEnterpriseBulkStatusDTO();
        try {
            socialEnterpriseBulkStatus.setEventId(data.getEventId());
            socialEnterpriseBulkStatus.setOperation("ADD");
            String pageId= data.getData().getSocialPageId();
            Integer businessId = businessDetails.getBusinessId();
            List<BusinessFBPage> pageDetails = socialFbRepo.findByFacebookPageId(pageId);
            BusinessFBPage businessFBPageLite = pageDetails.isEmpty() ? null : pageDetails.get(0);

            if(Objects.isNull(businessFBPageLite) ) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.PAGE_NOT_FOUND.name(),SocialChannel.FACEBOOK.getLabel());
                return commonService.createEnterpriseDoupErrorResponse(socialEnterpriseBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            } else if(Objects.nonNull(businessFBPageLite.getBusinessId())) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.PAGE_ALREADY_MAPPED_ERR.name(),SocialChannel.FACEBOOK.getLabel());
                return commonService.createEnterpriseDoupErrorResponse(socialEnterpriseBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.DUPLICATE.name());
            } else if (!businessFBPageLite.getEnterpriseId().equals(enterpriseId)) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.ACCOUNT_ACCESS_ERR.name(),SocialChannel.FACEBOOK.getLabel());
                return commonService.createEnterpriseDoupErrorResponse(socialEnterpriseBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            } else if (socialFbRepo.existsByBusinessId(businessId)) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.BUSINESS_MAPPED_ERR.name(),SocialChannel.FACEBOOK.getLabel());
                return commonService.createEnterpriseDoupErrorResponse(socialEnterpriseBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            }

            businessFBPageLite.setBusinessId(businessId);
            businessFBPageLite.setEnterpriseId(enterpriseId);
            businessFBPageLite.setAccountId(accountId);

            socialFbRepo.saveAndFlush(businessFBPageLite);
            socialEnterpriseBulkStatus.setStatus(ResellerMappingStatusEnum.SUCCESS.name());
            socialEnterpriseBulkStatus.setErrorMessage("");
            kafkaProducer.sendObject(Constants.FB_PAGE_MAPPING_ADDED, new LocationPageMappingRequest(businessFBPageLite.getBusinessId(),businessFBPageLite.getFacebookPageId() ));
            fbPageService.performPostMappingAction(businessFBPageLite);
            commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(), Collections.singletonList(businessFBPageLite), null, businessFBPageLite.getBusinessId(), businessFBPageLite.getEnterpriseId());
            return socialEnterpriseBulkStatus;
        } catch (Exception e) {
            logger.error("ENTERPRISE_MAPPING_EVENT: FOR facebook, cannot ap location with reseller {} {}", enterpriseId, e.toString() );
            String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.UNKNOWN_ERR.name(),SocialChannel.FACEBOOK.getLabel());
            socialEnterpriseBulkStatus.setErrorMessage(errMessage);
            return commonService.createEnterpriseDoupErrorResponse(socialEnterpriseBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
        }
    }
    private void createSocialStreamForSmb(Integer pageId, String channel) {
        SocialStreamConnectRequest socialConnectPageRequest = new SocialStreamConnectRequest();
        socialConnectPageRequest.setPageId(pageId);
        socialConnectPageRequest.setStreamType(SocialStreams.StreamType.MY_POSTS.getType());
        socialConnectPageRequest.setChannel(channel);
        kafkaProducer.sendObjectV1(SOCIAL_STREAM_CONNECT,socialConnectPageRequest);
    }

    private SocialResellerBulkStatusDTO createFBDoupErrorResponse(SocialResellerBulkStatusDTO socialResellerBulkStatus, long eventId,
                                                                        String errMessage, String status) {
        socialResellerBulkStatus.setEventId(eventId);
        socialResellerBulkStatus.setStatus(status);
        socialResellerBulkStatus.setOperation(Constants.REJECTED);
        socialResellerBulkStatus.setErrorMessage(errMessage);
        return socialResellerBulkStatus;
    }

	@Override
	public List<Integer> getMappedLocations(List<Long> bizHierarchyList) {
        return socialFbRepo.findByEnterpriseIdInAndBusinessIdIsNotNull(bizHierarchyList);
	}

    @Override
    public List<Integer> getMappedRecordsByLocationId(List<Integer> locationsIds) {
        return socialFbRepo.findByBusinessIds(locationsIds);
    }

    @Override
    public SocialEnterpriseReportUploadDTO processEnterpriseMappingIntegrationReport(Long enterpriseId, Integer size, Integer page) {
        try {
            SocialEnterpriseReportUploadDTO unmappedFbPages = new SocialEnterpriseReportUploadDTO();
            Page<BusinessFBPageLite> unmappedBusinessFbPage = socialFbRepo.findByEnterpriseIdAndBusinessIdIsNull(enterpriseId, new PageRequest(page, size));
            if (Objects.isNull(unmappedBusinessFbPage.getContent()) ) {
                return null;
            }

            List<SocialEnterpriseReportUnmappedFacebookPages> socialResellerReportUploadFacebookDatum =
                    unmappedBusinessFbPage.getContent().stream().map(data -> {
                        SocialEnterpriseReportUnmappedFacebookPages conf = new SocialEnterpriseReportUnmappedFacebookPages();
                        conf.setFacebookPageId(data.getFacebookPageId());
                        conf.setFacebookPageAddress(data.getSingleLineAddress());
                        conf.setFacebookPageName(data.getFacebookPageName());
                        return conf;
                    } ).collect(Collectors.toList());

            unmappedFbPages.setData(socialResellerReportUploadFacebookDatum);
            unmappedFbPages.setPageCount(unmappedBusinessFbPage.getTotalPages());
            unmappedFbPages.setTotalCount(unmappedBusinessFbPage.getTotalElements());

            return unmappedFbPages;

        } catch (Exception e) {
            logger.error("something went wrong while fetching the location mapping data for resellerId {}, error", enterpriseId, e);
            return null;
        }
    }
}
