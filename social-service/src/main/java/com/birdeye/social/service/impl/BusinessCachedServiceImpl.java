/**
 *
 */
package com.birdeye.social.service.impl;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.birdeye.social.dto.*;
import com.birdeye.social.service.BusinessService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.BusinessTypeEnum;
import com.birdeye.social.dto.BusinessEntity;
import com.birdeye.social.dto.BusinessLiteUserDTO;
import com.birdeye.social.dto.WebsiteDomainInfo;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.platform.dao.BusinessUserRepository;
import com.birdeye.social.platform.dao.ResellerDomainRepository;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.platform.entities.WebsiteDomain;
import com.birdeye.social.service.IBusinessCachedService;

/**
 * Business Cache Service
 *
 * <AUTHOR>
 *
 *         22 Mar 2018
 **/

@Service("businessCachedService")
public class BusinessCachedServiceImpl implements IBusinessCachedService {

	private static final Logger LOGGER = LoggerFactory
			.getLogger(BusinessCachedServiceImpl.class);

	@Autowired
	private BusinessRepository businessRepo;

	@Autowired
	private ResellerDomainRepository resellerDomainRepository;

	@Autowired
	private BusinessUserRepository	businessUserRepository;

	@Autowired
	private IBusinessCoreService businessCoreService;

	@Autowired
	private BusinessService businessService;

	private static final String DEFAULT_DOMAIN = "birdeye.com";

	// 1 hour cache
	@Override
	@Cacheable(value = "businessCache", key = "'BID:'+#businessId", unless = "#result == null")
	public BusinessEntity getBusinessEntityById(Integer businessId) {
		List<BusinessEntity> results = businessRepo
				.getAllBusinessByBid(Arrays.asList(businessId));
		if (CollectionUtils.isNotEmpty(results)) {
			return results.get(0);
		}
		return null;
	}

	@Cacheable(value = "businessCache", key = "'domain:'+#businessId", unless = "#result == null")
	public WebsiteDomainInfo getBusinessDomain(Integer businessId) {
		WebsiteDomain domain = null;
		BusinessDomainDTO businessDomainDTO;
		Integer currentBusinessId = businessId;
		int i = 0;
		LOGGER.info("fetching WebsiteDomain info for businessId: {}",businessId);
		while (domain == null && currentBusinessId != null && i < 5) {
			LOGGER.info("currentBusinessId: {}",currentBusinessId);
			businessDomainDTO = businessService.getBusinessDomain(currentBusinessId);
			if (Objects.nonNull(businessDomainDTO) && !(businessDomainDTO.getDomain().equalsIgnoreCase(DEFAULT_DOMAIN)&&businessDomainDTO.getSecureEnabled()==0)) {
				domain = new WebsiteDomain();
				domain.setDomainName(businessDomainDTO.getDomain());
				domain.setSecureEnabled(businessDomainDTO.getSecureEnabled());
				break;
			}
			BusinessEntity business = getBusinessEntityById(currentBusinessId);
			if(Objects.nonNull(business)){
				currentBusinessId = business.getResellerId();
			}
			i++;
		}
		if (domain == null) {
			businessDomainDTO = businessService.getBusinessDomain(1);
			if (Objects.nonNull(businessDomainDTO)) {
				domain = new WebsiteDomain();
				domain.setDomainName(businessDomainDTO.getDomain());
				domain.setSecureEnabled(businessDomainDTO.getSecureEnabled());
			}
		}
		LOGGER.info("WebsiteDomain info for businessId: {}: {}",businessId,domain);
		WebsiteDomainInfo domainInfo = new WebsiteDomainInfo();
//		domainInfo.setId(domain.getId());
//		domainInfo.setIsDefault(domain.getIsDefault());
		domainInfo.setDomainName(domain.getDomainName());
//		domainInfo.setLogoURL(domain.getLogoURL());
		domainInfo.setSecureEnabled(domain.getSecureEnabled());
		return domainInfo;
	}

	public WebsiteDomainInfo getBusinessDomain(Business business) {
		WebsiteDomain domain = null;
		BusinessDomainDTO businessDomainDTO;

		int i = 0;
		LOGGER.info("fetching WebsiteDomain info for businessId: {}",business.getId());
		while (domain == null && business != null && i < 5) {
			businessDomainDTO = businessService.getBusinessDomain(business.getId());
			if (Objects.nonNull(businessDomainDTO) && !(businessDomainDTO.getDomain().equalsIgnoreCase(DEFAULT_DOMAIN)&&businessDomainDTO.getSecureEnabled()==0)) {
				domain = new WebsiteDomain();
				domain.setDomainName(businessDomainDTO.getDomain());
				domain.setSecureEnabled(businessDomainDTO.getSecureEnabled());
				break;
			}
			business = business.getReseller();
			i++;
		}
		if (domain == null) {
			LOGGER.info("Fetching default domain for business# {}", business.getId());
			businessDomainDTO = businessService.getBusinessDomain(1);
			if (Objects.nonNull(businessDomainDTO)) {
				domain = new WebsiteDomain();
				domain.setDomainName(businessDomainDTO.getDomain());
				domain.setSecureEnabled(businessDomainDTO.getSecureEnabled());
			}
		}
		LOGGER.info("WebsiteDomain info for businessId: {}: {}",business.getId(),domain);
		WebsiteDomainInfo domainInfo = new WebsiteDomainInfo();
//		domainInfo.setId(domain.getId());
//		domainInfo.setIsDefault(domain.getIsDefault());
		domainInfo.setDomainName(domain.getDomainName());
//		domainInfo.setLogoURL(domain.getLogoURL());
		domainInfo.setSecureEnabled(domain.getSecureEnabled());
		return domainInfo;
	}

	@Override
	public WebsiteDomainInfo getBusinessDomainV1(BusinessLiteDTO business) {
		WebsiteDomain domain = null;
		BusinessDomainDTO businessDomainDTO;

		int i = 0;
		LOGGER.info("fetching WebsiteDomain info for businessId: {}",business.getBusinessId());
		while (domain == null && business != null && i < 5) {
			businessDomainDTO = businessService.getBusinessDomain(business.getBusinessId());
			if (Objects.nonNull(businessDomainDTO) && !(businessDomainDTO.getDomain().equalsIgnoreCase(DEFAULT_DOMAIN)&&businessDomainDTO.getSecureEnabled()==0)) {
				domain = new WebsiteDomain();
				domain.setDomainName(businessDomainDTO.getDomain());
				domain.setSecureEnabled(businessDomainDTO.getSecureEnabled());
				break;
			}
			business = businessCoreService.getBusinessLite(business.getResellerId(),false);
			i++;
		}
		if (domain == null) {
			LOGGER.info("Fetching default domain for business# {}", business.getBusinessId());
			businessDomainDTO = businessService.getBusinessDomain(1);
			if (Objects.nonNull(businessDomainDTO)) {
				domain = new WebsiteDomain();
				domain.setDomainName(businessDomainDTO.getDomain());
				domain.setSecureEnabled(businessDomainDTO.getSecureEnabled());
			}
		}
		LOGGER.info("WebsiteDomain info for businessId: {}: {}",business.getBusinessId(),domain);
		WebsiteDomainInfo domainInfo = new WebsiteDomainInfo();
		domainInfo.setDomainName(domain.getDomainName());
		domainInfo.setSecureEnabled(domain.getSecureEnabled());
		return domainInfo;
	}

	public WebsiteDomainInfo getDefaultDomain(Integer businessId){
		LOGGER.info("Fetching default domain for business# {}", businessId);
		WebsiteDomain domain = null;
		BusinessDomainDTO businessDomainDTO;
		LOGGER.info("fetching WebsiteDomain info for businessId: {}",businessId);
		businessDomainDTO = businessService.getBusinessDomain(1);
		if (Objects.nonNull(businessDomainDTO)) {
			domain = new WebsiteDomain();
			domain.setDomainName(businessDomainDTO.getDomain());
			domain.setSecureEnabled(businessDomainDTO.getSecureEnabled());
		}
		LOGGER.info("WebsiteDomain info for businessId: {}: {}",businessId,domain);
		WebsiteDomainInfo domainInfo = new WebsiteDomainInfo();
//		domainInfo.setId(domain.getId());
//		domainInfo.setIsDefault(domain.getIsDefault());
		domainInfo.setDomainName(domain.getDomainName());
//		domainInfo.setLogoURL(domain.getLogoURL());
		domainInfo.setSecureEnabled(domain.getSecureEnabled());
		return domainInfo;
	}

	@Override
	public List<Integer> getLocationIdsForAccount(Integer entepriseId) {
		return businessRepo.findEnterpriseLocations(entepriseId);
	}

	//TODO can be cached once moved to core service
	@Override
	public Business getById(Integer id) {
		return businessRepo.findById(id);

	}

	@Override
	public boolean isSMB(BusinessEntity business) {
		return (business.getEnterpriseId() == null && (BusinessTypeEnum.BUSINESS.getName().equalsIgnoreCase(business.getType())
    			|| BusinessTypeEnum.PRODUCT.getName().equalsIgnoreCase(business.getType())));
	}

	@Override
	public List<String> getEnterpriseUsers(int enterpriseId) {
		List<String> users = null;

		try {

			BusinessLiteUserDTO businessLiteUserDTO = businessCoreService.getEnterpriseUsers(enterpriseId);

			if (CollectionUtils.isNotEmpty(businessLiteUserDTO.getUserDetailsList())) {
				 Pattern pattern = Pattern.compile("@(?!birdeye\\.com$)[a-z0-9.-]+\\.[a-z]{2,8}");

				users = businessLiteUserDTO.getUserDetailsList().stream()
						.filter(userDetail -> !StringUtils.isEmpty(userDetail.role)
								&& (userDetail.role.equals("OWNER") || userDetail.role.equals("ADMIN")))
						.map(userDetail -> userDetail.getEmailId()).filter(pattern.asPredicate()).distinct().collect(Collectors.toList());
            
			if (CollectionUtils.isEmpty(users)) {
				LOGGER.info("No Enterprise Non Birdeye Users(Owner/ Admin) for id {}", enterpriseId);
				return Collections.EMPTY_LIST;
			}
			LOGGER.info("Enterprise Users(Owner/ Admin) for id {} fetched {}", enterpriseId,users);
			}else {
				LOGGER.info(" No Enterprise Users for any role for id {}", enterpriseId);

			}


		} catch (Exception e) {
			LOGGER.info("Some error occured in fetching the users for businessId  {}",enterpriseId);

		}
		return users;

	}

	@Override
	public BusinessLiteDTO getBusinessLiteWithLocationRequired(Integer businessId) {
		BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(businessId,true);
		if (Objects.isNull(businessLiteDTO.getLocation())){
			businessLiteDTO=businessCoreService.getBusinessLiteWithUpdated(businessId);
		}
		return businessLiteDTO;
	}

}
