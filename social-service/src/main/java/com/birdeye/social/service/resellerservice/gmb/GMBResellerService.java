package com.birdeye.social.service.resellerservice.gmb;

import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.model.*;
import com.birdeye.social.service.GoogleSocialAccountService;
import com.birdeye.social.service.resellerservice.SocialReseller;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class GMBResellerService implements SocialReseller {

    @Autowired
    private GoogleSocialAccountService googleSocialAccountService;
    @Autowired
    private BusinessGMBLocationRawRepository socialGMBRepo;


    @Override
    public String channelName() {
        return SocialChannel.GMB.getName();
    }

    @Override
    public ChannelPageInfo connectResellerPages( List<String> pageIds, Long resellerId, Boolean selectAll, String searchStr) {
        return  googleSocialAccountService.connectGooglePagesForReseller(pageIds, resellerId,selectAll, searchStr);
    }

    @Override
    public ChannelConnectedPageInfo checkIfAccountExistsByResellerId(Long accountId) {
        ChannelConnectedPageInfo channelConnectedPageInfo= new ChannelConnectedPageInfo();
        channelConnectedPageInfo.setGmbLocationExists(socialGMBRepo.existsByResellerIdAndIsSelected(accountId,1));
        return channelConnectedPageInfo;
    }

    @Override
    public void reconnectResellerPages(Long resellerId, ChannelAllPageReconnectRequest request, Integer userId, String type, Integer limit) {
        googleSocialAccountService.reconnectGMBPagesEnhancedFlowForReseller(resellerId,request.getAccessToken(), request.getRedirectUri(), userId,type,limit);
    }

    @Override
    public void submitFetchPageRequest(ChannelAuthRequest authRequest, String type) {
      //separate call for this
    }

    @Override
    public Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer page, Integer size, String search) {
        return googleSocialAccountService.getPaginatedPages(businessGetPageRequest, page, size,search);
    }

    @Override
	public PaginatedConnectedPages getPages(Long resellerId, PageConnectionStatus pageConnectionStatus,
                                            Integer page, Integer size, String search, ResellerSearchType searchType,
                                            PageSortDirection sortDirection, ResellerSortType sortParam, List<Integer> locationIds,
                                            MappingStatus mappingStatus, List<Integer> userIds, Boolean locationFilterSelected, String type) {
        return googleSocialAccountService.getResellerPages(resellerId, pageConnectionStatus, page, size,search, searchType,
                sortDirection, sortParam, locationIds, mappingStatus, userIds, locationFilterSelected);

	}

	@Override
	public void removeResellerPages(List<String> pageIds, Integer limit) {
		googleSocialAccountService.removeGMBPageForReseller(pageIds, limit);
	}

	@Override
	public Map<String, Object> saveLocationPageMapping(String channel, Integer locationId, String pageId,String pageType
			,Integer userId, Boolean force, Long resellerId) {
		Map<String, Object> response = new HashMap<>();
		log.info("[Reseller page mapping] For channel {}  page Id {} mapping with location Id {}", channel, pageId, locationId);
		if (StringUtils.isEmpty(pageId) || locationId == null) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_PAGE_OR_LOCATION_ID, "page/location id is null");
		}
       
		googleSocialAccountService.saveGMBLocationMapping(locationId, pageId, userId,Constants.RESELLER, resellerId);
		return response;
	}

	@Override
	public void removePageMappings(List<LocationPageMappingRequest> input) throws Exception {
		googleSocialAccountService.removeGMBLocationPageMappings(input,Constants.RESELLER,false);
		
	}

    /**
     * @param resellerLeafLocationIds
     * @return
     */
    @Override
    public List<Integer> getMappedResellerLeafLocationIds(List<Integer> resellerLeafLocationIds) {
        return googleSocialAccountService.getMappedResellerLeafLocations(resellerLeafLocationIds);
    }

}
