package com.birdeye.social.service.whatsapp.arbor;

import com.birdeye.social.constant.MappingStatus;
import com.birdeye.social.constant.PageSortDirection;
import com.birdeye.social.constant.ResellerSearchType;
import com.birdeye.social.constant.ResellerSortType;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.model.ChannelAuthRequest;
import com.birdeye.social.model.PageConnectionStatus;
import com.birdeye.social.sro.ChannelPageInfo;
import com.birdeye.social.sro.LocationPageMappingRequest;
import com.birdeye.social.sro.PaginatedConnectedPages;
import com.birdeye.social.sro.TwitterConnectAccountRequest;
import java.util.List;
import java.util.Map;

public interface WhatsappSocialAccountService {

    void submitFetchPageRequest(ChannelAuthRequest authRequest, String accountType);

    ChannelPageInfo connectPage(TwitterConnectAccountRequest twitterConnectAccountRequest);

    PaginatedConnectedPages getPages(Long parentId, PageConnectionStatus pageConnectionStatus, Integer page,
                                     Integer size, String search, ResellerSearchType searchType, PageSortDirection sortDirection,
                                     ResellerSortType sortParam, List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds,
                                     Boolean locationFilterSelected, String type);

    Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer pageNumber, Integer pageSize, String search);

    void removePageMapping(List<LocationPageMappingRequest> locationPageMappingRequests, String type, boolean unlink);


    boolean existsByResellerIdAndIsSelected(Long resellerId, Integer isSelected);

    boolean checkIfAccountExistsByAccountId(Long accountId);
}
