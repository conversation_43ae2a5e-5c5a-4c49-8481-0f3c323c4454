package com.birdeye.social.service.SocialCompetitorService;

import com.birdeye.social.service.SocialEngageService.SocialEngageV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class CompetitorFactory {
    @Autowired
    List<SocialCompetitor> socialCompetitors;


    public Optional<SocialCompetitor> getSocialCompetitorChannel(String channel) {
        Map<String, SocialCompetitor> operationMap = new HashMap<>();
        for(SocialCompetitor socialCompetitor : socialCompetitors){
            operationMap.put(socialCompetitor.channelName(), socialCompetitor);
        }
        return Optional.ofNullable(operationMap.get(channel.toLowerCase()));
    }
}
