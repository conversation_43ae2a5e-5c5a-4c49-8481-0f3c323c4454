package com.birdeye.social.service;

import com.birdeye.social.AbstractSocialTagOperationService;
import com.birdeye.social.assetlibrary.SocialAssetLibraryService;
import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryAssetStatus;
import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryAssetType;
import com.birdeye.social.dao.*;
import com.birdeye.social.dao.aipost.BusinessAiCustomizationRepo;
import com.birdeye.social.dao.reports.BusinessPostsRepository;
import com.birdeye.social.dao.reports.SocialAINonUsefulPostsRepository;
import com.birdeye.social.dao.reports.SocialAIPostAssetsRepo;
import com.birdeye.social.dao.reports.SocialAIUsefulPostsRepository;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.AIGetPostsRequest;
import com.birdeye.social.dto.AIGetPostsResponse;
import com.birdeye.social.entities.*;
import com.birdeye.social.entities.aipost.BusinessAiCustomization;
import com.birdeye.social.entities.report.SocialAINonUsefulPosts;
import com.birdeye.social.entities.report.SocialAIPostAssets;
import com.birdeye.social.entities.report.SocialAIUsefulPosts;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.request.btprequest.SocialBTPRequest;
import com.birdeye.social.external.response.btp.SocialBTPCalenderResponse;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.factory.SocialTagOperationServiceFactory;
import com.birdeye.social.insights.constants.InsightsConstants;
import com.birdeye.social.model.MediaData;
import com.birdeye.social.model.ai_post.AiPostConfigDetailResponse;
import com.birdeye.social.model.es.SocialPostCalendarMessage;
import com.birdeye.social.notification.AIPostsNotificationService;
import com.birdeye.social.service.btp.BestTimeToPostService;
import com.birdeye.social.service.calendar.SocialPostCalendarESService;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.DateTimeUtils;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.KafkaTopicEnum.AI_POST_GENERATION_WEEKLY_NOTIFICATION;
import static com.birdeye.social.utils.DateTimeUtils.*;

@Service("SocialPostAIStreamService")
@Slf4j
public class SocialPostAIStreamServiceImpl implements SocialPostAIStreamService {

    @Autowired
    private SocialAIUsefulPostsRepository socialAIUsefulPostsRepository;

    @Autowired
    private SocialBusinessPropertyRepo socialBusinessPropertyRepo;

    @Autowired
    private BusinessAiCustomizationRepo businessAiCustomizationRepo;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private EsService esService;

    @Autowired
    private SocialCalendarEventRepo socialCalendarEventRepo;

    @Autowired
    private SocialPostAiService socialPostAiService;

    @Autowired
    private SocialTagService socialTagService;

    @Autowired
    private PostLibMasterRepo aiGeneratedPostsRepo;

    @Autowired
    private BestTimeToPostService bestTimeToPostService;

    @Autowired
    private AIPostsNotificationService aiPostsNotificationService;

    @Autowired
    private SocialBusinessPropertyService socialBusinessPropertyService;

    @Autowired
    private SocialPostCalendarESService socialPostCalendarESService;

    @Autowired
    private SocialAIPostAssetsRepo socialAIPostAssetsRepo;

    @Autowired
    private SocialPostRepository socialPostRepository;

    @Autowired
    private SocialPostsAssetsRepository socialPostsAssetsRepository;

    @Autowired
    private CommonService commonService;

    @Autowired
    private SocialAssetLibraryService socialAssetLibraryService;

    @Autowired
    private SocialAiPostHelperService socialAiPostHelperService;

    @Autowired
    private SocialAINonUsefulPostsRepository socialAINonUsefulPostsRepository;

    public void migrateBusinessPosts(AccountEvent event) throws IOException {
        log.info("Event received to migrate posts for business: {}", event.getAccountNumber());
        if(CollectionUtils.isEmpty(socialAIUsefulPostsRepository.findByEnterpriseId(event.getAccountNumber()))) {
            int startMonth = CacheManager.getInstance()
                    .getCache(SystemPropertiesCache.class)
                    .getOneTimeMigrationMonthsBackForStart();

            int endMonth = CacheManager.getInstance()
                    .getCache(SystemPropertiesCache.class)
                    .getOneTimeMigrationMonthsBackForEnd();

            Date minPublishDate = Date.from(LocalDate.now().minusMonths(startMonth).atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date maxPublishDate = Date.from(LocalDate.now().minusMonths(endMonth).atStartOfDay(ZoneId.systemDefault()).toInstant());
            publishBusinessPostsWithAI(minPublishDate, maxPublishDate, event.getAccountNumber());
        }
    }

    private void publishBusinessPostsWithAI(Date minPublishDate, Date maxPublishDate, Long accountNumber) throws IOException {
        BoolQueryBuilder boolQuery = createBoolQuery(minPublishDate, maxPublishDate, accountNumber);
        List<EsPostDataPoint> esPostDataPoints = executeSearch(boolQuery);

        sendEventForAssetLibraryImages(accountNumber, null, null, true);

        if (CollectionUtils.isNotEmpty(esPostDataPoints)) {
            processBusinessPosts(esPostDataPoints);
        }
    }

    private void processBusinessPosts(Date minPublishDate, Date maxPublishDate) throws IOException {

        BoolQueryBuilder boolQuery = createBoolQuery(minPublishDate, maxPublishDate, null);
        List<EsPostDataPoint> esPostDataPoints = executeSearch(boolQuery);

        sendEventForAssetLibraryImages(null, minPublishDate, maxPublishDate, false);
        if (CollectionUtils.isNotEmpty(esPostDataPoints)) {
            processBusinessPosts(esPostDataPoints);
        }

    }

    private BoolQueryBuilder createBoolQuery(Date minPublishDate, Date maxPublishDate, Long accountNumber) {
        List<Long> excludeTestAccounts=CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getTestAccountsBusinessNumbers();

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .filter(QueryBuilders.rangeQuery("posted_date")
                        .gte(DateTimeUtils.localToESFormat(minPublishDate))
                        .lte(DateTimeUtils.localToESFormat(maxPublishDate)))
                .filter(QueryBuilders.existsQuery("ent_id"))
//                .mustNot(QueryBuilders.termsQuery("ent_id", excludeTestAccounts))// Excluding prod test/automation accounts
                .should(QueryBuilders.existsQuery("post_content"))  // Either this
                .should(QueryBuilders.existsQuery("image_urls"))   // Or this. // done to accommodate post with only images
                .minimumShouldMatch(1);  // Require at least one "should" clause to match

        if (accountNumber != null) {
            boolQuery.filter(QueryBuilders.termQuery("ent_id", accountNumber));
        }
        return boolQuery;
    }

    private List<EsPostDataPoint> executeSearch(BoolQueryBuilder boolQuery) throws IOException {
        List<EsPostDataPoint> esPostDataPoints = new ArrayList<>();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQuery)
                .size(10000)
                .sort("posted_date", SortOrder.ASC)
                .sort("_id", SortOrder.ASC);

        SearchRequest searchRequest = new SearchRequest(InsightsConstants.POST_INSIGHT).source(sourceBuilder);

        Object[] searchAfter = null;
        boolean hasMoreResults = true;

        while (hasMoreResults) {
            if (searchAfter != null) {
                sourceBuilder.searchAfter(searchAfter);
            }

            SearchResponse response = esService.search(searchRequest);
            SearchHit[] hits = response.getHits().getHits();

            List<EsPostDataPoint> currentBatch = Arrays.stream(hits)
                    .map(hit -> JSONUtils.fromJSON(hit.getSourceAsString(), EsPostDataPoint.class))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            esPostDataPoints.addAll(currentBatch);

            if (hits.length > 0) {
                searchAfter = hits[hits.length - 1].getSortValues();
            } else {
                hasMoreResults = false;
            }
        }

        return esPostDataPoints;
    }

    @Override
    public void processSocialPostAIResponses(List<SocialPostAIStreamResponse> aiResponses) throws IOException {
        log.info("consume events from AI service and mark posts as useful/not useful");
        auditNonUsefulPosts(aiResponses);
        List<String> successfulPostIds = aiResponses.stream()
                .filter(response -> Objects.isNull(response.getError()) && response.getUseful())
                .map(SocialPostAIStreamResponse::getExternalPostId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<String> imageOnlyPosts = aiResponses.stream()
                .filter(response -> BooleanUtils.isTrue(response.getImageOnlyPost()))
                .map(SocialPostAIStreamResponse::getExternalPostId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<String> assetLibraryImageUrls = aiResponses.stream()
                .filter(response -> BooleanUtils.isTrue(response.getIsAssetImage()))
                .map(SocialPostAIStreamResponse::getImageUrl)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());


        if(CollectionUtils.isNotEmpty(imageOnlyPosts)) {
            successfulPostIds.addAll(imageOnlyPosts);
        }

        if(CollectionUtils.isNotEmpty(assetLibraryImageUrls)) {
            log.info("Processing asset library images with size: {}", assetLibraryImageUrls.size());
            List<SocialAIPostAssets> savedAssets = saveAssetLibraryImagesToSocialAIPostAsset(assetLibraryImageUrls);
            sendAIEventsForAssetLibraryImages(savedAssets, aiResponses.get(0).getAccountId());
        }

        processSuccessfulPosts(successfulPostIds);
    }

    @Transactional
    public void auditNonUsefulPosts(List<SocialPostAIStreamResponse> aiResponses) {
        log.info("Auditing non-useful posts...");

        // Filter non-useful posts based on the given conditions
        List<SocialPostAIStreamResponse> nonUsefulPosts = aiResponses.stream()
                .filter(response -> Boolean.FALSE.equals(response.getUseful()) &&
                        Boolean.FALSE.equals(response.getImageOnlyPost()) &&
                        Boolean.FALSE.equals(response.getIsAssetImage()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(nonUsefulPosts)) {
            log.info("No non-useful posts found to audit.");
            return;
        }

        // Map non-useful posts to the entity for saving
        List<SocialAINonUsefulPosts> nonUsefulPostEntities = nonUsefulPosts.stream()
                .map(response -> SocialAINonUsefulPosts.builder()
                        .accountId(response.getAccountId())
                        .externalPostId(response.getExternalPostId())
                        .createdAt(new Date())
                        .build())
                .collect(Collectors.toList());

        // Save non-useful posts to the database
        socialAINonUsefulPostsRepository.save(nonUsefulPostEntities);

        log.info("Successfully audited {} non-useful posts.", nonUsefulPostEntities.size());
    }

    private void sendAIEventsForAssetLibraryImages(List<SocialAIPostAssets> assets, Integer businessId) {
        if (CollectionUtils.isEmpty(assets)) {
            return;
        }

        int POSTS_BATCH_SIZE = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getAIPostMigrationBatchSize();

        int totalBatches = (assets.size() + POSTS_BATCH_SIZE - 1) / POSTS_BATCH_SIZE;
        log.info("Total batches for asset library images: {}", totalBatches);
        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            int start = batchIndex * POSTS_BATCH_SIZE;
            int end = Math.min(start + POSTS_BATCH_SIZE, assets.size());
            List<SocialAIPostAssets> currentBatch = assets.subList(start, end);
            SocialAIUsefulImagesRequest currentRequest = prepareUsefulImageDataForAssetLibrary(currentBatch, businessId);
            kafkaProducerService.sendObjectV1("social-ai-useful-images", currentRequest);
        }
    }

    private SocialAIUsefulImagesRequest prepareUsefulImageDataForAssetLibrary(List<SocialAIPostAssets> assets, Integer businessId) {

        Map<String, String> assetMap = assets.stream()
                .collect(Collectors.toMap(asset -> String.valueOf(asset.getId()), SocialAIPostAssets::getImageUrl));

        return SocialAIUsefulImagesRequest.builder()
                .accountId(businessId)
                .isAssetImage(true)
                .assetImageData(assetMap)
                .build();
    }

    private List<SocialAIPostAssets> saveAssetLibraryImagesToSocialAIPostAsset(List<String> assetLibraryImageUrls) {
        List<SocialAIPostAssets> socialAIPostAssets = assetLibraryImageUrls.stream()
                .map(imageUrl -> new SocialAIPostAssets(imageUrl))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(socialAIPostAssets)) {
            socialAIPostAssets = socialAIPostAssetsRepo.save(socialAIPostAssets);
            log.info("Saved {} asset library images to social AI post assets.", socialAIPostAssets.size());
            return socialAIPostAssets;
        }

        return Collections.emptyList();
    }

    private void processSuccessfulPosts(List<String> successfulPostIds) throws IOException {
        log.info("Processing successful posts...");

        if (CollectionUtils.isEmpty(successfulPostIds)) {
            log.info("[processSuccessfulPosts] No successful posts found.");
            return;
        } else {
            log.info("[processSuccessfulPosts] Found {} successful posts.", successfulPostIds.size());
        }

        List<EsPostDataPoint> esPostDataPoints = fetchPostsByPostIds(successfulPostIds);

        saveSocialAIPosts(esPostDataPoints, SocialPostAIStreamStatus.SUCCESS, false);
    }

    private void processRetryableResponses(List<SocialPostAIStreamResponse> aiResponses) throws IOException {
        // Filter retryable responses
        Map<String, SocialPostAIStreamResponse> retryablePostMap = aiResponses.stream()
                .filter(response -> Objects.nonNull(response.getError()) && response.getRetryCount() <= 3)
                .collect(Collectors.toMap(
                        SocialPostAIStreamResponse::getExternalPostId,
                        a -> a
                ));

        if (retryablePostMap.isEmpty()) {
            log.info("[processRetryableResponses] No retryable posts found.");
            return;
        }

        List<EsPostDataPoint> esPostDataPoints = fetchPostsByPostIds(new ArrayList<>(retryablePostMap.keySet()));
        if (CollectionUtils.isNotEmpty(esPostDataPoints)) {
            List<SocialPostAIStreamRequest> retryRequests = generateRetryPayloads(esPostDataPoints, retryablePostMap);
            kafkaProducerService.sendObjectV1("social-business-posts-ai-stream", retryRequests);
        }
    }

    private void handleExceededRetryLimitPosts(List<String> exceededRetryLimitPostIds) throws IOException {
        log.info("Handling posts that exceeded retry limit...");

        if (CollectionUtils.isEmpty(exceededRetryLimitPostIds)) {
            log.info("[handleExceededRetryLimitPosts] No posts found exceeding retry limit.");
            return;
        }

        List<EsPostDataPoint> esPostDataPoints = fetchPostsByPostIds(exceededRetryLimitPostIds);
        saveSocialAIPosts(esPostDataPoints, SocialPostAIStreamStatus.RETRY_LIMIT_EXCEEDED, false);
    }

    private List<EsPostDataPoint> fetchPostsByPostIds(List<String> externalPostIds) throws IOException {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termsQuery("post_id", externalPostIds))
                .filter(QueryBuilders.existsQuery("ent_id"));
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(boolQuery);
        sourceBuilder.size(externalPostIds.size());
        SearchResponse response = esService.search(new SearchRequest(InsightsConstants.POST_INSIGHT).source(sourceBuilder));
        return Arrays.stream(response.getHits().getHits())
                .map(hit -> JSONUtils.fromJSON(hit.getSourceAsString(), EsPostDataPoint.class))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private void saveSocialAIPosts(List<EsPostDataPoint> esPostDataPoints, SocialPostAIStreamStatus status, boolean contentPublished) {
        log.info("Saving Social AI posts with status: {}", status);

        if (CollectionUtils.isEmpty(esPostDataPoints)) {
            log.info("[saveSocialAIPosts] No business posts to save.");
            return;
        }

        List<SocialAIUsefulPosts> socialAIUsefulPosts = esPostDataPoints.stream()
                .filter(post -> Objects.nonNull(post.getEnt_id()))
                .map(post -> convertToSocialAIUsefulPosts(post, status, contentPublished))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(socialAIUsefulPosts)) {
            List<SocialAIUsefulPosts> toBeSavedUsefulPost = socialAIUsefulPosts.stream()
                    .filter(post -> Objects.nonNull(post) && Objects.nonNull(post.getPost_content()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(toBeSavedUsefulPost)) {
                log.info("[saveSocialAIPosts] Saving social AI posts with ids: {}", toBeSavedUsefulPost.stream().map(SocialAIUsefulPosts::getPostId).collect(Collectors.toList()));
                socialAIUsefulPostsRepository.save(toBeSavedUsefulPost);
            }
        } else {
            log.info("[saveSocialAIPosts] No social AI posts to save.");
        }


        if(SocialPostAIStreamStatus.SUCCESS.equals(status)) { //send events for useful images
            sendEventToAIForUsefulImages(socialAIUsefulPosts, esPostDataPoints.get(0).getBusiness_id());
        }

    }

    private void sendEventToAIForUsefulImages(List<SocialAIUsefulPosts> socialAIUsefulPosts, Integer businessId) {
        if (CollectionUtils.isEmpty(socialAIUsefulPosts)) {
            return;
        }

        List<SocialAIUsefulImagesRequest.SocialAIUsefulImageData> usefulImageData = prepareUsefulImageData(socialAIUsefulPosts);

        if(CollectionUtils.isNotEmpty(usefulImageData))
            sendEventInBatches(usefulImageData, businessId);
    }

    private void sendEventInBatches(List<SocialAIUsefulImagesRequest.SocialAIUsefulImageData> usefulImageData, Integer businessId) {
        if (CollectionUtils.isEmpty(usefulImageData)) {
            return;
        }
        int IMAGE_BATCH_SIZE = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getAIImageMigrationBatchSize();

        int totalBatches = (usefulImageData.size() + IMAGE_BATCH_SIZE - 1) / IMAGE_BATCH_SIZE;
        log.info("Total batches: {}", totalBatches);
        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            int start = batchIndex * IMAGE_BATCH_SIZE;
            int end = Math.min(start + IMAGE_BATCH_SIZE, usefulImageData.size());
            List<SocialAIUsefulImagesRequest.SocialAIUsefulImageData> list = usefulImageData.subList(start, end);
            SocialAIUsefulImagesRequest request = buildSocialAIUsefulImagesRequest(list, businessId);
            kafkaProducerService.sendObjectV1("social-ai-useful-images", request);
        }
    }

    private List<SocialAIUsefulImagesRequest.SocialAIUsefulImageData> prepareUsefulImageData(
            List<SocialAIUsefulPosts> socialAIUsefulPosts) {
        List<Integer> imageIds = extractAllImageIds(socialAIUsefulPosts);
        log.info("Extracted image ids: {}", imageIds);
        if(CollectionUtils.isEmpty(imageIds)) {
           return new ArrayList<>();
        }
        Map<Integer, String> idVsUrlMap = getAssetIdToImageUrlMap(imageIds);

        return socialAIUsefulPosts.stream()
                .filter(post -> CollectionUtils.isNotEmpty(post.getImageIds()))
                .map(post -> mapToUsefulImageData(post, idVsUrlMap))
                .collect(Collectors.toList());
    }

    private List<Integer> extractAllImageIds(List<SocialAIUsefulPosts> posts) {
        return posts.stream()
                .map(SocialAIUsefulPosts::getImageIds)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .map(Integer::valueOf)
                .collect(Collectors.toList());
    }

    private SocialAIUsefulImagesRequest.SocialAIUsefulImageData mapToUsefulImageData(
            SocialAIUsefulPosts post, Map<Integer, String> idVsUrlMap) {
        return SocialAIUsefulImagesRequest.SocialAIUsefulImageData.builder()
                .postId(post.getPostId())
//                .accountId(post.getBusinessId())
                .publishDate(parsePublishDate(post.getPublishDate()))
                .imageData(mapImageIdsToUrls(post.getImageIds(), idVsUrlMap))
                .build();
    }

    private String parsePublishDate(Date publishDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(publishDate);
    }

    private Date parsePublishDate(String publishDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return sdf.parse(publishDate);
        } catch (ParseException e) {
            log.error("Error parsing publish date: {}", publishDate, e);
            return null;
        }
    }

    private Map<String, String> mapImageIdsToUrls(List<String> imageIds, Map<Integer, String> idVsUrlMap) {
        if (CollectionUtils.isEmpty(imageIds)) {
            return null;
        }
        return imageIds.stream()
                .collect(Collectors.toMap(
                        Function.identity(),
                        imageId -> idVsUrlMap.get(Integer.valueOf(imageId))
                ));
    }

    private SocialAIUsefulImagesRequest buildSocialAIUsefulImagesRequest(
            List<SocialAIUsefulImagesRequest.SocialAIUsefulImageData> imageData, Integer businessId) {

        return SocialAIUsefulImagesRequest.builder()
                .postData(imageData)
                .isAssetImage(false)
                .accountId(businessId)
                .build();
    }

    private Map<Integer, String> getAssetIdToImageUrlMap(List<Integer> assetIds) {
        try {
            return socialAIPostAssetsRepo.findAssetIdAndImageUrlTuples(assetIds)
                    .stream()
                    .collect(Collectors.toMap(
                            tuple -> tuple.get(0, Integer.class),
                            tuple -> tuple.get(1, String.class),
                            (existing, replacement) -> existing
                    ));
        } catch(Exception e) {
            log.error("Error while fetching assetId to imageUrl map: {} {}", e.getMessage(), assetIds);
            return new HashMap<>();
        }
    }

    private SocialAIUsefulPosts convertToSocialAIUsefulPosts(EsPostDataPoint post, SocialPostAIStreamStatus status, boolean contentPublished) {
        Map<String, String> idVsUrlMap = saveImageUrlsToSocialDBs(post);
        if(idVsUrlMap.isEmpty()) {
            log.info("No image urls found for post: {}", post.getPost_id());
            return null;
        }
        return SocialAIUsefulPosts.builder()
                .bePostId(BooleanUtils.isTrue(post.getIs_be_post()) ? Integer.valueOf(post.getBe_post_id()) : null)
                .postId(post.getPost_id()) //externalPostId
                .post_content(post.getPost_content())
                .imageIds(Objects.nonNull(idVsUrlMap) ? new ArrayList<>(idVsUrlMap.keySet()) : null) // save imageIds from idVsUrlMap
                .isContentPublished(contentPublished)
                .publishDate(DateTimeUtils.getDateWithInputFormat(post.getPosted_date(), DateTimeUtils.YYYY_MM_DD_HH_MM_SS))
                .status(status)
                .enterpriseId(post.getEnt_id())
                .businessId(post.getBusiness_id())
                .sourceId(post.getSource_id())
                .build();
    }

    private List<SocialPostAIStreamRequest> generateRetryPayloads(List<EsPostDataPoint> businessPosts, Map<String, SocialPostAIStreamResponse> retryablePostMap) {
        return businessPosts.stream()
                .map(esPostDataPoint -> {
                    SocialPostAIStreamRequest socialPostAIStreamRequest = new SocialPostAIStreamRequest();
                    socialPostAIStreamRequest.setBusinessPostId(null);
                    socialPostAIStreamRequest.setBePostId(BooleanUtils.isTrue(esPostDataPoint.getIs_be_post()) ? Integer.valueOf(esPostDataPoint.getBe_post_id()) : null);
                    socialPostAIStreamRequest.setExternalPostId(esPostDataPoint.getPost_id());
                    socialPostAIStreamRequest.setPostContent(esPostDataPoint.getPost_content());
                    socialPostAIStreamRequest.setEnterpriseId(esPostDataPoint.getEnt_id());
                    socialPostAIStreamRequest.setPostedDate(DateTimeUtils.getDateWithInputFormat(esPostDataPoint.getPosted_date(), DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
                    socialPostAIStreamRequest.setBePost(esPostDataPoint.getIs_be_post());
                    socialPostAIStreamRequest.setRetryCount(retryablePostMap.get(esPostDataPoint.getPost_id()).getRetryCount() + 1);
                    return socialPostAIStreamRequest;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void processBusinessPosts(List<EsPostDataPoint> posts) throws IOException {

        removeReviewPosts(posts);

        int POSTS_BATCH_SIZE = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getAIPostMigrationBatchSize();

        List<SocialPostAIStreamRequest> allPosts = posts.stream()
                .map(this::safeGeneratePayload)
                .filter(Objects::nonNull)
                .filter(post -> Objects.nonNull(post.getEnterpriseId()))
                .filter(post -> Objects.nonNull(post.getPostContent()))
                .collect(Collectors.toList());

        int totalPosts = allPosts.size();
        int totalBatches = (totalPosts + POSTS_BATCH_SIZE - 1) / POSTS_BATCH_SIZE;

        publishProcessedPostsToKafka(totalBatches, POSTS_BATCH_SIZE, totalPosts, allPosts);

        sendEventForImageOnly(posts, POSTS_BATCH_SIZE);
    }

    private void sendEventForImageOnly(List<EsPostDataPoint> posts, Integer POSTS_BATCH_SIZE) {
        if(CollectionUtils.isEmpty(posts)) {
            return;
        }
        List<EsPostDataPoint> imageOnlyPosts = posts.stream()
                .filter(post -> Objects.nonNull(post.getImage_urls()) && post.getImage_urls().size() > 0 && (Objects.isNull(post.getPost_content()) || post.getPost_content().isEmpty()))
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(imageOnlyPosts)) {
            log.info("Sending image only posts: {}", imageOnlyPosts.size());
            List<SocialPostAIStreamResponse> request = imageOnlyPosts.stream()
                    .map(this::createImageOnlyRequest)
                    .collect(Collectors.toList());

            int totalBatches = (request.size() + POSTS_BATCH_SIZE - 1) / POSTS_BATCH_SIZE;
            log.info("Total batches for image only posts: {}", totalBatches);
            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                int start = batchIndex * POSTS_BATCH_SIZE;
                int end = Math.min(start + POSTS_BATCH_SIZE, request.size());
                List<SocialPostAIStreamResponse> currentRequest = request.subList(start, end);
                kafkaProducerService.sendObjectV1("social-ai-stream-image-only", currentRequest); // consumed by social only
            }
        }
    }

    private SocialPostAIStreamResponse createImageOnlyRequest(EsPostDataPoint post) {
       SocialPostAIStreamResponse request = new SocialPostAIStreamResponse();
       request.setExternalPostId(post.getPost_id());
       request.setImageOnlyPost(true);
       request.setUseful(false);
       return request;
    }

    private void publishProcessedPostsToKafka(int totalBatches, int POSTS_BATCH_SIZE, int totalPosts, List<SocialPostAIStreamRequest> allPosts) {
        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            int start = batchIndex * POSTS_BATCH_SIZE;
            int end = Math.min(start + POSTS_BATCH_SIZE, totalPosts);
            List<SocialPostAIStreamRequest> list = allPosts.subList(start, end);
            kafkaProducerService.sendObjectV1("social-business-posts-ai-stream", list);
        }
    }

    private PostDetailsDTO generatePayload(SocialAIUsefulPosts post) {
        return PostDetailsDTO.builder()
                .postText(post.getPost_content())
                .postId(post.getPostId())
                .bePostId(post.getBePostId())
                .enterpriseId(post.getEnterpriseId())
                .build();
    }

    private SocialPostAIStreamRequest safeGeneratePayload(EsPostDataPoint esPostDataPoint) {
        try {
            SocialPostAIStreamRequest socialPostAIStreamRequest = new SocialPostAIStreamRequest();
            socialPostAIStreamRequest.setBusinessPostId(null);
            socialPostAIStreamRequest.setBePostId(BooleanUtils.isTrue(esPostDataPoint.getIs_be_post()) ? Integer.valueOf(esPostDataPoint.getBe_post_id()) : null);
            socialPostAIStreamRequest.setExternalPostId(esPostDataPoint.getPost_id());
            socialPostAIStreamRequest.setPostContent(esPostDataPoint.getPost_content());
            socialPostAIStreamRequest.setEnterpriseId(esPostDataPoint.getEnt_id());
            socialPostAIStreamRequest.setPostedDate(DateTimeUtils.getDateWithInputFormat(esPostDataPoint.getPosted_date(), DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
            socialPostAIStreamRequest.setBePost(BooleanUtils.isTrue(esPostDataPoint.getIs_be_post()));
            socialPostAIStreamRequest.setRetryCount(0);
            return socialPostAIStreamRequest;
        } catch (Exception e) {
            log.error("Unexpected error occurred while processing postId: {}", esPostDataPoint.getPost_id(), e);
            return null;
        }
    }


    @Override
    public void sendBusinessPostDataToAIService() throws IOException {
        log.info("Event received to send recent posts to AI service to mark it useful/not useful");

        int startDays = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDailyPostDaysBackForStart();
        int endDays = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDailyPostDaysBackForEnd();

        Date minPublishDate = Date.from(LocalDate.now().minusDays(startDays).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date maxPublishDate = Date.from(LocalDate.now().minusDays(endDays).atStartOfDay(ZoneId.systemDefault()).toInstant());

        processBusinessPosts(minPublishDate, maxPublishDate);
    }


    @Override
    public void initiateSocialScan() {
        log.info("Monthly Job to publish all social enabled accounts for post generations");
        publishSocialEnabledAccounts(KafkaTopicEnum.AI_POST_GENERATION_PER_ACCOUNT.getName());
    }

    private void publishSocialEnabledAccounts(String topic) {
        int page = 0;
        int pageSize = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getAIPostGenerationBatchSize();

        List<Long> excludeTestAccounts = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getTestAccountsBusinessNumbers();
        try {
            SimpleDateFormat utcSimpleDateFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
            Page<Long> accountIdsPage = socialBusinessPropertyRepo
                    .findSocialEnabledAccountIds(formatTimeWithPattern(new Date(), Constants.UTC, utcSimpleDateFormat), excludeTestAccounts,new PageRequest(page, pageSize));

            if (CollectionUtils.isEmpty(accountIdsPage.getContent())) {
                log.info("No more accounts to process");
                return;
            }
            List<Long> accountNumbers = accountIdsPage.getContent();
            accountNumbers.forEach(a -> {
                AccountEvent event = new AccountEvent(a, false);
                kafkaProducerService.sendObjectV1(topic, event);
            });
            if(CollectionUtils.isNotEmpty(accountNumbers)){
                socialBusinessPropertyRepo.updatePostMigrationsProcessing(accountNumbers);
            }
        } catch (Exception e) {
            log.error("Unexpected error occurred while publishing account data", e);
        }
    }

    @Override
    public void publishAccountSettingToAI(AccountEvent event) {
        log.info("Event received to publish account ai custom setting data to AI service for account {}", event.getAccountNumber());
        BusinessLiteDTO businessLiteDTO = null;
        try{
            businessLiteDTO = businessCoreService.getBusinessLiteByNumber(event.getAccountNumber(), true);
            if(Objects.nonNull(businessLiteDTO.getBusinessNumber()) && Objects.nonNull(businessLiteDTO.getEnterpriseNumber()) && !Objects.equals(businessLiteDTO.getBusinessNumber(), businessLiteDTO.getEnterpriseNumber())){
                log.error("Ignoring post generation for businessNum {}: as business is an enterprise location", event.getAccountNumber());
                return;
            }
        } catch (Exception e){
            log.error("Error occurred while getBusinessLiteByNumber for account {}", event.getAccountNumber(), e);
        }
        if(Objects.nonNull(businessLiteDTO)) {
            BusinessProfileResponse businessProfileResponse = businessCoreService.getBusinessProfile(businessLiteDTO.getAccountId());
            AiPostConfigDetailResponse aiPostConfigDetailResponse = socialPostAiService.getBusinessAiConfig(businessLiteDTO.getAccountId(), businessLiteDTO.getBusinessNumber(), null);
            List<SocialBusinessProperty> socialBusinessPropertyList = socialBusinessPropertyRepo.findByEnterpriseId(event.getAccountNumber());
            if (CollectionUtils.isEmpty(socialBusinessPropertyList)) {
                log.error("No SocialBusinessProperty entry found for the given enterprise number: {}", event.getAccountNumber());
                throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, "internal server error");
            }
            SocialBusinessProperty socialBusinessProperty = socialBusinessPropertyList.get(0);
            SimpleDateFormat utcSimpleDateFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
            Date currentGenerationDate = Objects.nonNull(socialBusinessProperty.getNextScanDate()) ? socialBusinessProperty.getNextScanDate() : formatTimeWithPattern(new Date(), Constants.UTC, utcSimpleDateFormat);
            int days = AiPostConfigGenerationType.WEEKLY.equals(aiPostConfigDetailResponse.getGenerationFrequency()) ? 7 : 28;
            Date nextGenerationDate = DateUtils.addDays(currentGenerationDate, days);

            Date postGenStartDate = DateUtils.addDays(currentGenerationDate, 7); // always generate post for a week
            int daysToAdd = AiPostConfigGenerationType.WEEKLY.equals(aiPostConfigDetailResponse.getGenerationFrequency()) ? 6 : 27;
            Date postGenEndDate = DateUtils.addDays(postGenStartDate, daysToAdd);
            setDatesToExtremeOfTime(postGenStartDate, postGenEndDate);
            Integer numberOfPosts = getNumberOfPost(aiPostConfigDetailResponse);
            if (Objects.nonNull(businessLiteDTO.getBusinessId()) && Objects.nonNull(businessProfileResponse)) {
                String countryCode = (Objects.nonNull(businessLiteDTO.getLocation()) && Objects.nonNull(businessLiteDTO.getLocation().getCountryCode())) ? businessLiteDTO.getLocation().getCountryCode() : "US";
                SocialAIPostGenerationRequest socialAIPostGenerationRequest = SocialAIPostGenerationRequest.builder()
                        .enterpriseId(businessLiteDTO.getBusinessNumber())
                        .accountId(businessLiteDTO.getBusinessId())
                        .categories(aiPostConfigDetailResponse.getCategories())
                        .numberOfPosts(numberOfPosts)
                        .category(businessProfileResponse.getCategory())
                        .subCategories(businessProfileResponse.getSubCategories())
                        .services(businessProfileResponse.getServices())
                        .keywords(businessProfileResponse.getKeywords())
                        .products(businessProfileResponse.getProducts())
                        .description(businessProfileResponse.getDescription())
                        .holidays(getHolidays(postGenStartDate, postGenEndDate, countryCode))
                        .country((Objects.nonNull(businessLiteDTO.getLocation()) && Objects.nonNull(businessLiteDTO.getLocation().getCountryName())) ? businessLiteDTO.getLocation().getCountryName() : "United State")
                        .eventPublishedAt(new Date().getTime())
                        .name(StringUtils.isNotBlank(businessLiteDTO.getBusinessAlias()) ? businessLiteDTO.getBusinessAlias() : businessLiteDTO.getBusinessName())
                        .build();
                socialBusinessProperty.setCurrentScanDate(currentGenerationDate);
                socialBusinessProperty.setNextScanDate(nextGenerationDate);
                socialBusinessProperty.setPicked(true);
                socialBusinessPropertyRepo.save(socialBusinessProperty);
                socialBusinessPropertyService.evictCache(event.getAccountNumber());
                kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_POST_GENERATION_REQUEST.getName(), socialAIPostGenerationRequest);
            } else {
                log.info("Error in fetching account info from core for account number: {} ", event.getAccountNumber());
            }
        }else {
            List<SocialBusinessProperty> socialBusinessPropertyList = socialBusinessPropertyRepo.findByEnterpriseId(event.getAccountNumber());
            SimpleDateFormat utcSimpleDateFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
            if (CollectionUtils.isNotEmpty(socialBusinessPropertyList)) {
                socialBusinessPropertyList.forEach(socialBusinessProperty -> {
                    socialBusinessProperty.setCurrentScanDate(formatTimeWithPattern(new Date(), Constants.UTC, utcSimpleDateFormat));
                    socialBusinessProperty.setNextScanDate(DateUtils.addDays(socialBusinessProperty.getCurrentScanDate(),7));
                    socialBusinessProperty.setPicked(false);
                    socialBusinessPropertyRepo.save(socialBusinessProperty);
                    socialBusinessPropertyService.evictCache(event.getAccountNumber());
                });
            }
        }
    }

    @Override
    public AIGetPostsResponse getAIPosts(AIGetPostsRequest aiGetPostsRequest) throws IOException {
        log.info("Event received to get AI posts, Payload {}", aiGetPostsRequest);
        Integer engagementCountThreshold = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getAiPostEngagementCountThreshold();

        AIGetPostsResponse response = new AIGetPostsResponse();
        Long enterpriseId = aiGetPostsRequest.getEnterpriseId();
        Date minPublishDate = Date.from(LocalDate.now().minusMonths(4).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date maxPublishDate = Date.from(LocalDate.now().minusMonths(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        List<SocialAIUsefulPosts> socialBEAIUsefulPosts = socialAIUsefulPostsRepository.findBeAIUsefulPosts(minPublishDate, maxPublishDate, null, enterpriseId);
        if(CollectionUtils.isEmpty(socialBEAIUsefulPosts)) {
            log.info("No Social AI Useful Posts found for date range {} {} and enterpriseId {}", minPublishDate, maxPublishDate, enterpriseId);
            return response;
        }
        Map<Boolean, List<PostDetailsDTO>> partitionedPosts = socialBEAIUsefulPosts.stream()
                .map(this::generatePayload)
                .filter(Objects::nonNull)
                .collect(Collectors.partitioningBy(payload -> Objects.nonNull(payload.getBePostId())));

        List<PostDetailsDTO> bePostList = partitionedPosts.get(true);
        List<PostDetailsDTO> externalPostList = partitionedPosts.get(false);

        // Fetch and update engagements for external posts
        Map<String, Double> externalPostEngagements = socialAiPostHelperService.fetchExternalPostEngagements(externalPostList, "engagement");
        List<PostDetailsDTO> updatedExternalPosts = externalPostList.stream()
                .filter(post -> Objects.nonNull(post.getPostId()))
                .peek(post -> post.setEngagement(Objects.nonNull(externalPostEngagements.get(post.getPostId()))?externalPostEngagements.get(post.getPostId()):null))
                .filter(post -> Objects.nonNull(post.getEngagement()))
                .filter(post -> post.getEngagement() >= engagementCountThreshold)
                .collect(Collectors.toList());

        // Fetch and update engagements for BE posts
        Map<Integer, Double> bePostEngagements = socialAiPostHelperService.fetchBePostEngagements(bePostList, "engagement");
        List<PostDetailsDTO> updatedBePosts = new ArrayList<>(bePostList.stream()
                .filter(post -> Objects.nonNull(post.getBePostId()))
                .peek(post -> post.setEngagement(Objects.nonNull(bePostEngagements.get(post.getBePostId())) ? bePostEngagements.get(post.getBePostId()) : null)) // Update engagement
                .filter(post -> Objects.nonNull(post.getEngagement()))
                .filter(post -> post.getEngagement() >= engagementCountThreshold)
                .collect(Collectors.toMap(
                        PostDetailsDTO::getBePostId,
                        Function.identity(),
                        (existing, replacement) -> existing)) // Keep first occurrence for duplicates
                .values());

        List<PostDetailsDTO> allPosts = new ArrayList<>();
        allPosts.addAll(updatedExternalPosts);
        allPosts.addAll(updatedBePosts);
        List<PostDetailsDTO> finalList = allPosts.stream()
                .filter(post -> post.getEngagement() != null)
                .sorted(Comparator.comparing(PostDetailsDTO::getEngagement).reversed()) //sort posts by engagement in descending order
                .limit(aiGetPostsRequest.getNumberOfPosts())
                .collect(Collectors.toList());
        response.setPostDetailsDTOS(finalList);
        return response;
    }

    @Override
    @Transactional
    public void saveAIGeneratedPosts(AIGeneratedPostsResponse aiGeneratedPostsResponse) throws IOException {
        log.info("Event received to save AI generated posts, Payload {}", aiGeneratedPostsResponse);
        if (CollectionUtils.isEmpty(aiGeneratedPostsResponse.getAiGeneratedPosts())) {
            log.info("Request is empty, Hence returning");
            return;
        }
        Long enterpriseId = aiGeneratedPostsResponse.getAiGeneratedPosts().get(0).getEnterpriseId();
        log.info("Saving AI posts for account: {}", enterpriseId);
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLiteByNumber(enterpriseId, true);

        //create all tags and get map of unique tags
        Map<String, Long> tagMap = processTags(aiGeneratedPostsResponse.getAiGeneratedPosts(), businessLiteDTO);

        // build post lib master objects
        Map<String, PostLibMaster> aiGeneratedPostMap = buildAiGeneratedPosts(aiGeneratedPostsResponse, businessLiteDTO);

        AiPostConfigDetailResponse aiPostConfigDetailResponse = socialPostAiService.getBusinessAiConfig(businessLiteDTO.getAccountId(), businessLiteDTO.getBusinessNumber(), null);
        List<SocialBusinessProperty> socialBusinessPropertyList = socialBusinessPropertyRepo.findByEnterpriseId(businessLiteDTO.getEnterpriseNumber());
        if (CollectionUtils.isEmpty(socialBusinessPropertyList)) {
            log.error("No SocialBusinessProperty entry found for the given enterprise number: {}", businessLiteDTO.getEnterpriseNumber());
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, "internal server error");
        }
        log.info("socialBusinessPropertyList received is {}", socialBusinessPropertyList);
        SocialBusinessProperty socialBusinessProperty = socialBusinessPropertyList.get(0);
        Date postGenStartDate;
        if(socialBusinessProperty.getCurrentScanDate() != null) {
            postGenStartDate = DateUtils.addDays(socialBusinessProperty.getCurrentScanDate(), 7);
        } else { // if current scan date is null, set post gen start date to current date + 7
            SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
            postGenStartDate = DateUtils.addDays(formatTimeWithPattern(new Date(), Constants.UTC, sdf), 7);
        }

        int daysToAdd = AiPostConfigGenerationType.WEEKLY.equals(aiPostConfigDetailResponse.getGenerationFrequency()) ? 6 : 27;
        Date postGenEndDate = DateUtils.addDays(postGenStartDate, daysToAdd);

        String countryCode = (Objects.nonNull(businessLiteDTO.getLocation()) && Objects.nonNull(businessLiteDTO.getLocation().getCountryCode())) ? businessLiteDTO.getLocation().getCountryCode() : "US";
        List<SocialAIPostGenerationRequest.Holiday> holidays = new ArrayList<>();
        if (Objects.nonNull(countryCode)) {
            holidays = getHolidays(postGenStartDate, postGenEndDate, countryCode);
        }
        aiGeneratedPostMap = addBTPTimeInPosts(businessLiteDTO, aiGeneratedPostMap, postGenStartDate, postGenEndDate, holidays);
        List<PostLibMaster> savedPosts = saveAiGeneratedPosts(aiGeneratedPostMap, tagMap);
        sendEventToUpdatePexelsImages(savedPosts);
        updateReferencePostUsageCount(savedPosts);
        mapTagsToEntities(savedPosts, tagMap, businessLiteDTO);
        socialBusinessProperty.setPicked(false);
        socialBusinessPropertyRepo.save(socialBusinessProperty);
        socialPostAiService.updateLastFrequencyType(businessLiteDTO.getBusinessNumber());
        AccountEvent event = new AccountEvent(businessLiteDTO.getBusinessNumber(),false);
        kafkaProducerService.sendObjectV1(KafkaTopicEnum.AI_POST_GENERATION_WEEKLY_NOTIFICATION.getName(), event);
    }

    public void sendEventToUpdatePexelsImages(List<PostLibMaster> posts) {
        log.info("Sending event to update pexels images for posts {}", posts);
        for (PostLibMaster savedPost : posts) {
            if("pexels".equalsIgnoreCase(savedPost.getImageSource()) || StringUtils.isNotEmpty(savedPost.getPexelsImageIds())){
                kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_UPDATE_WITH_OPTIMIZED_IMG.getName(), savedPost.getId());
            }
        }
    }
    @Override
    public PexelsImageResponse fetchPexelsImages(String searchText, int count) {
        log.info("Fetching Pexels images for searchText: {} with count: {}", searchText, count);
        try {
            PexelsImageResponse response = new PexelsImageResponse();
            response.setPexelUrls(commonService.getImagesFromPexels(searchText, count));
            return response;
        } catch (UnsupportedEncodingException e) {
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, "Error fetching Pexels images", e);
        }
    }
    private void updateReferencePostUsageCount(List<PostLibMaster> savedPosts) {
        if(CollectionUtils.isNotEmpty(savedPosts)) {
            List<String> postIds = savedPosts.stream()
                    .map(PostLibMaster::getPostId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(postIds)) {
                log.info("No top performing posts found for postIds: {}", postIds);
                return;
            }

            log.info("Updating referencePostUsageCount for postIds: {}", postIds);
            List<SocialAIUsefulPosts> usefulPost = socialAIUsefulPostsRepository.findByPostIdIn(postIds);
            if(CollectionUtils.isNotEmpty(usefulPost)) {
                usefulPost.stream().forEach(post -> {
                    post.setReferencePostUsageCount(post.getReferencePostUsageCount() + 1);
                });
            }
            socialAIUsefulPostsRepository.save(usefulPost);
        }
    }

    /*
        1. Get data from PostLibMaster and Calendar ES
        2. Get imageUrls from AI_POST_ASSETS using imageIds from PostLibMaster
        2. Save Pexels images in pictures queue
        3. Replace imageUrls in AI_POST_ASSETS with the new URLs
        4. Replace imageUrls and mediaSequence in calendar ES with the new URLs
     */
    @Override
    public void updatePexelsImagesWithOptimizedImg(Integer postLibMasterId) {
        log.info("Event received to save Pexels images in pictures queue, id: {}", postLibMasterId);

        if (postLibMasterId == null) {
            log.info("Request is invalid. Returning.");
            return;
        }

        PostLibMaster postLibMaster = aiGeneratedPostsRepo.findById(postLibMasterId);
        if (postLibMaster == null || StringUtils.isEmpty(postLibMaster.getPublicImageIds())) {
            log.info("PostLibMaster not found or has no public image IDs. ID: {}", postLibMasterId);
            return;
        }
        List<Integer> imageIds = parseImageIds(postLibMaster.getPublicImageIds());
        List<Integer> compressedImageIds = parseImageIds(postLibMaster.getPublicCompressedImageIds());
        List<Integer> allAssetIds = new ArrayList<>(imageIds);
        allAssetIds.addAll(compressedImageIds);
        List<SocialAIPostAssets> socialAIPostAssets = socialAIPostAssetsRepo.findByIdIn(allAssetIds);
        if (CollectionUtils.isEmpty(socialAIPostAssets)) {
            log.info("No SocialAIPostAssets found for imageIds: {}", imageIds);
            return;
        }
        List<SocialAIPostAssets> imageAssets = socialAIPostAssets.stream()
                .filter(asset -> imageIds.contains(asset.getId()))
                .collect(Collectors.toList());
        List<SocialAIPostAssets> compressedImageAssets = socialAIPostAssets.stream()
                .filter(asset -> compressedImageIds.contains(asset.getId()))
                .collect(Collectors.toList());

        updateAssetUrlsWithOptimizedS3Urls(imageAssets, compressedImageAssets, postLibMaster.getEnterpriseId());
        socialAIPostAssetsRepo.save(socialAIPostAssets);

        updateCalendarWithOptimizedImages(postLibMaster.getId(), imageAssets, compressedImageAssets);
    }

    private List<Integer> parseImageIds(String imageIdsStr) {
        return Arrays.stream(imageIdsStr.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotEmpty)
                .map(Integer::valueOf)
                .collect(Collectors.toList());
    }

    private void updateAssetUrlsWithOptimizedS3Urls(List<SocialAIPostAssets> imageAssets,
                                                    List<SocialAIPostAssets> compressedAssets,
                                                    Integer enterpriseId) {
        // Both list will be of same size
        for (int i=0; i<imageAssets.size() && i<compressedAssets.size();i++) {
            try {
                PicturesqueCompressedMediaResponse response = commonService.generateOptimizedImage(imageAssets.get(i).getImageUrl(), enterpriseId);
                if (Objects.nonNull(response)) {
                    if(StringUtils.isNotEmpty(response.getMidSizeUrl())) {
                        imageAssets.get(i).setImageUrl(response.getMidSizeUrl());
                    }
                    if(StringUtils.isNotEmpty(response.getSmallUrl())) {
                        compressedAssets.get(i).setImageUrl(response.getSmallUrl());
                    }
                }
            } catch (Exception e) {
                log.error("Error optimizing image for assetId {}: {}", imageAssets.get(i).getId(), e.getMessage());
            }
        }
    }

    private void updateCalendarWithOptimizedImages(Integer aiPostId, List<SocialAIPostAssets> imageAssets,
                                                   List<SocialAIPostAssets> compressedImageAssets) {
        SocialPostCalendarMessage calendarMessage = socialPostCalendarESService.searchFromEsIndexByAiPostId(aiPostId);
        if (calendarMessage == null) {
            log.info("No SocialPostCalendarMessage found for aiPostId: {}", aiPostId);
            return;
        }
        List<String> imageUrls = imageAssets.stream()
                .map(SocialAIPostAssets::getImageUrl)
                .collect(Collectors.toList());

        List<MediaData> mediaList = imageAssets.stream()
                .map(asset -> new MediaData(asset.getImageUrl(), null))
                .collect(Collectors.toList());

        List<String> compressedUrls = compressedImageAssets.stream()
                .map(SocialAIPostAssets::getImageUrl)
                .collect(Collectors.toList());

        calendarMessage.setMediaSequence(imageUrls);
        calendarMessage.setImages(mediaList);
        calendarMessage.setCompressedMediaSequence(compressedUrls);
        calendarMessage.setRecordUpdatedDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

        socialPostCalendarESService.save(Collections.singletonList(calendarMessage));
        log.info("Updated SocialPostCalendarMessage with optimized images for aiPostId: {}", aiPostId);
    }


    @Override
    public void publishNotificationEvent() {
        log.info("publishing event for all social enabled accounts for email notification");
        int page = 0;
        int pageSize = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getAIPostsEmailBatchSize();
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -7);
            Date sevenDaysAgo = calendar.getTime();
            Page<Long> accountIdsPage = businessAiCustomizationRepo
                    .findAccountIdsForReminderEmailNotification(sevenDaysAgo, new PageRequest(page, pageSize));

            if (CollectionUtils.isEmpty(accountIdsPage.getContent())) {
                log.info("No more accounts to process");
                return;
            }

            List<Long> accountNumbers = accountIdsPage.getContent();
            accountNumbers.stream().forEach(a -> {
                AccountEvent event = new AccountEvent(a, true);
                kafkaProducerService.sendObjectV1(AI_POST_GENERATION_WEEKLY_NOTIFICATION.getName(), event);
            });
            if (CollectionUtils.isNotEmpty(accountNumbers)) {
                businessAiCustomizationRepo.updateReminderEmailProcessing(accountNumbers);
            }
        } catch (Exception e) {
            log.error("Unexpected error occurred while publishing account data", e);
        }
    }

    @Override
    public void sendAIPostNotification(AccountEvent event) {
        log.info("Event received to trigger email notification for business: {}", event.getAccountNumber());
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLiteByNumber(event.getAccountNumber(), false);

        if (businessLiteDTO == null || businessLiteDTO.getBusinessId() == null) {
            log.info("Error in fetching account info from core for account number: {}", event.getAccountNumber());
            return;
        }

        AiPostConfigDetailResponse aiPostConfigDetailResponse = socialPostAiService.getBusinessAiConfig(
                businessLiteDTO.getAccountId(), businessLiteDTO.getBusinessNumber(), null);

        if (aiPostConfigDetailResponse == null) {
            log.info("AI Post configuration not found for account number: {}", event.getAccountNumber());
            return;
        }

        List<SocialBusinessProperty> socialBusinessPropertyList = socialBusinessPropertyRepo.findByEnterpriseId(event.getAccountNumber());
        if (CollectionUtils.isEmpty(socialBusinessPropertyList)) {
            log.error("No SocialBusinessProperty entry found for the given enterprise number: {}", event.getAccountNumber());
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, "internal server error");
        }
        SocialBusinessProperty socialBusinessProperty = socialBusinessPropertyList.get(0);

        processAndSendNotifications(socialBusinessProperty, aiPostConfigDetailResponse, businessLiteDTO,event.isReminderEmail());
    }

    @Override
    public void publishAccountSettingsUpdate(AIPostSettingChangeRequest settingChangeRequest, Integer accountId) {
        log.info("Event received to update AI posts custom setting for business: {}, payload:{}", accountId, settingChangeRequest);
        if (Objects.nonNull(settingChangeRequest) && Objects.nonNull(settingChangeRequest.getNewConfig()) && Objects.nonNull(settingChangeRequest.getOldConfig())) {
            AIPostSettingChangeRequest.AIPostConfig oldConfig = settingChangeRequest.getOldConfig();
            AIPostSettingChangeRequest.AIPostConfig newConfig = settingChangeRequest.getNewConfig();

            if (oldConfig.getGenerationFrequencyType() == 2) {
                log.info("Post generation frequency is set to monthly for accountId {}, settings will take effect in the next job cycle", oldConfig.getAccountId());
                return;
            }

            processSettingsUpdate(accountId, newConfig);
        } else {
            processSettingsUpdate(accountId, null);
        }
    }

    @Override
    public void accountSettingsReset(Integer accountId, boolean resetAll, Boolean resetAllImages) {
        log.info("Event received to reset AI posts custom setting for business: {}", accountId);
        List<PostLibMaster> toBeDeleted = aiGeneratedPostsRepo.findByAiSuggestedAndEnterpriseId(1,accountId);
        aiGeneratedPostsRepo.delete(toBeDeleted);
        toBeDeleted.forEach(toBeDeletedPost -> socialPostCalendarESService.deleteRecord(toBeDeletedPost.getId(), null));
        log.info("Deleted {} AI generated posts for account: {}", toBeDeleted.size(), accountId);
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(accountId, true);
        List<SocialBusinessProperty> toBeUpdatedProperty = socialBusinessPropertyRepo.findByEnterpriseId(businessLiteDTO.getBusinessNumber());
        toBeUpdatedProperty.forEach(socialBusinessProperty -> {
            socialBusinessProperty.setCurrentScanDate(null);
            socialBusinessProperty.setNextScanDate(null);
            socialBusinessPropertyRepo.save(socialBusinessProperty);
        });
        log.info("Updated SocialBusinessProperty for accountId: {}", accountId);
        BusinessAiCustomization toBeUpdatedConfig = businessAiCustomizationRepo.findByAccountId(accountId);
        if (Objects.nonNull(toBeUpdatedConfig)) {
            toBeUpdatedConfig.setLastFrequencyType(1);
            toBeUpdatedConfig.setGenerationFrequencyType(1);
            businessAiCustomizationRepo.save(toBeUpdatedConfig);
        }
        if(resetAll) {
            log.info("Resetting all useful posts for account: {}", accountId);
            List<SocialAIUsefulPosts> toBeDeletedUsefulPost = socialAIUsefulPostsRepository.findByEnterpriseId(businessLiteDTO.getBusinessNumber());
            socialAIUsefulPostsRepository.delete(toBeDeletedUsefulPost);

        }
        if(resetAllImages) {
            log.info("Resetting all images");
            socialAIPostAssetsRepo.deleteAll();
        }
        log.info("Updated BusinessAiCustomization for accountId: {}", accountId);
    }

    @Override
    public void publishedSocialEnabledAccountsData() {
        int page = 0;
        int pageSize = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getPostsMigrationAccountBatchSize();
        List<Long> excludeTestAccounts = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getTestAccountsBusinessNumbers();
        try {
                Page<Long> accountIdsPage = socialBusinessPropertyRepo
                        .findSocialEnabledAccountIds(excludeTestAccounts, new PageRequest(page, pageSize));

                if (CollectionUtils.isEmpty(accountIdsPage.getContent())) {
                    log.info("No more accounts to process");
                    return;
                }

                List<Long> accountNumbers = accountIdsPage.getContent();
                accountNumbers.stream().forEach(a -> {
                    AccountEvent event = new AccountEvent(a,false);
                    kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_AI_POST_MIGRATION.getName(), event);
                });
                socialBusinessPropertyRepo.updatePostMigrationsProcessing(accountNumbers);

        } catch (Exception e) {
            log.error("Unexpected error occurred while publishing account data", e);
        }
    }
    private void processSettingsUpdate(Integer accountId, AIPostSettingChangeRequest.AIPostConfig newConfig) {
        Date currentDate = new Date();
        PostLibMaster latestPost = fetchLatestGeneratedPost(accountId);

        Date nextWeekStartDate = getNextWeekStartDate(currentDate);
        Date postGenerationWeekStartDate = getNextWeekStartDate(latestPost.getCreatedDate());
        Date postGenerationEndDate = DateTimeUtils.getEndOfDay(DateUtils.addDays(postGenerationWeekStartDate, 28));

        Long daysUntilNextPost = DateTimeUtils.getDifferenceDays(nextWeekStartDate, postGenerationEndDate);
        if (daysUntilNextPost > 0) {
            int weeks = (int) Math.ceil(daysUntilNextPost / 7.0);
            BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(accountId, true);
            BusinessProfileResponse businessProfileResponse = businessCoreService.getBusinessProfile(businessLiteDTO.getAccountId());
            AiPostConfigDetailResponse configDetail = null;
            if (Objects.isNull(newConfig)) {
                configDetail = socialPostAiService.getBusinessAiConfig(businessLiteDTO.getAccountId(), businessLiteDTO.getBusinessNumber(), null);
            }

            if (Objects.nonNull(businessLiteDTO.getBusinessId()) && Objects.nonNull(businessProfileResponse)) {
                Integer numberOfPosts = calculateNumberOfPosts(newConfig, weeks, configDetail);

                String countryCode = (Objects.nonNull(businessLiteDTO.getLocation()) && Objects.nonNull(businessLiteDTO.getLocation().getCountryCode())) ? businessLiteDTO.getLocation().getCountryCode() : "US";
                SocialAIPostGenerationRequest postGenerationRequest = buildPostGenerationRequest(
                        businessLiteDTO, businessProfileResponse, newConfig, numberOfPosts, countryCode, latestPost, configDetail, nextWeekStartDate, postGenerationEndDate);

                kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_POST_GENERATION_REQUEST.getName(), postGenerationRequest);
            }
        }
    }

    @Override
    public void saveUsefulImages(List<SocialAIUsefulImagesResponse.SocialAIUsefulImageData> socialAIUsefulImageData, Integer businessId) {
        log.info("Received request to update useful images: {}", socialAIUsefulImageData);

        Map<Boolean, Map<Integer, SocialAIUsefulImagesResponse.SocialAIUsefulImageData>> partitionedImageIds = socialAIUsefulImageData.stream()
                .filter(imageData -> !imageData.getIsAssetImage())
                .collect(Collectors.partitioningBy(
                        imageData -> imageData.getIsUseful() != null &&
                                imageData.getIsUseful() &&
                                imageData.getStatus_code() == 200,
                        Collectors.toMap(
                                SocialAIUsefulImagesResponse.SocialAIUsefulImageData::getImageId,
                                Function.identity(),
                                (existing, replacement) -> existing
                        )
                ));

        Map<Integer, SocialAIUsefulImagesResponse.SocialAIUsefulImageData> assetIdVsUsefulImageDataMap = partitionedImageIds.get(true);
        Map<Integer, SocialAIUsefulImagesResponse.SocialAIUsefulImageData> assetIdVsNotUsefulImageDataMap = partitionedImageIds.get(false);

        // Update asset library images
        List<SocialAIPostAssets> usefulAssetLibraryImages = handleAssetLibraryImages(socialAIUsefulImageData);

        List<SocialAIPostAssets> usefulImages = new ArrayList<>();

//        if(assetIdVsUsefulImageDataMap.isEmpty() && assetIdVsNotUsefulImageDataMap.isEmpty()) {
//            log.info("No images found with status_code 200");
//            return;
//        }

        // UPDATE IMAGES WITH isUseful: false and description: null
        if(!assetIdVsNotUsefulImageDataMap.isEmpty()) {
            log.info("Updating isUseful: false for imageIds: {}", assetIdVsNotUsefulImageDataMap.keySet());
            socialAIPostAssetsRepo.updateIsUsefulAndDescription(false, null, assetIdVsNotUsefulImageDataMap.keySet());
        }

        // UPDATE IMAGES WITH isUseful: true and status_code: 200
        if(!assetIdVsUsefulImageDataMap.isEmpty()) {
            log.info("Updating images with status_code: 200 and isUseful: true {}", assetIdVsUsefulImageDataMap.keySet());
            usefulImages = socialAIPostAssetsRepo.findByAssetIdIn(assetIdVsUsefulImageDataMap.keySet());


            if(CollectionUtils.isNotEmpty(usefulImages)) {
                log.info("Useful images already exists");
                usefulImages.stream().map(image -> {
                    log.info("Updating desc and isUseful for imageId: {}", image.getAssetId());
                    image.setIsUseful(Objects.nonNull(assetIdVsUsefulImageDataMap.get(image.getAssetId()).getImageDescription())); // set isUseful: true when image desc is found else false
                    image.setDescription(Optional.ofNullable(assetIdVsUsefulImageDataMap.get(image.getAssetId()).getImageDescription()).orElse(null));
                    return image;
                }).collect(Collectors.toList());
            }

            if (CollectionUtils.isNotEmpty(usefulImages)) {
                log.info("Updating desc and isUseful for imageIds: {}", usefulImages.stream().map(SocialAIPostAssets::getAssetId).collect(Collectors.toList()));
                socialAIPostAssetsRepo.save(usefulImages);
            }
        }

        if(CollectionUtils.isNotEmpty(usefulImages) || CollectionUtils.isNotEmpty(usefulAssetLibraryImages)) {
            //send event to get description for usefulImages: true where description is null
            sendEventToGetAIUsefulImagesDescription(usefulImages, usefulAssetLibraryImages, assetIdVsUsefulImageDataMap, businessId);
        }
    }

    private List<SocialAIPostAssets> handleAssetLibraryImages(List<SocialAIUsefulImagesResponse.SocialAIUsefulImageData> socialAIUsefulImageData) {
        Map<Integer, SocialAIUsefulImagesResponse.SocialAIUsefulImageData> aiAssets = socialAIUsefulImageData.stream()
                .filter(SocialAIUsefulImagesResponse.SocialAIUsefulImageData::getIsAssetImage)
                .filter(imageData -> imageData.getStatus_code() == 200)
                .collect(Collectors.toMap(
                        SocialAIUsefulImagesResponse.SocialAIUsefulImageData::getImageId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        if(!aiAssets.isEmpty()) {
            List<SocialAIPostAssets> usefulImages = socialAIPostAssetsRepo.findByIdIn(aiAssets.keySet());
            if(CollectionUtils.isNotEmpty(usefulImages)) {
                log.info("Useful asset library images already exists");
                usefulImages.stream().map(image -> {
                    log.info("Updating desc and isUseful for imageId: {}", image.getId());
                    image.setIsUseful(Objects.nonNull(aiAssets.get(image.getId()).getImageDescription())); // set isUseful: true when image desc is found else false
                    image.setDescription(Optional.ofNullable(aiAssets.get(image.getId()).getImageDescription()).orElse(null));
                    return image;
                }).collect(Collectors.toList());
            }

            if (CollectionUtils.isNotEmpty(usefulImages)) {
                log.info("Updating desc and isUseful for imageIds: {}", usefulImages.stream().map(SocialAIPostAssets::getAssetId).collect(Collectors.toList()));
                socialAIPostAssetsRepo.save(usefulImages);
            }

            return usefulImages;
        }

        return Collections.emptyList();
    }

    private void sendEventToGetAIUsefulImagesDescription(List<SocialAIPostAssets> usefulImages, List<SocialAIPostAssets> usefulAssetLibraryImages,
                                                         Map<Integer, SocialAIUsefulImagesResponse.SocialAIUsefulImageData> assetIdVsUsefulImageDataMap,
                                                         Integer businessId) {
        SocialAIImageData socialAIImageData = new SocialAIImageData();
        List<SocialAIImageData.ImageData> imageData = usefulImages.stream()
                .filter(image -> image.getDescription() == null)
                        .map(image ->
                                new SocialAIImageData.ImageData(image.getAssetId(), image.getImageUrl(),
                                        parsePublishDate(assetIdVsUsefulImageDataMap.get(image.getAssetId()).getPublishDate())))
                .collect(Collectors.toList());

        // Add useful asset library images with null description
        if (CollectionUtils.isNotEmpty(usefulAssetLibraryImages)) {
            imageData.addAll(usefulAssetLibraryImages.stream()
                    .filter(image -> image.getDescription() == null)
                    .map(image ->
                            new SocialAIImageData.ImageData(image.getAssetId(), image.getImageUrl(), null))
                    .collect(Collectors.toList()));
        }

        if(CollectionUtils.isEmpty(imageData)) {
            log.info("No images found with empty description and status_code 200");
            return;
        }

        log.info("Sending event to get description for imageId with size: {}", imageData.size());

        socialAIImageData.setAccountId(businessId);
        socialAIImageData.setImageData(imageData);

        kafkaProducerService.sendObjectV1("social-ai-useful-images-description", socialAIImageData);
    }


    @Override
    public void saveImageDescription(List<SocialPostAIImageDescription> socialPostAIImageDescriptions) {
        log.info("Received request to update description for images: {}", socialPostAIImageDescriptions);
        for(SocialPostAIImageDescription imageData : socialPostAIImageDescriptions) {
            if(imageData.getStatus_code() == 200) {
                log.info("Update description: {} for imageId: {}", imageData.getDescription(), imageData.getImageId());
                socialAIPostAssetsRepo.updateDescriptionAndIsUseful(imageData.getDescription(), true, imageData.getImageId());
            } else {
                log.info("Not updating description for imageId: {} with status_code: {}", imageData.getImageId(), imageData.getDescription());
            }
        }
    }

    @Override
    public List<SocialPostAIImageDescription> getImageDescription(List<Integer> imageIds) {
        log.info("Received request to get description for imageIds: {}", imageIds);
        if(CollectionUtils.isEmpty(imageIds)) {
            return new ArrayList<>();
        }
        List<SocialAIPostAssets> images = socialAIPostAssetsRepo.findByAssetIdIn(imageIds);
        return images.stream()
                //set status code 422 for null description else 200
                .map(entry -> new SocialPostAIImageDescription(entry.getAssetId(), entry.getImageUrl(), entry.getDescription(), Objects.nonNull(entry.getDescription()) ? 200 : 422))
                .collect(Collectors.toList());
    }

    private PostLibMaster fetchLatestGeneratedPost(Integer accountId) {
        PostLibMaster latestPost = aiGeneratedPostsRepo.findTopByAiSuggestedAndEnterpriseIdOrderByCreatedDateDesc(1,accountId);
        if (Objects.isNull(latestPost)) {
            latestPost = aiGeneratedPostsRepo.findTopByAiSuggestedOrderByCalendarTimeDesc(1);
        }
        return latestPost;
    }

    private Integer calculateNumberOfPosts(AIPostSettingChangeRequest.AIPostConfig newConfig, int weeks, AiPostConfigDetailResponse configDetail) {
        if (newConfig != null) {
            return weeks * newConfig.getPostsFrequency();
        }
        return weeks * configDetail.getPostsPerWeek();
    }

    private SocialAIPostGenerationRequest buildPostGenerationRequest(
            BusinessLiteDTO businessLiteDTO, BusinessProfileResponse businessProfileResponse,
            AIPostSettingChangeRequest.AIPostConfig newConfig, Integer numberOfPosts,
            String countryCode, PostLibMaster latestPost, AiPostConfigDetailResponse configDetail, Date nextWeekStartDate, Date postGenerationEndDate) {

        return SocialAIPostGenerationRequest.builder()
                .enterpriseId(businessLiteDTO.getBusinessNumber())
                .accountId(businessLiteDTO.getBusinessId())
                .categories(newConfig != null ? newConfig.getCategories() : configDetail.getCategories())
                .numberOfPosts(numberOfPosts)
                .category(businessProfileResponse.getCategory())
                .subCategories(businessProfileResponse.getSubCategories())
                .services(businessProfileResponse.getServices())
                .keywords(businessProfileResponse.getKeywords())
                .products(businessProfileResponse.getProducts())
                .description(businessProfileResponse.getDescription())
                .holidays(getHolidays(nextWeekStartDate, postGenerationEndDate, countryCode))
                .country(countryCode)
                .eventPublishedAt(latestPost.getCreatedDate().getTime())
                .name(StringUtils.isNotBlank(businessLiteDTO.getBusinessAlias())
                        ? businessLiteDTO.getBusinessAlias()
                        : businessLiteDTO.getBusinessName())
                .build();
    }

    private void processAndSendNotifications(SocialBusinessProperty socialBusinessProperty, AiPostConfigDetailResponse configDetail,
                                             BusinessLiteDTO businessLiteDTO, boolean reminderEmail) {
        Date postGenStartDate = DateUtils.addDays(socialBusinessProperty.getCurrentScanDate(), 7);
        int daysToAdd = AiPostConfigGenerationType.WEEKLY.equals(configDetail.getLastGenerationFrequency()) ? 6 : 27;
        Date postGenEndDate = DateUtils.addDays(postGenStartDate, daysToAdd);
        Long businessNumber = businessLiteDTO.getBusinessNumber();
        log.info("Processing email notification for business: {}", businessNumber);
        setDatesToExtremeOfTime(postGenStartDate, postGenEndDate);
        log.info("Business AI Post Setting for buisness Number {} is : {}",businessNumber, configDetail);
        postGenStartDate = DateTimeUtils.formatDate(postGenStartDate, YYYY_MM_DD_HH_MM);
        postGenEndDate = DateTimeUtils.formatDate(postGenEndDate, YYYY_MM_DD_HH_MM);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(socialBusinessProperty.getCurrentScanDate());

       // Calendar weekStart = Calendar.getInstance();
        //weekStart.set(Calendar.DAY_OF_WEEK, calendar.get(Calendar.DAY_OF_WEEK));

        // Format week start and end dates

        Date weekStartDate = DateTimeUtils.formatDate(calendar.getTime(), YYYY_MM_DD_HH_MM);
        weekStartDate = DateUtils.addDays(weekStartDate, 7);
        Date weekEndDate = DateUtils.addDays(weekStartDate, 6);

        setDatesToExtremeOfTime(weekStartDate, weekEndDate);

        log.info("Post generations dates for business number {} is: postGenStartDate: {}, postGenEndDate: {}, weekStartDate: {}, weekEndDate: {}",
                businessNumber, postGenStartDate,postGenEndDate,weekStartDate,weekEndDate);
        List<PostLibMaster> aiGeneratedPosts = fetchPostsForNotification(configDetail, businessLiteDTO, weekStartDate, weekEndDate, postGenStartDate, postGenEndDate, reminderEmail);
        Map<Integer, String> postIdVsCollageUrlMap = getCollageUrlForAIGeneratedPosts(aiGeneratedPosts, businessNumber);
        if (CollectionUtils.isNotEmpty(aiGeneratedPosts)) {
          processAndLimitNotifications(aiGeneratedPosts, configDetail, businessLiteDTO, postGenStartDate, postIdVsCollageUrlMap);
        }else{
            log.info("No AI posts found in the given time range,not sending email notification for business: {}", businessNumber);
        }
    }

    private List<PostLibMaster> fetchPostsForNotification(
            AiPostConfigDetailResponse configDetail, BusinessLiteDTO businessLiteDTO,
            Date weekStartDate, Date weekEndDate,
            Date postGenerationWeekStartDate, Date postGenerationWeekEndDate,
            boolean isReminderEmail) {
        List<PostLibMaster> aiGeneratedPosts=new ArrayList<>();
        boolean isWeekly = AiPostConfigGenerationType.WEEKLY.equals(configDetail.getLastGenerationFrequency());
        boolean isNext4Weeks = AiPostConfigGenerationType.NEXT_4_WEEKS.equals(configDetail.getLastGenerationFrequency());

        if (isNext4Weeks && !isReminderEmail) {
            updateBusinessCustomization(businessLiteDTO.getAccountId(),false);
            aiGeneratedPosts= aiGeneratedPostsRepo.getAIPostsInTimeRange(postGenerationWeekStartDate, postGenerationWeekEndDate, businessLiteDTO.getBusinessId());
        }else if (isWeekly || isReminderEmail) {
            if(isReminderEmail){
                updateBusinessCustomization(businessLiteDTO.getAccountId(),true);
            }
            aiGeneratedPosts= aiGeneratedPostsRepo.getAIPostsInTimeRange(weekStartDate, weekEndDate, businessLiteDTO.getBusinessId());
        }
        return  aiGeneratedPosts;
    }

    private void updateBusinessCustomization(Integer accountId, boolean isPicked) {
        BusinessAiCustomization businessAiCustomization = businessAiCustomizationRepo.findByAccountId(accountId);
        if (businessAiCustomization != null) {
            if(isPicked){
                businessAiCustomization.setPicked(false);
            }else{
                businessAiCustomization.setLastEmailSendAt(new Date());
            }
            businessAiCustomizationRepo.save(businessAiCustomization);
        }
    }



    private void processAndLimitNotifications(List<PostLibMaster> aiGeneratedPosts, AiPostConfigDetailResponse configDetail,
                                              BusinessLiteDTO businessLiteDTO, Date postGenStartDate,
                                              Map<Integer, String> postIdVsCollageUrlMap) {
        int aiPostsCountLimit = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getEmailNotificationAIPostsCount();

        List<PostLibMaster> limitedPosts = aiGeneratedPosts.stream()
                .limit(aiPostsCountLimit)
                .collect(Collectors.toList());

        aiPostsNotificationService.processAIPostNotifications(limitedPosts, configDetail, businessLiteDTO, postGenStartDate, postIdVsCollageUrlMap);
    }

    private Map<String, Long> processTags(List<AIGeneratedPostsResponse.AIGeneratedPost> aiGeneratedPosts, BusinessLiteDTO businessLiteDTO) {
        Map<String, Long> tagMap = new HashMap<>();
        AbstractSocialTagOperationService tagOperationService = SocialTagOperationServiceFactory.byOperation(SocialTagOperation.CREATE);

        Long aiUserId = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultUserIdForAIGeneratedPosts().longValue();

        Set<String> uniqueTags = aiGeneratedPosts.stream()
                .filter(post -> Objects.nonNull(post.getTag()))
                .map(post -> post.getTag())
                .collect(Collectors.toSet());


        log.info("Unique tags found for save operation: {}", uniqueTags);

        for(String tag : uniqueTags) {
            Set<AbstractSocialTagOperation.SocialTagOperationDetail> tagDetails = new HashSet();
            tagDetails.add(new AbstractSocialTagOperation.SocialTagOperationDetail(tag));
            SocialTagOperationResponse tagOperationResponse = tagOperationService.performOperation(
                    businessLiteDTO.getAccountId(),
                    businessLiteDTO.getEnterpriseNumber(),
                    aiUserId,
                    tagDetails,
                    false
            );

            if (tagOperationResponse == null || CollectionUtils.isEmpty(tagOperationResponse.getTags())) {
                log.error("Tag operation response is null or empty");
                return Collections.emptyMap();
            }

            log.info("Tag operation response: {}", tagOperationResponse);

            tagMap.putAll(tagOperationResponse.getTags().stream()
                    .collect(Collectors.toMap(
                            AbstractSocialTagOperation.SocialTagOperationDetail::getName,
                            AbstractSocialTagOperation.SocialTagOperationDetail::getId
                    )));
        }

        return tagMap;
    }

    private Map<String, PostLibMaster> buildAiGeneratedPosts(AIGeneratedPostsResponse aiGeneratedPostsResponse,
                                                      BusinessLiteDTO businessLiteDTO) {
        if (isEmptyResponse(aiGeneratedPostsResponse)) {
            return Collections.emptyMap();
        }

        setSocialPostIds(aiGeneratedPostsResponse.getAiGeneratedPosts());

        return aiGeneratedPostsResponse.getAiGeneratedPosts().stream()
                .filter(Objects::nonNull)
                .collect(
                        Collectors
                                .toMap(post -> Optional.ofNullable(post.getPostId()).orElse(post.getEventId()),
                                        post -> {
                                            try {
                                                return buildPostLibMaster(post, businessLiteDTO);
                                            } catch (IOException e) {
                                                log.error("Error building PostLibMaster for postId: {}, error: {}", post.getPostId(), e.getMessage(), e);
                                                throw new RuntimeException(e);
                                            }
                                        }));
    }

    private void setSocialPostIds(List<AIGeneratedPostsResponse.AIGeneratedPost> aiGeneratedPosts) {
        if (CollectionUtils.isNotEmpty(aiGeneratedPosts)) {
            try {
                Map<String, String> postIdToBePostIdMap = fetchPostIdToBePostIdMap(aiGeneratedPosts);
                if(!postIdToBePostIdMap.isEmpty()) {
                    List<Integer> socialPostIds = postIdToBePostIdMap.values().stream()
                            .map(Integer::valueOf)
                            .collect(Collectors.toList());
                    List<SocialPost> socialPosts = socialPostRepository.findByIdIn(socialPostIds);
                    Map<Integer, Integer> postIdToMasterPostIdMap = new HashMap<>();
                    if(CollectionUtils.isNotEmpty(socialPosts)) {
                        postIdToMasterPostIdMap = socialPosts.stream()
                                .collect(Collectors.toMap(SocialPost::getId, SocialPost::getMasterPostId));
                    }

                    for(AIGeneratedPostsResponse.AIGeneratedPost post : aiGeneratedPosts) {
                        if (postIdToBePostIdMap.containsKey(post.getPostId())) {
                            Integer socialPostId = Integer.valueOf(postIdToBePostIdMap.get(post.getPostId()));
                            post.setSocialMasterPostId(postIdToMasterPostIdMap.get(socialPostId));
                        }
                    }
                }

            } catch (Exception e) {
                log.error("Error fetching postId to bePostId map: {}", e.getMessage(), e);
            }
        }
    }

    private Map<String, String> fetchPostIdToBePostIdMap(List<AIGeneratedPostsResponse.AIGeneratedPost> topPerformingPosts) throws IOException {
        // Step 1: Extract all postId values from topPerformingPosts
        List<String> postIds = topPerformingPosts.stream()
                .map(AIGeneratedPostsResponse.AIGeneratedPost::getPostId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(postIds)) {
            log.info("No postIds found in topPerformingPosts.");
            return Collections.emptyMap();
        }

        // Step 2: Build Elasticsearch query
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termsQuery("post_id", postIds))
                .filter(QueryBuilders.termQuery("is_be_post", true));

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQuery)
                .fetchSource(new String[]{"post_id", "be_post_id"}, null) // Fetch only required fields
                .size(postIds.size());

        SearchRequest searchRequest = new SearchRequest(InsightsConstants.POST_INSIGHT).source(sourceBuilder);

        // Step 3: Execute the search and parse the response
        SearchResponse searchResponse = esService.search(searchRequest);
        Map<String, String> postIdToBePostIdMap = Arrays.stream(searchResponse.getHits().getHits())
                .map(hit -> JSONUtils.fromJSON(hit.getSourceAsString(), EsPostDataPoint.class))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        EsPostDataPoint::getPost_id,
                        EsPostDataPoint::getBe_post_id,
                        (existing, replacement) -> existing // Handle duplicate keys
                ));

        log.info("Fetched postId to bePostId map: {}", postIdToBePostIdMap);
        return postIdToBePostIdMap;
    }

    private boolean isEmptyResponse(AIGeneratedPostsResponse response) {
        return response == null || CollectionUtils.isEmpty(response.getAiGeneratedPosts());
    }

    private PostLibMaster buildPostLibMaster(AIGeneratedPostsResponse.AIGeneratedPost post, BusinessLiteDTO businessLiteDTO) throws IOException {

        log.info("Creating post_lib_master with postId: {}, postText: {} imageSource: {}", post.getPostId(), post.getPostText(),
                post.getImageSource());

        String publicImageIds = getPublicImageIds(post);
        PexelsImagesDTO pexelsImageIds = getPexelsImageIds(post);
        if(StringUtils.isEmpty(publicImageIds)){
            publicImageIds = pexelsImageIds.getImageIds();
        }
        String imageIds = getImageIds(post);

        return PostLibMaster.builder()
                .postHeader(post.getPostHeader())
                .postId(post.getPostId())
                .socialMasterPostId(post.getSocialMasterPostId())
                .imageIds(imageIds)
                .compressedImageIds(imageIds)
                .publicCompressedImageIds(pexelsImageIds.getCompressedImageIds())
                .publicImageIds(publicImageIds)
                .pexelsImageIds(pexelsImageIds.getPexelsImageIds())
                .postText(post.getPostText())
                .usageCount(0)
                .tag(post.getTag())
                .createdDate(new Date())
                .aiSuggested(1)
                .createdBy(getDefaultUserId())
                .lastEditedAt(new Date())
                .enterpriseId(getEnterpriseId(businessLiteDTO))
                .aiReason(post.getReason())
                .imageSource(post.getImageSource())
                .build();
    }

    private String getImageIds(AIGeneratedPostsResponse.AIGeneratedPost post) {
        String imageIds = null;
        if(CollectionUtils.isNotEmpty(post.getRepurposedImageIds())) {
            imageIds = post.getRepurposedImageIds().stream().map(String::valueOf).collect(Collectors.joining(","));
        }

        return imageIds;
    }

    private String getPublicImageIds(AIGeneratedPostsResponse.AIGeneratedPost post) throws IOException {
        String publicImageIds = null;
        if(CollectionUtils.isNotEmpty(post.getAssetImageIds())) {
            publicImageIds = post.getAssetImageIds().stream().map(String::valueOf).collect(Collectors.joining(","));
        }
        return publicImageIds;
    }

    private PexelsImagesDTO getPexelsImageIds(AIGeneratedPostsResponse.AIGeneratedPost post) throws IOException {
        if(CollectionUtils.isNotEmpty(post.getPexelUrls())) {
            PexelsImagesDTO pexelsImagesDTO = new PexelsImagesDTO();
            log.info("Creating images from pexels: {}", post.getPostId());
            List<String> pexelsImageUrls = post.getPexelUrls();
            List<String> allUrls = new ArrayList<>();
            allUrls.addAll(pexelsImageUrls); // to be replaced by S3 uploaded urls
            allUrls.addAll(pexelsImageUrls); // to be replaced by S3 uploaded compressed urls
            allUrls.addAll(pexelsImageUrls); // pexels original urls
            List<SocialAIPostAssets> aiPostAssets = allUrls.stream()
                    .map(SocialAIPostAssets::new)
                    .collect(Collectors.toList());

            socialAIPostAssetsRepo.save(aiPostAssets);

            List<Integer> assetIds = aiPostAssets.stream()
                    .map(SocialAIPostAssets::getId)
                    .collect(Collectors.toList());

            String imageIds = assetIds.subList(0, pexelsImageUrls.size()).stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            String compressedImageIds = assetIds.subList(pexelsImageUrls.size(), pexelsImageUrls.size()*2).stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            String pexelsImageIds = assetIds.subList(pexelsImageUrls.size()*2, pexelsImageUrls.size()*3).stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));


            pexelsImagesDTO.setImageIds(imageIds);
            pexelsImagesDTO.setCompressedImageIds(compressedImageIds);
            pexelsImagesDTO.setPexelsImageIds(pexelsImageIds);
            return pexelsImagesDTO;

        }else{
            log.info("No pexels images found for postId: {}", post.getPostId());
            return new PexelsImagesDTO();
        }
    }

    private Integer getDefaultUserId() {
        return CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getDefaultUserIdForAIGeneratedPosts();
    }

    private Integer getEnterpriseId(BusinessLiteDTO businessLiteDTO) {
        return Optional.ofNullable(businessLiteDTO)
                .map(BusinessLiteDTO::getAccountId)
                .orElse(null);
    }

    private Map<String, PostLibMaster> addBTPTimeInPosts(BusinessLiteDTO businessLiteDTO,
                                                         Map<String, PostLibMaster> aiGeneratedPostMap,
                                                         Date postGenStartDate,
                                                         Date postGenEndDate,
                                                         List<SocialAIPostGenerationRequest.Holiday> holidays) {
        Map<String, PostLibMaster> updatedPostMap = new HashMap<>();
        try {
            int btpSlotIndex = 0;
            String countryCode = (Objects.nonNull(businessLiteDTO.getLocation()) &&
                    Objects.nonNull(businessLiteDTO.getLocation().getCountryCode()))
                    ? businessLiteDTO.getLocation().getCountryCode() : "US";

            AiPostConfigDetailResponse aiPostConfigDetailResponse = socialPostAiService.getBusinessAiConfig(
                    businessLiteDTO.getAccountId(), businessLiteDTO.getBusinessNumber(), null);

            SocialBTPRequest socialBTPRequest = new SocialBTPRequest();
            socialBTPRequest.setCurrentTime(postGenStartDate.getTime());
            socialBTPRequest.setCountryCode(countryCode);
            SocialBTPCalenderResponse socialBTPCalenderResponse = bestTimeToPostService
                    .getScheduleTimePerMonthForAIPosts(socialBTPRequest, businessLiteDTO.getTimeZoneId(), postGenEndDate);

            Map<Date, List<String>> dateTimeMap = parseTimeSlots(socialBTPCalenderResponse);

            if (!dateTimeMap.isEmpty()) {
                List<Date> sortedDates = new ArrayList<>(dateTimeMap.keySet());
                sortedDates.sort(Date::compareTo);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(sortedDates.get(0));
                int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
                Map<Integer, List<Date>> weekMap = groupDatesIntoWeeks(sortedDates, dayOfWeek);

                // Filter posts without calendar time
                Map<String, PostLibMaster> postsWithoutTime = aiGeneratedPostMap.entrySet().stream()
                        .filter(entry -> Objects.isNull(entry.getValue().getCalendarTime()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

                while (!postsWithoutTime.isEmpty() && btpSlotIndex < 3) {
                    updatedPostMap.putAll(distributePosts(postsWithoutTime, dateTimeMap, weekMap,
                            aiPostConfigDetailResponse, btpSlotIndex, holidays, businessLiteDTO.getTimeZoneId()));

                    // Update the remaining posts without time
                    postsWithoutTime = aiGeneratedPostMap.entrySet().stream()
                            .filter(entry -> Objects.isNull(entry.getValue().getCalendarTime()))
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                    btpSlotIndex++;
                }
            }
        } catch (Exception e) {
            log.error("Unexpected error occurred in getBtpTime method", e);
        }
        return updatedPostMap;
    }

    private Map<Date, List<String>> parseTimeSlots(SocialBTPCalenderResponse socialBTPCalenderResponse) {
        Map<Date, List<String>> dateTimeMap = new HashMap<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");

        for (Map.Entry<String, List<String>> entry : socialBTPCalenderResponse.getTimeSlots().entrySet()) {
            try {
                String dateStr = entry.getKey();
                Date date = dateFormat.parse(dateStr);
                dateTimeMap.put(date, entry.getValue());
            } catch (ParseException e) {
                log.error("Error parsing date: {}", entry.getKey(), e);
            }
        }
        return dateTimeMap;
    }
    public Map<Integer, List<Date>> groupDatesIntoWeeks(List<Date> sortedDates, int startDay) {
        Map<Integer, List<Date>> weekMap = new LinkedHashMap<>();
        List<Date> currentWeek = new ArrayList<>();
        int weekIndex = 1;

        for (Date date : sortedDates) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);

            // If it's the start of a new week and the current week is not empty, store the previous week
            if (dayOfWeek == startDay && !currentWeek.isEmpty()) {
                weekMap.put(weekIndex++, new ArrayList<>(currentWeek));
                currentWeek.clear();
            }

            currentWeek.add(date);
        }

        // Add the last week if any dates remain
        if (!currentWeek.isEmpty()) {
            weekMap.put(weekIndex, currentWeek);
        }

        return weekMap;
    }

    private Map<String, PostLibMaster> distributePosts(Map<String, PostLibMaster> aiGeneratedPostMap,
                                                       Map<Date, List<String>> dateTimeMap,
                                                       Map<Integer, List<Date>> weekMap,
                                                       AiPostConfigDetailResponse aiPostConfigDetailResponse,
                                                       int btpSlotIndex,
                                                       List<SocialAIPostGenerationRequest.Holiday> holidays,
                                                       String timeZoneId) {
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat(DateTimeUtils.YYYY_MM_DD);
        int index = 0;
        Set<String> datesUsed = new HashSet<>();
        Set<Integer> indexUsed = new HashSet<>();

        // Convert map to list for index-based access while preserving order
        List<Map.Entry<String, PostLibMaster>> postEntries = new ArrayList<>(aiGeneratedPostMap.entrySet());

        // Process holidays first
        holidays.forEach(holiday -> {
            Date date = holiday.getDate();
            try {
                if (dateTimeMap.containsKey(date) && dateTimeMap.get(date).size() > btpSlotIndex) {
                    String formattedDate = dateTimeFormat.format(date) + " " + dateTimeMap.get(date).get(btpSlotIndex) + ":00";
                    if (!datesUsed.contains(formattedDate)) {
                        postEntries.stream()
                                .filter(entry -> entry.getValue().getPostHeader().contains(holiday.getHoliday()))
                                .findFirst()
                                .ifPresent(entry -> {
                                    log.info("formattedDate {} and timeZoneId {}", formattedDate, timeZoneId);
                                    entry.getValue().setCalendarTime(DateTimeUtils.getDateWithInputFormat(
                                            getCurrentTimeZoneToUTC(formattedDate, timeZoneId),
                                            DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
                                    datesUsed.add(formattedDate);
                                    indexUsed.add(postEntries.indexOf(entry));
                                });
                    }
                }
            } catch (Exception e) {
                log.info("Exeception while processing holiday posts: {}", e.getMessage());
            }
        });

        // Process regular posts
        for (Map.Entry<Integer, List<Date>> entry : weekMap.entrySet()) {
            List<Date> weekDates = entry.getValue();
            Integer postsPerWeek = aiPostConfigDetailResponse.getPostsPerWeek();
            List<Integer> customOrder = getCustomOrder(weekDates.get(0));
            weekDates.sort(Comparator.comparingInt(date -> customOrder.indexOf(getDayOfWeek(date))));

            for (Date date : weekDates) {
                if (index < postEntries.size() && postsPerWeek > 0 &&
                        Objects.nonNull(dateTimeMap.get(date)) && dateTimeMap.get(date).size() > btpSlotIndex) {
                    try {
                        String formattedDate = dateTimeFormat.format(date) + " " + dateTimeMap.get(date).get(btpSlotIndex) + ":00";
                        if (!datesUsed.contains(formattedDate)) {
                            if (indexUsed.contains(index)) {
                                index = getNextUnusedIndex(index, indexUsed);
                            }
                            log.info("formattedDate {} and timeZoneId {}", formattedDate, timeZoneId);
                            postEntries.get(index).getValue().setCalendarTime(
                                    DateTimeUtils.getDateWithInputFormat(
                                            getCurrentTimeZoneToUTC(formattedDate, timeZoneId),
                                            DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
                            datesUsed.add(formattedDate);
                            postsPerWeek--;
                            index++;
                        }
                    } catch (Exception e) {
                        log.error("Error setting calendar time for date: {}", date, e);
                    }
                }
            }
        }

        // Return posts
        return postEntries.stream()
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private int getNextUnusedIndex(int index, Set<Integer> indexUsed) {
        int newIndex = index + 1;
        while (indexUsed.contains(newIndex)) {
            newIndex++;
        }
        return newIndex;
    }


    private List<PostLibMaster> saveAiGeneratedPosts(Map<String, PostLibMaster> aiGeneratedPostMap,
                                                            Map<String, Long> tagMap) {
        // Convert map values to list for saving
        List<PostLibMaster> postsToSave = new ArrayList<>(aiGeneratedPostMap.values());

        if (CollectionUtils.isNotEmpty(postsToSave)) {
            log.info("Saving ai generated to post to post_lib_master: {}", postsToSave.size());
            // Save the posts
            List<PostLibMaster> savedPosts = aiGeneratedPostsRepo.save(postsToSave);


            // Create a new map for the saved posts
            Map<String, PostLibMaster> savedPostMap = new HashMap<>();

            // Reconstruct the map with the saved posts, preserving the original post IDs
            for (PostLibMaster savedPost : savedPosts) {
                // Find the original post in the input map to get the ID
                aiGeneratedPostMap.entrySet().stream()
                        .filter(entry -> entry.getValue().equals(savedPost))
                        .findFirst()
                        .ifPresent(entry -> {
                            savedPostMap.put(entry.getKey(), savedPost);
                        });
            }
            socialPostCalendarESService.save(socialPostCalendarESService.getESCalendarSaveObjForAi(savedPostMap, tagMap));
            return savedPosts;
        }

        return Collections.emptyList();
    }

    private void mapTagsToEntities(List<PostLibMaster> savedPosts, Map<String, Long> tagMap, BusinessLiteDTO businessLiteDTO) {
        if (CollectionUtils.isEmpty(savedPosts)) {
            return;
        }
        log.info("Map tags to entities for posts: {} and tagMap: {}", savedPosts, tagMap);

        long defaultAIUserId = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getDefaultUserIdForAIGeneratedPosts();

        savedPosts.stream()
                .filter(post -> Objects.nonNull(post.getTag()))
                .forEach(post -> {
                    Long entityId = post.getId().longValue();
                    Long tagId = tagMap.get(post.getTag());

                    log.info("Creating tag mapping for entityId: {} and tagId: {} and userId: {}", entityId, tagId, defaultAIUserId);

                    SocialTagEntityBulkMappingRequest bulkMappingRequest = new SocialTagEntityBulkMappingRequest();
                    bulkMappingRequest.setEntityIds(Collections.singleton(entityId));

                    SocialTagMappingOperationRequest operationRequest = new SocialTagMappingOperationRequest();
                    operationRequest.setOperation(SocialTagOperation.CREATE);
                    operationRequest.setTagIds(Collections.singleton(tagId));

                    bulkMappingRequest.setTagMappings(Collections.singleton(operationRequest));

                    socialTagService.performTagEntityMappingBulkOperations(
                            bulkMappingRequest,
                            SocialTagEntityType.POST_LIB,
                            businessLiteDTO.getAccountId(),
                            defaultAIUserId,
                            businessLiteDTO.getEnterpriseNumber()
                    );
                });
    }

   private Integer getNumberOfPost(AiPostConfigDetailResponse aiPostConfigDetailResponse) {
        return aiPostConfigDetailResponse.getPostsPerWeek() * (AiPostConfigGenerationType.WEEKLY.equals(aiPostConfigDetailResponse.getGenerationFrequency())?1:4);
    }


    private List<SocialAIPostGenerationRequest.Holiday> getHolidays(Date startDate,Date endDate,String countryCode) {
        List<SocialAIPostGenerationRequest.Holiday> holidays = new ArrayList<>();
        if(Objects.nonNull(countryCode)){
            List<SocialCalendarEvents> socialCalendarEvents = socialCalendarEventRepo.findEventsBetweenDates(startDate, endDate, countryCode);
            holidays = socialCalendarEvents.stream()
                    .map(holiday -> new SocialAIPostGenerationRequest.Holiday(holiday.getEventDate(), holiday.getEventName()))
                    .collect(Collectors.toList());
        }
        return  holidays;
    }

    public Date formatTimeWithPattern(Date currentTime, String timezoneId,SimpleDateFormat simpleDateFormat) {
        int offset = getOffset(timezoneId);
        currentTime = DateUtils.addMinutes(currentTime,offset);
        String date = simpleDateFormat.format(currentTime);
        try {
            return simpleDateFormat.parse(date);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
    private static List<Integer> getCustomOrder(Date startDate) {
        int startDay = getDayOfWeek(startDate);
        Map<Integer, List<Integer>> orderMap = new HashMap<>();
        orderMap.put(Calendar.MONDAY, Arrays.asList(
                Calendar.MONDAY, Calendar.WEDNESDAY, Calendar.FRIDAY,
                Calendar.TUESDAY, Calendar.THURSDAY, Calendar.SATURDAY, Calendar.SUNDAY));
        orderMap.put(Calendar.TUESDAY, Arrays.asList(
                Calendar.WEDNESDAY, Calendar.FRIDAY, Calendar.MONDAY,
                Calendar.THURSDAY, Calendar.TUESDAY, Calendar.SATURDAY, Calendar.SUNDAY));
        orderMap.put(Calendar.WEDNESDAY, orderMap.get(Calendar.TUESDAY));
        orderMap.put(Calendar.THURSDAY, Arrays.asList(
                Calendar.FRIDAY, Calendar.MONDAY, Calendar.WEDNESDAY,
                Calendar.THURSDAY, Calendar.TUESDAY, Calendar.SATURDAY, Calendar.SUNDAY));
        orderMap.put(Calendar.FRIDAY, orderMap.get(Calendar.THURSDAY));
        orderMap.put(Calendar.SATURDAY, orderMap.get(Calendar.MONDAY));
        orderMap.put(Calendar.SUNDAY, orderMap.get(Calendar.MONDAY));
        return orderMap.getOrDefault(startDay, orderMap.get(Calendar.MONDAY));
    }
    private static int getDayOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_WEEK);
    }

    private Map<Integer, String> getCollageUrlForAIGeneratedPosts(List<PostLibMaster> aiGeneratedPosts, Long businessNumber) {
        Map<Integer, String> postIdVsCollageUrlMap = new HashMap<>();
        for(PostLibMaster post : aiGeneratedPosts) {
            log.info("Generating collage for AI generated post: {}", post.getId());
            List<String> imageUrls = new ArrayList<>();
            if (StringUtils.isNotEmpty(post.getImageIds())) {
                log.info("Image ids found: {}", post.getImageIds());
                List<Integer> imageIds = Arrays.stream(post.getImageIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
                imageUrls = socialAIPostAssetsRepo.findImageUrlsByAssetIdIn(imageIds);
            }
            if(StringUtils.isNotEmpty(post.getPublicImageIds())) {
                log.info("Public image ids found: {}", post.getPublicImageIds());
                List<Integer> publicImageIds = Arrays.stream(post.getPublicImageIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(publicImageIds)) {
                    List<SocialAIPostAssets> publicImages = socialAIPostAssetsRepo.findByIdIn(publicImageIds);
                    if (CollectionUtils.isNotEmpty(publicImages)) {
                        imageUrls.addAll(publicImages.stream().map(SocialAIPostAssets::getImageUrl).collect(Collectors.toList()));
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(imageUrls)) {
                log.info("Image urls: {}", imageUrls);
                List<String> updatedUrls = removeQueryParamsFromPexelsImages(imageUrls);
                log.info("Image urls after removing query params: {}", updatedUrls);
                String collageUrl = commonService.generateCollageForImages(updatedUrls, businessNumber);
                log.info("Collage url: {}", collageUrl);
                postIdVsCollageUrlMap.put(post.getId(), collageUrl);
            }
        }
        return postIdVsCollageUrlMap;
    }

    private Map<String, String> saveImageUrlsToSocialDBs(EsPostDataPoint post) {
        log.info("Saving image urls to social_post_assets and social_ai_post_images for post: {}", post.getPost_id());
        Map<String, String> idVsUrlMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(post.getImage_urls())) {

            saveImagesToSocialPostAssets(post.getImage_urls(), idVsUrlMap);

            if(!idVsUrlMap.isEmpty()) {
                List<Integer> assetIds = idVsUrlMap.keySet().stream().map(id -> Integer.valueOf(id)).collect(Collectors.toList());
                List<SocialAIPostAssets> usefulImages = socialAIPostAssetsRepo.findByAssetIdIn(assetIds);
                if(CollectionUtils.isNotEmpty(usefulImages)) {
                    log.info("Useful Image already exists imageIds: {}", usefulImages.stream().map(SocialAIPostAssets::getAssetId).collect(Collectors.toList()));
                    for(SocialAIPostAssets image : usefulImages) {
                        assetIds.remove(image.getAssetId());
                    }
                   // socialAIPostAssetsRepo.save(usefulImages);
                }
                if(CollectionUtils.isNotEmpty(assetIds)) {
                    log.info("Creating entries in social_ai_post_assets for imageIds: {}", assetIds);
                    Map<String, String> finalIdVsUrlMap = idVsUrlMap;
                    List<SocialAIPostAssets> images = assetIds.stream()
                            .map(id -> new SocialAIPostAssets(id, finalIdVsUrlMap.get(String.valueOf(id))))
                            .collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(images)) {
                        log.info("Saving images to social_ai_post_assets: {}", images);
                        socialAIPostAssetsRepo.save(images);
                    }
                }
            }
        } else {
            log.info("No Images found for post: {}", post.getPost_id());
        }
        return idVsUrlMap;
    }


    private void removeReviewPosts(List<EsPostDataPoint> posts) {
        Map<Boolean, List<EsPostDataPoint>> partitionedPosts = posts.stream()
                .collect(Collectors.partitioningBy(EsPostDataPoint::getIs_be_post));

        List<EsPostDataPoint> bePosts = partitionedPosts.get(true);
        List<EsPostDataPoint> externalPosts = partitionedPosts.get(false);

        List<Integer> bePostIds = bePosts.stream()
                .map(post -> Integer.valueOf(post.getBe_post_id())).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(bePostIds)) {
            log.info("Social posts found with ids: {}", bePostIds);
            List<Integer> reviewPostIds = socialPostRepository.findByIdInAndSaveType(bePostIds, "review_share");
            if(CollectionUtils.isNotEmpty(reviewPostIds)) {
                log.info("Review posts found with ids: {}", reviewPostIds);
                log.info("Removing review posts from the list...");
                bePosts.removeIf(post -> reviewPostIds.contains(Integer.valueOf(post.getBe_post_id())));
                posts.clear();
                posts.addAll(bePosts);
                posts.addAll(externalPosts);
            }
        }
    }

    private void saveImagesToSocialPostAssets(List<String> imageUrls, Map<String, String> idVsUrlMap) {
        String cdnUrl = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getCdnImageBaseUrl();
        //Remove cdn base url
        List<String> processedImageUrls = imageUrls.stream().map(url -> {
                    url = url.replaceFirst("^https://", "").replaceFirst("^http://", "");
                    int firstSlashIndex = url.indexOf('/');
                    if (firstSlashIndex >= 0) {
                        return url.substring(firstSlashIndex + 1);
                    } else return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        log.info("Processed Image urls: {}", processedImageUrls);
        Map<String, String> bucketIdVsUrlMap = new HashMap<>();
        List<String> imagesWithBucket = new ArrayList<>();
        for(String url : processedImageUrls) {
            //check if bucket id is present in the url
            log.info("Checking if bucket id is present in the url: {}", url);
           try {
               String [] split = url.split("/");
               Long.parseLong(split[0]);
               int firstSlashIndex = url.indexOf('/');
               bucketIdVsUrlMap.put(split[0], url.substring(firstSlashIndex + 1));
               imagesWithBucket.add(url);
           } catch (Exception e) {
               log.info("Bucket Id not found for url: {}", url);
           }
        }
        if(CollectionUtils.isNotEmpty(imagesWithBucket)) {
            log.info("Removing images with bucket from processed image urls: {}", imagesWithBucket);
            processedImageUrls.removeAll(imagesWithBucket);
        }

        List<String> checkExistingUrls = new ArrayList<>();
        checkExistingUrls.addAll(processedImageUrls);
        checkExistingUrls.addAll(bucketIdVsUrlMap.values());
        //check if image already exists
        List<SocialPostsAssets> existingMedia = socialPostsAssetsRepository.findByMediaUrls(checkExistingUrls);
        if(CollectionUtils.isNotEmpty(existingMedia)) {
            existingMedia.stream().forEach(media -> idVsUrlMap.put(String.valueOf(media.getId()), constructMediaUrl(media, cdnUrl)));
            processedImageUrls.removeAll(existingMedia.stream().map(SocialPostsAssets::getImageUrl).collect(Collectors.toList()));
            for(SocialPostsAssets media : existingMedia) {
                if(bucketIdVsUrlMap.containsKey(media.getBucketId())) {
                    bucketIdVsUrlMap.remove(media.getBucketId());
                }
            }
        }
        List<SocialPostsAssets> mediaList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(processedImageUrls))
            log.info("Adding images without bucket to social_post_assets: {}", processedImageUrls);
            mediaList.addAll(processedImageUrls.stream().map(imageUrl -> new SocialPostsAssets(imageUrl, null)).collect(Collectors.toList()));
        if(!bucketIdVsUrlMap.isEmpty()) {
            log.info("Adding images with bucket to social_post_assets: {}, {}", bucketIdVsUrlMap.keySet(), bucketIdVsUrlMap.values());
            mediaList.addAll(bucketIdVsUrlMap.entrySet().stream().map(entry -> new SocialPostsAssets(entry.getValue(), entry.getKey())).collect(Collectors.toList()));
        }
        //save image urls to social_post_assets
        if(CollectionUtils.isNotEmpty(mediaList)) {
            log.info("Saving image urls to social_post_assets: {}", mediaList);
            List<SocialPostsAssets> savedMedia = socialPostsAssetsRepository.save(mediaList);

            if(CollectionUtils.isNotEmpty(savedMedia)) {
                savedMedia.stream().forEach(media -> idVsUrlMap.put(String.valueOf(media.getId()), constructMediaUrl(media, cdnUrl)));
            }
        }
    }

    private List<String> removeQueryParamsFromPexelsImages(List<String> urls) {
        List<String> updatedUrls = new ArrayList<>();
        for(String url : urls) {
            int index = url.indexOf('?');
            if(index > 0) {
                updatedUrls.add(url.substring(0, index));
            } else {
                updatedUrls.add(url);
            }
        }
        return updatedUrls;
    }

    private String constructMediaUrl(SocialPostsAssets media, String cdnUrl) {
       String completeUrl = cdnUrl + "/";
       if(StringUtils.isNotEmpty(media.getBucketId())) {
           completeUrl += media.getBucketId() + "/";
       }
       completeUrl += media.getImageUrl();
       return completeUrl;
    }

    private void sendEventForAssetLibraryImages(Long enterpriseId, Date startDate, Date endDate, Boolean isOneTimeMigration) {
        log.info("Sending event for asset library images for enterpriseId: {} and date range: {}, {}", enterpriseId, startDate, endDate);
        String CDNBaseUrl = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getCdnImageBaseUrl();

        int IMAGE_BATCH_SIZE = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getAIPostMigrationBatchSize();

        List<String> assetUrls;
        BusinessLiteDTO businessLiteDTO = null;
        if(Boolean.TRUE.equals(isOneTimeMigration)) {
            businessLiteDTO = businessCoreService.getBusinessLiteByNumber(enterpriseId);
            assetUrls = socialAssetLibraryService.getAllAssetUrls(enterpriseId, SocialAssetLibraryAssetType.PHOTO, SocialAssetLibraryAssetStatus.ACTIVE);
        } else {
            assetUrls = socialAssetLibraryService.getAllAssetUrlsForDateRange(startDate, endDate, SocialAssetLibraryAssetType.PHOTO, SocialAssetLibraryAssetStatus.ACTIVE);
        }

        if(CollectionUtils.isEmpty(assetUrls)) {
            log.info("No asset urls found for enterpriseId: {} or in date range: {}, {}", enterpriseId, startDate, endDate);
            return;
        }
        List<String> completeAssetUrls = addCDNBaseUrlToAssetUrls(CDNBaseUrl, assetUrls);
        BusinessLiteDTO finalBusinessLiteDTO = businessLiteDTO;
        List<SocialPostAIStreamResponse> assetImagesRequest = completeAssetUrls.stream()
                .map(url -> createAssetImageRequest(url, finalBusinessLiteDTO))
                .collect(Collectors.toList());

        int totalBatches = (assetImagesRequest.size() + IMAGE_BATCH_SIZE - 1) / IMAGE_BATCH_SIZE;
        log.info("Total batches for asset images: {}", totalBatches);
        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            int start = batchIndex * IMAGE_BATCH_SIZE;
            int end = Math.min(start + IMAGE_BATCH_SIZE, assetImagesRequest.size());
            List<SocialPostAIStreamResponse> currentRequest = assetImagesRequest.subList(start, end);
            kafkaProducerService.sendObjectV1("social-ai-stream-image-only", currentRequest); // consumed by social only
        }

    }

    private List<String> addCDNBaseUrlToAssetUrls(String CDNBaseUrl, List<String> assetUrls) {
        if (CollectionUtils.isEmpty(assetUrls) || StringUtils.isBlank(CDNBaseUrl)) {
            return Collections.emptyList();
        }
        return assetUrls.stream()
                .map(url -> CDNBaseUrl + "/" + url)
                .collect(Collectors.toList());
    }

    private SocialPostAIStreamResponse createAssetImageRequest(String assetImageUrl, BusinessLiteDTO businessLiteDTO) {
        SocialPostAIStreamResponse request = new SocialPostAIStreamResponse();
        request.setAccountId((businessLiteDTO != null) ? businessLiteDTO.getAccountId() : null);
        request.setImageUrl(assetImageUrl);
        request.setIsAssetImage(true);
        request.setUseful(false);
        return request;
    }
}