/**
 *
 */
package com.birdeye.social.service;

import com.birdeye.social.dto.FacebookPageMetadataDTO;
import com.birdeye.social.dto.SocialPageDTO;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.model.FilterPageRequest;

import java.util.List;
import java.util.Map;

/**
 ** File:         FacebookSocialService.java
 ** Created:      11 Apr 2018
 ** Author:       sahilarora
 **
 ** This code is copyright (c) BirdEye Software India Pvt. Ltd.
 **/
public interface FacebookSocialService {
	
	/**
	 * Cache facebook page profile metadata : pagename, profile image url, page name, address etc
	 * @param businessFbPage Raw facebook page
	 * @return FacebookPageMetadataDTO
	 */
	FacebookPageMetadataDTO getFacebookPageMetadataCached(BusinessFBPage businessFbPage);
	
	/**
	 * Get facebook page metadata details by page id and cache it
	 * @param facebookPageId String value of facebookPageId
	 * @return FacebookPageMetadataDTO
	 */
	FacebookPageMetadataDTO getFacebookPageDetailsByPageId(String facebookPageId);

	FacebookPageMetadataDTO getFacebookPageDetails(BusinessFBPage facebookPage);

	/**
	 * Finds all the raw facebook pages connected to an enterpriseId and returns a
	 * key-val map of (FacebookPageId, FacebookPageMetadataDTO)
	 * @param enterpriseId String value of enterpriseId
	 * @return Map of (FacebookPageId, FacebookPageMetadataDTO)
	 */
	Map<String, FacebookPageMetadataDTO> getFbPageMapDtoByEnterpriseId(String enterpriseId);

	/**
	 * Finds all the raw facebook pages connected to an enterpriseId and returns a
	 * key-val map of (FacebookPageId, FacebookPageMetadataDTO)
	 * @param enterpriseId String value of enterpriseId
	 * @return Map of (FacebookPageId, FacebookPageMetadataDTO)
	 */
	Map<String, BusinessFBPage> getFbPageMapByEnterpriseId(String enterpriseId);

	/**
	 * Finds all the raw facebook pages details connected to a Filter request and returns a
	 * object of @see com.birdeye.social.dto.SocialPageDTO
	 * @param filterPageRequest String value of enterpriseId
	 * @return Object of @see com.birdeye.social.dto.SocialPageDTO
	 */

	SocialPageDTO getFBPageDTO(FilterPageRequest filterPageRequest);

	/**
	 * Clear facebook page details cache by facebook page id
	 * @param facebookPageId String value of facebookPageId
	 */
	void clearFbPageCache(String facebookPageId);

	/**
	 * Clear cache of fbPages details associated with an enterpriseId
	 * @param enterpriseId String value of enterpriseId
	 */
	void clearFbPagesEnterpriseCache(String enterpriseId);

	/**
	 * Clear cache of fbPages details associated with a filterPage
	 * @param filterPage
	 */
	void clearFbPageByFilter(FilterPageRequest filterPage);

	/**
	 * Clear whole facebook page details cache
	 */
	void clearAllCache();

    boolean getFacebookPermission(Integer accountId, List<String> modules);
	boolean getFacebookPostPermission(List<BusinessFBPage> fbPages, List<String> modules);

    Map<String, Boolean> getFacebookPostPermissionPageWise(List<BusinessFBPage> businessFBPages, List<String> modules);
}
