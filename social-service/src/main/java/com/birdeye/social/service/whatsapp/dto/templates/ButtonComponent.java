package com.birdeye.social.service.whatsapp.dto.templates;

import com.birdeye.social.service.whatsapp.dto.templates.type.ComponentType;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

/**
 * The type Button component.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ButtonComponent extends Component<ButtonComponent> {


    private List<Button> buttons;


    /**
     * Instantiates a new Button component.
     */
    public ButtonComponent() {
        super(ComponentType.BUTTONS);
    }


    /**
     * Gets buttons.
     *
     * @return the buttons
     */
    public List<Button> getButtons() {
        return buttons;
    }

    /**
     * Sets buttons.
     *
     * @param buttons the buttons
     * @return the buttons
     */
    public ButtonComponent setButtons(List<Button> buttons) {
        this.buttons = buttons;
        return this;
    }

    /**
     * Add button button component.
     *
     * @param button the button
     * @return the button component
     */
    public ButtonComponent addButton(Button button) {
        if (this.buttons == null) this.buttons = new ArrayList<>();
        this.buttons.add(button);
        return this;
    }
}
