package com.birdeye.social.service.btp;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.btp.BTPCategory;
import com.birdeye.social.constant.btp.BTPChannel;
import com.birdeye.social.constant.btp.BTPCountry;
import com.birdeye.social.constant.btp.BTPReportType;
import com.birdeye.social.entities.SocialPostAdditionalInfo;
import com.birdeye.social.entities.btp.BTPHeatMapData;
import com.birdeye.social.entities.btp.BTPPriorityLookup;
import com.birdeye.social.entities.btp.BTPDayWiseData;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.btp.BTPPriorityService;
import com.birdeye.social.external.btp.BTPDayWiseService;
import com.birdeye.social.external.btp.BTPHeatMapService;
import com.birdeye.social.external.request.btprequest.BTPPriorityRequest;
import com.birdeye.social.external.request.btprequest.DayWiseDataPoints;
import com.birdeye.social.external.request.btprequest.SocialAIRequest;
import com.birdeye.social.external.request.btprequest.SocialBTPRequest;
import com.birdeye.social.external.response.btp.*;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.external.socialpostadditionalinfo.SocialPostAdditionalInfoService;
import com.birdeye.social.model.SocialPostAdditionalRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.birdeye.social.service.DtoToEntityConverter.*;
import static com.birdeye.social.service.EntityToDtoConverter.convertToBTPHeatMapResponse;
import static com.birdeye.social.utils.DateTimeUtils.*;

@Service
public class BestTimeToPostServiceImpl implements BestTimeToPostService {

    private static final Logger logger = LoggerFactory.getLogger(BestTimeToPostServiceImpl.class);
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String yyyy_mm_dd = "MM/dd/yyyy";
    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
    public static final String HH_MM = "HH:mm";
    public static final String UTC = "UTC";

    @Autowired
    private BTPHeatMapService btpHeatMapService;

    @Autowired
    private BTPDayWiseService btpDayWiseService;

    @Autowired
    private BTPPriorityService btpCategoryService;

    @Autowired
    private SocialPostAdditionalInfoService socialPostAdditionalInfoService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Override
    public void saveHeatMapData(SocialAIRequest socialAIRequest) {
        logger.info("Social AI Request to save heat map data :{}",socialAIRequest);
        if(Objects.isNull(socialAIRequest) || MapUtils.isEmpty(socialAIRequest.getDataPoints())){
            logger.info("Request can not be null or empty from AI");
            return;
        }
        BTPHeatMapData btpHeatMapData = convertAiDataToBTPHeatMapEntity(socialAIRequest);
        btpHeatMapService.save(btpHeatMapData);
    }

    @Override
    public void saveHeatMapData(SocialAIRequest socialAIRequest,int numberOfWeek) {
        logger.info("Social AI Request to save heat map data :{}",socialAIRequest);
        if(Objects.isNull(socialAIRequest) || MapUtils.isEmpty(socialAIRequest.getDataPoints())){
            logger.info("Request can not be null or empty from AI");
            return;
        }
        socialAIRequest.setWeekStartDate(DateUtils.addWeeks(socialAIRequest.getWeekStartDate(),numberOfWeek));
        btpHeatMapService.save(convertAiDataToBTPHeatMapEntity(socialAIRequest));
    }

    @Override
    public void saveDailyStats(SocialAIRequest socialAIRequest) {
        logger.info("Social AI Request to save daily stats data :{}",socialAIRequest);
        if(Objects.isNull(socialAIRequest) || MapUtils.isEmpty(socialAIRequest.getDataPoints())){
            logger.info("Request can not or empty be null from AI");
            return;
        }
        Integer noOfWeeks = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBTPSetWeekCount();
        socialAIRequest.setWeekStartDate(DateUtils.addWeeks(socialAIRequest.getWeekStartDate(),noOfWeeks));
        saveDailyBTPStats(socialAIRequest);
    }

    private void saveDailyBTPStats(SocialAIRequest socialAIRequest) {
        BTPDayWiseData btpDayWiseData = convertAIDataToBTPDayWiseData(socialAIRequest);

        String startDate = formatTime(YYYY_MM_DD, socialAIRequest.getWeekStartDate());
        Map<String, DayWiseDataPoints> timeVsDayOfWeekMap = socialAIRequest.getDataPoints();
        timeVsDayOfWeekMap.forEach((key, value) -> { // use constructor and deep copy
            logger.info("Save data for day and time:{}", key);
            Date currentDate = getDate(startDate,key);
            btpDayWiseData.setId(null);
            btpDayWiseData.setDay(currentDate);
            btpDayWiseData.setValue(value.getSUN());
            btpDayWiseService.save(btpDayWiseData);
            btpDayWiseData.setId(null);
            btpDayWiseData.setDay(DateUtils.addDays(currentDate,1));
            btpDayWiseData.setValue(value.getMON());
            btpDayWiseService.save(btpDayWiseData);
            btpDayWiseData.setId(null);
            btpDayWiseData.setDay(DateUtils.addDays(currentDate,2));
            btpDayWiseData.setValue(value.getTUE());
            btpDayWiseService.save(btpDayWiseData);
            btpDayWiseData.setId(null);
            btpDayWiseData.setDay(DateUtils.addDays(currentDate,3));
            btpDayWiseData.setValue(value.getWED());
            btpDayWiseService.save(btpDayWiseData);
            btpDayWiseData.setId(null);
            btpDayWiseData.setDay(DateUtils.addDays(currentDate,4));
            btpDayWiseData.setValue(value.getTHU());
            btpDayWiseService.save(btpDayWiseData);
            btpDayWiseData.setId(null);
            btpDayWiseData.setDay(DateUtils.addDays(currentDate,5));
            btpDayWiseData.setValue(value.getFRI());
            btpDayWiseService.save(btpDayWiseData);
            btpDayWiseData.setId(null);
            btpDayWiseData.setDay(DateUtils.addDays(currentDate,6));
            btpDayWiseData.setValue(value.getSAT());
            btpDayWiseService.save(btpDayWiseData);
        });
    }

    @Override
    public void saveDailyStats(SocialAIRequest socialAIRequest, int numberOfWeek) {
        logger.info("Social AI Request to save daily stats data :{}",socialAIRequest);
        if(Objects.isNull(socialAIRequest) || MapUtils.isEmpty(socialAIRequest.getDataPoints())){
            logger.info("Request can not or empty be null from AI");
            return;
        }
        socialAIRequest.setWeekStartDate(DateUtils.addWeeks(socialAIRequest.getWeekStartDate(),numberOfWeek));
        saveDailyBTPStats(socialAIRequest);
    }

    @Override
    public SocialBTPHeatMapResponse getHeatMapData(SocialBTPRequest socialBTPRequest) {
        Date currentTime = Objects.isNull(socialBTPRequest.getCurrentTime()) ? new Date()
                : new Date(socialBTPRequest.getCurrentTime());
        Date weekEndTime = getEndOfWeekFromCurrentDate(currentTime,1);
        Date weekStartTime = getStartOfWeekFromCurrentDate(currentTime);
        logger.info("Week start time : {} and Week end time :{}",weekStartTime,weekEndTime);
        BTPPriorityLookup btpPriorityLookup =
                btpCategoryService.findByCountryAndChannelAndCategoryAndReportType(socialBTPRequest.getCountryCode(),
                        socialBTPRequest.getChannels(),socialBTPRequest.getIndustry(),socialBTPRequest.getReportType());
        logger.info("Priority lookup for request : {} is :{}",socialBTPRequest,btpPriorityLookup);
        BTPHeatMapData btpHeatMapData =
                btpHeatMapService.findByCountryAndCategoryAndChannelAndReportTypeAndWeekStartDateGreaterThanAndWeekEndDateLessThan
                        (btpPriorityLookup,weekStartTime,weekEndTime);
        if(Objects.isNull(btpHeatMapData) || Objects.isNull(btpHeatMapData.getBtpDataPointsMetaData())){ // check NPE
            logger.info("No data found for request :{}",socialBTPRequest); // TODO: 23/07/24 no data found error code
            throw new BirdeyeSocialException(ErrorCodes.NO_DATA_FOUND,"No data found");
        }
        return convertToBTPHeatMapResponse(btpHeatMapData);
    }

    @Override
    public ScheduleBTPResponse getScheduleData(SocialBTPRequest socialBTPRequest,String timezoneId) {
        ScheduleBTPResponse scheduleBTPResponse = new ScheduleBTPResponse();
        Date currentTime = new Date(socialBTPRequest.getCurrentTime());
        Date fromTime = formatTimeWithPattern(currentTime,timezoneId);
        logger.info("Current time : {}",fromTime);
        Date toTime = DateUtils.addDays(getNextWeekEndDate(),1);
        logger.info("To time : {}",toTime);
        Map<String,List<TimeAndReportData>> combinedBestTime =
                setGlobalLevelData(fromTime,timezoneId,socialBTPRequest.getCountryCode(),toTime);
        scheduleBTPResponse.setCombinedBestTime(combinedBestTime);
        ChannelWiseResponse channelWiseResponse = new ChannelWiseResponse();
        getChannelWiseDataForSchedulePost(socialBTPRequest, timezoneId, channelWiseResponse, fromTime, toTime, combinedBestTime);
        scheduleBTPResponse.setChannelWiseBestTime(channelWiseResponse);
        return scheduleBTPResponse;
    }

    private void getChannelWiseDataForSchedulePost(SocialBTPRequest socialBTPRequest, String timezoneId,
                                                   ChannelWiseResponse channelWiseResponse, Date fromTime, Date toTime,
                                                   Map<String, List<TimeAndReportData>> combinedBestTime) {
        socialBTPRequest.getChannels().parallelStream().forEach(channel -> {
            BTPChannel btpChannel = BTPChannel.getSocialChannel(channel);
            if(Objects.isNull(btpChannel)) btpChannel = BTPChannel.GLOBAL;
            logger.info("Channel found with request :{}",btpChannel);
            switch (btpChannel){
                case FACEBOOK:
                    channelWiseResponse.setFacebook(mapOfDayAndTime(socialBTPRequest,channel, fromTime, toTime, timezoneId));
                    break;
                case INSTAGRAM:
                    channelWiseResponse.setInstagram(mapOfDayAndTime(socialBTPRequest,channel, fromTime, toTime, timezoneId));
                    break;
                case TWITTER:
                    channelWiseResponse.setTwitter(mapOfDayAndTime(socialBTPRequest,channel, fromTime, toTime, timezoneId));
                    break;
                case LINKEDIN:
                    channelWiseResponse.setLinkedin(mapOfDayAndTime(socialBTPRequest,channel, fromTime, toTime, timezoneId));
                    break;
                case GOOGLE:
                    channelWiseResponse.setGoogle(combinedBestTime);
                    break;
                case YOUTUBE:
                    channelWiseResponse.setYoutube(combinedBestTime);
                    break;
                case TIKTOK:
                    channelWiseResponse.setTiktok(combinedBestTime);
                default:
                    logger.info("No channel found :{}",btpChannel);
                    break;
            }
        });
    }

    private Map<String, List<TimeAndReportData>> setGlobalLevelData(Date fromTime,String timezoneId,String countryCode,Date toTime) {
        String getCountryCode = BTPCountry.getCoutryByString(countryCode);
        BTPPriorityLookup engLookUp = btpCategoryService.findByCountryAndChannelAndCategoryAndReportType(getCountryCode,
                    new LinkedList<>(), BTPCategory.GLOBAL.name(),BTPReportType.INCREASE_ENGAGEMENT.name());
        BTPPriorityLookup impLookUp = btpCategoryService.findByCountryAndChannelAndCategoryAndReportType(getCountryCode,
                    new LinkedList<>(), BTPCategory.GLOBAL.name(),BTPReportType.BUILD_AWARENESS.name());
        return getStringListMap(fromTime,toTime, engLookUp, impLookUp,timezoneId);
    }

    @NotNull
    private Map<String, List<TimeAndReportData>> getStringListMap(Date fromTime,Date toTime, BTPPriorityLookup engLookUp,
                                                                  BTPPriorityLookup impLookUp,String timezoneId) {
        logger.info("Get engagement time :{}",System.currentTimeMillis());
        boolean btpCacheFlag = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBTPCacheFlag();
        List<BTPDayWiseData> dayWiseEngData;
        if(btpCacheFlag) {
            dayWiseEngData =
                    btpDayWiseService.findByCountryAndChannelAndCategoryAndGreaterThanFromTimeAndReportType(
                            engLookUp, roundedDate(fromTime), toTime, BTPReportType.INCREASE_ENGAGEMENT.name(), 1);
        } else {
            dayWiseEngData =
                    btpDayWiseService.findByCountryAndChannelAndCategoryAndGreaterThanFromTimeAndReportTypeDB(
                            engLookUp, roundedDate(fromTime), toTime, BTPReportType.INCREASE_ENGAGEMENT.name(), 1);
        }
        logger.info("After engagement time :{}",System.currentTimeMillis());
        Map<String,List<TimeAndReportData>> mapOfDateAndValues = new HashMap<>();
        if(CollectionUtils.isNotEmpty(dayWiseEngData)){
            logger.info("Data found for the engagement lookup");
            setKeyAndValueOfDayWiseData(dayWiseEngData,mapOfDateAndValues, BTPReportType.INCREASE_ENGAGEMENT.name(),timezoneId);
        }
        List<BTPDayWiseData> dayWiseImpData;
        if(btpCacheFlag) {
            dayWiseImpData =
                    btpDayWiseService.findByCountryAndChannelAndCategoryAndGreaterThanFromTimeAndReportType(
                            impLookUp, roundedDate(fromTime), toTime, BTPReportType.BUILD_AWARENESS.name(), 2);
        } else {
            dayWiseImpData =
                    btpDayWiseService.findByCountryAndChannelAndCategoryAndGreaterThanFromTimeAndReportTypeDB(
                            impLookUp, roundedDate(fromTime), toTime, BTPReportType.BUILD_AWARENESS.name(), 2);
        }
        if(CollectionUtils.isNotEmpty(dayWiseImpData)){
            logger.info("Data found for the impression lookup");
            setKeyAndValueOfDayWiseData(dayWiseImpData,mapOfDateAndValues, BTPReportType.BUILD_AWARENESS.name(),timezoneId);
        }
        return mapOfDateAndValues;
    }

    private Map<String,List<TimeAndReportData>> mapOfDayAndTime(SocialBTPRequest socialBTPRequest,
                                                                String channel,Date fromTime,Date toTime,String timezoneId) {
        BTPPriorityLookup btpPriorityLookupForEngagement =
                btpCategoryService.findByCountryAndChannelAndCategoryAndReportType(socialBTPRequest.getCountryCode()
                        ,Collections.singletonList(channel), socialBTPRequest.getIndustry(), BTPReportType.INCREASE_ENGAGEMENT.name());
        logger.info("Engagement look up :{} for channel:{}",btpPriorityLookupForEngagement,channel);
        BTPPriorityLookup btpPriorityLookupForImpression =
                btpCategoryService.findByCountryAndChannelAndCategoryAndReportType(socialBTPRequest.getCountryCode()
                        ,Collections.singletonList(channel), socialBTPRequest.getIndustry(),BTPReportType.BUILD_AWARENESS.name());
        logger.info("Impression look up :{} for channel:{}",btpPriorityLookupForImpression,channel);
        return getStringListMap(fromTime,toTime, btpPriorityLookupForEngagement, btpPriorityLookupForImpression,timezoneId);
    }

    private Date getStartOfWeekFromCurrentDate(Date currentDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.SUNDAY);
        calendar.setTime(currentDate);
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
        return DateUtils.addDays(calendar.getTime(),-1);
    }

    private Date roundedDate(Date originalTime) {
        logger.info("Initial date: {}", originalTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(originalTime);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date roundedDate = calendar.getTime();
        logger.info("Rounded date: {}", roundedDate);
        return roundedDate;
    }

    @Override
    public SocialBTPCalenderResponse getScheduleTimePerDayForCalender(SocialBTPRequest socialBTPRequest,String timezoneId) {
        SocialBTPCalenderResponse socialBTPCalenderResponse = new SocialBTPCalenderResponse();
        Date currentDateAndTime = new Date(socialBTPRequest.getCurrentTime());
        logger.info("From time :{}",currentDateAndTime);
        Date currentTime = formatTimeWithPattern(currentDateAndTime,timezoneId);
        Date toTime = DateUtils.addDays(getNextWeeksEndDate(5),1);
        logger.info("To time :{}",toTime);
        String reportType = BTPReportType.INCREASE_ENGAGEMENT.name();
        BTPPriorityLookup lookup =
                btpCategoryService.findByCountryAndChannelAndCategoryAndReportType(socialBTPRequest.getCountryCode(),
                        socialBTPRequest.getChannels(), socialBTPRequest.getIndustry(), reportType);
        logger.info("Priority look up response :{}",lookup);

        // Optimized data retrieval and processing
        Map<String, List<String>> timeSlots = getOptimizedTimeSlots(lookup, reportType, currentDateAndTime, toTime, currentTime, timezoneId);

        socialBTPCalenderResponse.setTimeSlots(timeSlots);
        socialBTPCalenderResponse.setCategory(lookup.getCategory());
        return socialBTPCalenderResponse;
    }

    /**
     * Optimized method to retrieve and process time slots data
     * Reduces database query complexity and improves loop processing efficiency
     */
    private Map<String, List<String>> getOptimizedTimeSlots(BTPPriorityLookup lookup, String reportType,
                                                           Date currentDateAndTime, Date toTime, Date currentTime, String timezoneId) {
        boolean btpCacheFlag = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBTPCacheFlag();
        List<BTPDayWiseData> btpDayWiseDataList;

        if(btpCacheFlag) {
            // Use optimized query with window functions for better performance
            btpDayWiseDataList = btpDayWiseService.findByTop3TimesPerDayOptimized(lookup, reportType, roundedDate(currentDateAndTime), toTime);
        } else {
            // Fallback to optimized query without cache
            btpDayWiseDataList = btpDayWiseService.findByTop3TimesPerDayOptimized(lookup, reportType, roundedDate(currentDateAndTime), toTime);
        }

        return processTimeSlotDataOptimized(btpDayWiseDataList, currentTime, timezoneId);
    }

    /**
     * Optimized processing of time slot data with reduced computational overhead
     * - Pre-calculates timezone offset
     * - Uses efficient data structures
     * - Minimizes repeated operations in loop
     */
    private Map<String, List<String>> processTimeSlotDataOptimized(List<BTPDayWiseData> btpDayWiseDataList,
                                                                  Date currentTime, String timezoneId) {
        if (CollectionUtils.isEmpty(btpDayWiseDataList)) {
            return new HashMap<>();
        }

        Map<String, List<String>> timeSlots = new HashMap<>();
        int offset = getOffset(timezoneId);
        logger.info("Offset value :{}", offset);

        // Pre-filter and process data efficiently
        for(BTPDayWiseData data : btpDayWiseDataList) {
            // Skip past dates early to avoid unnecessary processing
            if(data.getDay().before(currentTime)) {
                continue;
            }

            Date currentDateTime = DateUtils.addMinutes(data.getDay(), -offset);
            logger.info("Date and time : {} and value : {}", data.getDay(), data.getValue());

            String date = formatTime(yyyy_mm_dd, currentDateTime);
            String time = formatTime(HH_MM, currentDateTime);

            // Use computeIfAbsent for cleaner code and better performance
            List<String> timeSlotList = timeSlots.computeIfAbsent(date, k -> new ArrayList<>());

            // Skip if already have 3 time slots for this date
            if(timeSlotList.size() >= 3) {
                continue;
            }

            timeSlotList.add(time);
        }

        return timeSlots;
    }
    @Override
    public SocialBTPCalenderResponse getScheduleTimePerMonthForAIPosts(SocialBTPRequest socialBTPRequest, String timezoneId, Date postGenEndDate) {
        SocialBTPCalenderResponse socialBTPCalenderResponse = new SocialBTPCalenderResponse();
        Date currentDateAndTime = new Date(socialBTPRequest.getCurrentTime());
        logger.info("From time :{}",currentDateAndTime);
        setDatesToExtremeOfTime(currentDateAndTime,postGenEndDate);
        String reportType = BTPReportType.INCREASE_ENGAGEMENT.name();
        BTPPriorityLookup lookup =
                btpCategoryService.findByCountryAndChannelAndCategoryAndReportType(socialBTPRequest.getCountryCode(),
                        socialBTPRequest.getChannels(), socialBTPRequest.getIndustry(), reportType);
        logger.info("Priority look up response :{}",lookup);
        List<BTPDayWiseData> btpDayWiseDataList = btpDayWiseService.findByTop3TimesPerDay(lookup,reportType,currentDateAndTime,postGenEndDate);
        Map<String, List<String>> timeSlots = new HashMap<>();
        for(BTPDayWiseData data : btpDayWiseDataList){
            Date currentDateTime = data.getDay();
            logger.info("Date and time : {} and value : {}",data.getDay(),data.getValue());
            String date = formatTime(yyyy_mm_dd,currentDateTime);
            String time = formatTime(HH_MM,currentDateTime);
            if(data.getDay().before(currentDateAndTime)){
                continue;
            }
            List<String> timeSlotList = timeSlots.get(date);
            if(CollectionUtils.isNotEmpty(timeSlotList) && timeSlotList.size() == 3){
                continue;
            }
            if(CollectionUtils.isEmpty(timeSlotList)){
                timeSlotList = new ArrayList<>();
            }
            timeSlotList.add(time);
            timeSlots.put(date,timeSlotList);
        }
        socialBTPCalenderResponse.setTimeSlots(timeSlots);
        socialBTPCalenderResponse.setCategory(lookup.getCategory());
        return socialBTPCalenderResponse;
    }


    @Override
    public BTPTop3Times getScheduleTimePerWeek(SocialBTPRequest socialBTPRequest,String timezoneId) {
        BTPTop3Times btpTop3Times = new BTPTop3Times();
        Map<Date,String> dateTimeMap = new TreeMap<>();
        Date currentDateAndTime = new Date(socialBTPRequest.getCurrentTime());
        Date currentTime = formatTimeWithPattern(currentDateAndTime,timezoneId);
        Date weekEndDate = DateUtils.addDays(currentTime,7);
        String reportType = BTPReportType.INCREASE_ENGAGEMENT.name().equalsIgnoreCase(socialBTPRequest.getReportType())
                ? BTPReportType.INCREASE_ENGAGEMENT.name() :BTPReportType.BUILD_AWARENESS.name() ;
        BTPPriorityLookup lookup =
                btpCategoryService.findByCountryAndChannelAndCategoryAndReportType(socialBTPRequest.getCountryCode(),
                        socialBTPRequest.getChannels(), socialBTPRequest.getIndustry(), reportType);
        logger.info("Priority look up response :{}",lookup);
        List<BTPDayWiseData> btpDayWiseDataList =
                btpDayWiseService.findByTop3TimesPerWeek(lookup,reportType,currentTime,weekEndDate);
        if(CollectionUtils.isEmpty(btpDayWiseDataList)){
            logger.info("No data found for the request :{}",socialBTPRequest);
            return btpTop3Times;
        }
        for(BTPDayWiseData data : btpDayWiseDataList){
            if(!dateTimeMap.containsKey(formatDate(data.getDay(),yyyy_mm_dd))) {
                dateTimeMap.put(formatDate(data.getDay(), yyyy_mm_dd), formatTime(YYYY_MM_DD_HH_MM, data.getDay()));
            }
            if(dateTimeMap.size() == 3) break;
        }
        btpTop3Times.setTimeSlots(new ArrayList<>(dateTimeMap.values()));
        btpTop3Times.setCountry(lookup.getCountry());
        return btpTop3Times;
    }

    @Override
    public ScheduleBTPResponse getScheduleDataForReseller(SocialBTPRequest socialBTPRequest,String timeZone) {
        return getScheduleData(socialBTPRequest,timeZone);
    }

    @Override
    public void deletePriority(Integer id) {
        btpCategoryService.deleteById(id);
    }

    @Override
    public void addOrUpdatePriority(BTPPriorityRequest btpPriorityRequest) {
        BTPPriorityLookup btpPriorityLookup;
        if(Objects.isNull(btpPriorityRequest.getId())){
            btpPriorityLookup = new BTPPriorityLookup();
        }else{
            btpPriorityLookup = btpCategoryService.findById(btpPriorityRequest.getId());
        }
        convertToBTpPriority(btpPriorityLookup,btpPriorityRequest);
        btpCategoryService.save(btpPriorityLookup);
    }

    @Override
    public void addOrUpdateSocialPostAdditionalInfo(SocialPostAdditionalRequest request) {
        logger.info("Request received to update post with Additional info : {}",request);
        if(Objects.isNull(request) || Objects.isNull(request.getSocialPostId())) {
            logger.info("Request can not be null :{}",request);
            return;
        }
        SocialPostAdditionalInfo socialPostAdditionalInfo =
                socialPostAdditionalInfoService.findById(request.getSocialPostId());
        if(Objects.isNull(socialPostAdditionalInfo))
            socialPostAdditionalInfo = new SocialPostAdditionalInfo();
        socialPostAdditionalInfo.setIsBtpPost(request.isCreatedOnBTP() ? 1 : 0);
        socialPostAdditionalInfo.setSocialPostId(request.getSocialPostId());
        socialPostAdditionalInfoService.saveInfo(socialPostAdditionalInfo);
    }

    @Override
    public void migrateBtp(SocialAIRequest socialAIRequest) {
        kafkaProducerService.sendObjectV1("ai-best-time-to-post", socialAIRequest);
    }

    private void setKeyAndValueOfDayWiseData(List<BTPDayWiseData> dayWiseEngData,
                                             Map<String, List<TimeAndReportData>> mapOfDateAndValues,
                                             String reportType,String timezoneId) {
        int offset = getOffset(timezoneId);
        for(BTPDayWiseData data : dayWiseEngData) {
            logger.info("Date from DB : {}",data.getDay());
            Date currentDate = DateUtils.addMinutes(data.getDay(),-offset);
            String date = formatTime(yyyy_mm_dd,currentDate);
            String time = formatTime(HH_MM,currentDate);
            logger.info("Date :{} and time :{}",date,time);
            List<TimeAndReportData> timeAndReportDataList = new ArrayList<>();
            if(MapUtils.isNotEmpty(mapOfDateAndValues) && mapOfDateAndValues.containsKey(date))
                timeAndReportDataList = mapOfDateAndValues.get(date);
            if(timeAndReportDataList.size() == 2){
                continue;
            }
            if(CollectionUtils.isNotEmpty(timeAndReportDataList)){
                TimeAndReportData report = timeAndReportDataList.get(0);
                if(report.getTime().equalsIgnoreCase(time)
                        || report.getReason().equalsIgnoreCase(reportType)){
                    logger.info("Time is already added : {} with engagement report",time);
                    continue;
                }
            }
            TimeAndReportData reportData = new TimeAndReportData();
            reportData.setReason(reportType);
            reportData.setTime(time);
            timeAndReportDataList.add(reportData);
            mapOfDateAndValues.put(date,timeAndReportDataList);
        }
    }
    public static Date formatTimeWithPattern(Date currentTime, String timezoneId) {
        int offset = getOffset(timezoneId);
        currentTime = DateUtils.addMinutes(currentTime,offset);
        SimpleDateFormat utcSimpleDateFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM);
        String date = utcSimpleDateFormat.format(currentTime);
        try {
            return utcSimpleDateFormat.parse(date);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
}
