package com.birdeye.social.service;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.GMBLocationJobStatus;
import com.birdeye.social.constant.SocialAlertTypeEnum;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BrokenIntegrationRepo;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.entities.BrokenIntegration;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.external.service.KafkaProducerService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("BrokenIntegrationService")
public class BrokenIntegrationServiceImpl implements IBrokenIntegrationService {

    @Autowired
    private BrokenIntegrationRepo brokenIntegrationRepo;

    @Autowired
    private KafkaProducerService producer;

    @Autowired
    private BusinessGMBLocationRawRepository socialGMBRepo;

    @Autowired
    private SocialFBPageRepository socialFBPageRepository;

    @Override
    public void save(Long parentId, String channel, String source) {
        BrokenIntegration brokenIntegration = new BrokenIntegration();
        brokenIntegration.setChannel(channel);
        brokenIntegration.setEnterpriseId(parentId);
        brokenIntegration.setSource(source);
        brokenIntegration.setStatus(GMBLocationJobStatus.IN_PROGRESS.name());
        brokenIntegrationRepo.save(brokenIntegration);
    }

    
    @Override
    public void saveAll(List<BrokenIntegration> brokenIntegrations) {
        brokenIntegrationRepo.save(brokenIntegrations);
    }
    
    @Override
    public Set<Long> fetchInProgessDataWithSource(String source, Set<Long> businessIds) {
        Set<Long> brokenIntegrations = brokenIntegrationRepo.findDistinctByEntStatusAndSource(GMBLocationJobStatus.IN_PROGRESS.name(),source, businessIds);
        return brokenIntegrations;
    }

    @Override
    public void pushValidIntegrationStatus(Long enterpriseId, String source, Integer rawId, Integer isValid, String pageId) {
        if(Objects.nonNull(enterpriseId) && Objects.equals(isValid,0)) {
            Boolean isMapped = false;
            if(SocialChannel.GMB.getName().equalsIgnoreCase(source) && pageId != null) {
                List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations = socialGMBRepo.findByLocationId(pageId);
                if (CollectionUtils.isNotEmpty(businessGoogleMyBusinessLocations) && Objects.nonNull(enterpriseId)) {
                    isMapped = true;
                }
            } else if( SocialChannel.FACEBOOK.getName().equalsIgnoreCase(source) && pageId != null) {
                List<BusinessFBPage> businessFBPages = socialFBPageRepository.findByFacebookPageId(pageId);
                if (CollectionUtils.isNotEmpty(businessFBPages) && Objects.nonNull(enterpriseId)) {
                    isMapped = true;
                }
            }
            if(isMapped.equals(true)) {
                save(enterpriseId, source, SocialAlertTypeEnum.DISCONNECTED.name());
            }

            Map<String, Object> data = new HashMap<>();
            data.put("enterpriseId", enterpriseId);
            data.put("source", source);
            data.put("rawId", rawId);
            data.put("isValid", isValid);
            data.put("isMapped",isMapped);
            data.put("integrationId",pageId);
            producer.sendWithKey(Constants.INIT_DISCONNECTED_PAGE, enterpriseId.toString(), data);
        }
    }

    @Override
    public void updateStatus(String status, Long enterpriseId) {
        brokenIntegrationRepo.updateStatus(status,enterpriseId);
    }
}
