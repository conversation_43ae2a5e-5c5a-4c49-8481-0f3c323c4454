package com.birdeye.social.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.birdeye.platform.dto.Rules;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.dailyDigest.DailyDigestRequest;
import com.birdeye.social.dto.dailyDigest.DailyDigestResponse;
import com.birdeye.social.entities.BusinessKeywordMapping;
import com.birdeye.social.entities.Mention;
import com.birdeye.social.entities.WebhoseRaw;
import com.birdeye.social.model.GNIPActivity;
import com.birdeye.social.model.listen.MigrationRequest;
import com.birdeye.social.sro.MentionListResponse;
import com.birdeye.social.sro.MentionsFilterRequest;
import com.birdeye.social.webhose.request.WebhoseFetchRequest;

import freemarker.template.TemplateException;

public interface SocialMentionService {
	
	//TODO: GNIPActivity should be replaced with generic SocialActivity.
	// This needs to be done at producer level while pushing events.
//	public void save(GNIPActivity socialActivity);

	public void createGNIPRuleForTwitter(Integer businessId, Integer userId, Rules rules);
	
	public Rules getAllRules(Integer businessId);
	
	public void deleteGNIPRule(Integer businessId, Integer userId, Integer ruleId);
	
	public BusinessKeywordMapping getRule(Integer ruleId);
	
	public void refreshRule(Integer businessId);
	
	public MentionListResponse listMentions(MentionsFilterRequest request) throws IOException, TemplateException;
    
	public void fetchWebhoseMentions(WebhoseFetchRequest webhoseFetchRequest);

	void saveWebhoseMention(WebhoseRaw post);
	
	public void saveWebhoseMentions(WebhoseMentionSaveRequest webhoseMentionSaveRequest);

	List<WebhoseRaw> filterNewMentions(List<WebhoseRaw> postList, Integer businessId);
	
	public void syncMentions(Integer enterpriseId, String sourceType);

	void updateEnterpriseBusinessId(Integer sourceEnterpriseNumber, Integer targetEnterpriseNumber);

	void inactivateKeywords(Integer businessId);

	void inactiveRuleAndKeywords(Integer businessId);

	void activateKeywords(Integer businessId);

	void saveMentionToES(ESReviewDTO esReviewDTO);

	void publishMentionToES(Mention mention);

//	void migrateEsIndex(MigrationRequest request);

//	void saveMentionToES(MentionEsRequest mentionEsRequest);

	MentionTicketResponseList getMentionData(MentionTicketRequest ticketRequest);

	void getMentionFromId(Long id);
	
	DailyDigestResponse getDailyDigestReport(DailyDigestRequest request);

	void saveMentionToESList(Map<String,List<ESReviewDTO>> esReviewDTOList);
}
