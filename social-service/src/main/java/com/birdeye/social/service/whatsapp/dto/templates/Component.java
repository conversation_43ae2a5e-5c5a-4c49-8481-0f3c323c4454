package com.birdeye.social.service.whatsapp.dto.templates;

import com.birdeye.social.service.whatsapp.dto.templates.type.ComponentType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;

/**
 * The parts of the message template.
 *
 * @param <T> the type parameter
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({@JsonSubTypes.Type(value = ButtonComponent.class, name = "BUTTONS"), //
        @JsonSubTypes.Type(value = FooterComponent.class, name = "FOOTER"), //
        @JsonSubTypes.Type(value = HeaderComponent.class, name = "HEADER"), //
        @JsonSubTypes.Type(value = BodyComponent.class, name = "BODY")})//
public class Component<T extends Component<T>> {
    /**
     * <b>Required.</b>
     *
     * <ul>Values:
     * <li>BODY</li>
     * <li>HEADER</li>
     * <li>FOOTER</li>
     * <li>BUTTONS</li>
     * </ul>
     */
    private ComponentType type;
    private String text;
    private Example example;

    protected Component() {

    }

    protected Component(ComponentType type) {
        this.type = type;
    }

    public String getText() {
        return text;
    }


    public T setText(String text) {
        this.text = text;
        return (T) this;
    }

    public Example getExample() {
        return example;
    }

    public T setExample(Example example) {
        this.example = example;
        return (T) this;
    }

    public ComponentType getType() {
        return type;
    }

    public Component<T> setType(ComponentType type) {
        this.type = type;
        return this;
    }
}
