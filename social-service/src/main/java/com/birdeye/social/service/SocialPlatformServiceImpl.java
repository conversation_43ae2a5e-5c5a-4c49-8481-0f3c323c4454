package com.birdeye.social.service;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.model.ChannelPageRemoved;
import com.birdeye.social.sro.LocationPageMappingRequest;
import com.birdeye.social.sro.SocialChannelSyncRequest;
import com.birdeye.social.sro.SocialEsValidRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class SocialPlatformServiceImpl implements ISocialPlatformService{

    private static final Logger LOG = LoggerFactory.getLogger(SocialPlatformServiceImpl.class);

    @Autowired
    private GoogleSocialAccountService		googleSocialAccountService;

    @Autowired
    private FacebookSocialAccountService	fbSocialAccountService;
    
    @Autowired
    private TwitterSocialAccountService	twitterSocialAccountService;
    
    

    @Override
    public void addMapping(LocationPageMappingRequest locationPageMappingRequest, String channel) {
        LOG.info("Request received to insert mapping for channel {} {}",channel,locationPageMappingRequest);
        if(Objects.isNull(locationPageMappingRequest) || Objects.isNull(locationPageMappingRequest.getLocationId()) || Objects.isNull(locationPageMappingRequest.getPageId())){
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST_INSERT_MAPPING,"invalid request to insert mapping");
        }

        if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
            fbSocialAccountService.saveMapping(locationPageMappingRequest);
        } else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
            googleSocialAccountService.savePlatformMapping(locationPageMappingRequest);
        } else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
            twitterSocialAccountService.savePlatformMapping(locationPageMappingRequest);
        }
    }

    @Override
    public void removeMapping(List<LocationPageMappingRequest> input, String channel) {
        LOG.info("Request received to delete mapping for channel {} {}",channel,input);
        if(Objects.isNull(input) || CollectionUtils.isEmpty(input)){
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST_REMOVE_MAPPING,"invalid request to remove mapping");
        }

        if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
            fbSocialAccountService.removePlatformMapping(input);
        } else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
            googleSocialAccountService.removePlatformMapping(input);
        } else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
            twitterSocialAccountService.removePlatformMapping(input);
        }
    }

    @Override
    public void removePage(List<ChannelPageRemoved> input) {
        LOG.info("Request received to delete entry for {}",input);
        if(Objects.isNull(input) || CollectionUtils.isEmpty(input)){
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST_REMOVE_MAPPING,"invalid request to remove page");
        }

        String channel = input.get(0).getChannelName();

        if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
            fbSocialAccountService.removePlatformEntry(input);
        } else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
            googleSocialAccountService.removePlatformEntry(input);
        } else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
            twitterSocialAccountService.removePlatformEntry(input);
        }
    }

    @Override
    public void updatePageValidity(SocialEsValidRequest socialEsValidRequest) {
        String channel = socialEsValidRequest.getChannel();

        if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
            fbSocialAccountService.updateFbBusinessIsValid(socialEsValidRequest);
        } else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
            googleSocialAccountService.updateGmbBusinessIsValid(socialEsValidRequest);
        } else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
             twitterSocialAccountService.updateTwitterBusinessIsValid(socialEsValidRequest);
        }
    }

    @Override
    public void updatePlatformMapping(SocialChannelSyncRequest socialChannelSyncRequest) {
        String channel = socialChannelSyncRequest.getChannel();
        if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
            fbSocialAccountService.updatePlatformMapping(socialChannelSyncRequest);
        } else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
            googleSocialAccountService.updatePlatformMapping(socialChannelSyncRequest);
        } else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
            twitterSocialAccountService.updatePlatformMapping(socialChannelSyncRequest);
        }
    }
}
