package com.birdeye.social.linkedin;

import com.birdeye.social.constant.EnabledTasks;
import com.birdeye.social.constant.LinkedInTimeGranularity;
import com.birdeye.social.entities.LinkedinRefreshToken;
import com.birdeye.social.external.request.linkedin.*;
import com.birdeye.social.linkedin.organization.LinkedInOrganizationElement;
import com.birdeye.social.linkedin.organization.LinkedInOrganizationResponse;
import com.birdeye.social.linkedin.organization.Role;
import com.birdeye.social.linkedin.response.LinkedInAPIResponse;

import java.io.UnsupportedEncodingException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ILinkedinConnectService {
    String getAuthorizationUrl(LinkedinCreds creds, String redirectUri, String origin) throws URISyntaxException;

    AccessTokenInfo getAccessToken(LinkedinCreds creds, String redirectUri, String authCode);

    Map<String, Object> getLinkedinTokenDetails(String token);

    LinkedinUserProfileInfo getlinkedinUserProfile(String token);

    LinkedinOrgElements linkedinOrganizationInfo(String token,Integer count,Integer start);

    LinkedinOrgPageInfoWrapper linkedinOrganizationInfoForIds(List<Integer> ids,String token);

    LinkedinOrganizationInfo linkedinOrganizationLookUpInfoForIds(List<Integer> profileIds,String token);

    List<String> post(SharePostRequestLinkedin sharePostRequest, String accessToken);

    AccessTokenInfo refreshAccessToken(LinkedinCreds linkedinCreds, LinkedinRefreshToken linkedinRefreshToken);

    LinkedinContactInfo linkedinContactDetails(String token);

    Map<String, Object> registerMedia(RegisterLinkedinMediaRequest registerLinkedinMediaRequest, String accessToken);


    Map<String, Object> registerMediaV2(RegisterLinkedinMediaRequestV2 registerLinkedinMediaRequest, String accessToken, Boolean isVideo);

    boolean uploadMedia(byte[] media, String accessToken, String apiUrl, Boolean isVideo);

    String checkUploadStatus(String urn, String accessToken, Boolean isVideo);

    String checkVideoUploadStatus(String urn, String accessToken, Boolean isVideo);

    List<Elements> getTargetAudienceList(String accessToken, String category);

    LinkedInAPIResponse getPostInsightResponse(String accessToken, String postId, String urn);

    LinkedInAPIResponse getPageInsightResponse(String accessToken, String urn);
    LinkedInAPIResponse getTimeBoundPageInsightResponse(String accessToken, String urn, long startTime, long endTime, LinkedInTimeGranularity time);
    boolean finalizeMediaRequest(FinalizeLinkedinVideoRequest finalizeLinkedinVideoRequest, String accessToken);

    String uploadVideoMedia(byte[] media, String apiUrl,String token);
    boolean linkedinEventSubscriptionInfo(String token, String orgUrn,String personUrn,String developerId, String webhook) throws UnsupportedEncodingException;

    Map<String,Object> linkedinEventUnSubscriptionInfo(String accessToken, String orgUrn, String personUrn,String developerId,Boolean isDelete) throws UnsupportedEncodingException;

    LinkedInOrganizationResponse linkedInOrganizationResponse(String token);

    Boolean checkPermissions(Set<Role> roles, String scope, EnabledTasks module, boolean isProfile);

    LinkedInAPIResponse getPostVideoViewsResponse(String accessToken, String urn);

    LinkedinImagesWrapper linkedinImagesForIds(List<String> imageIds, String token);

    LinkedinImageData linkedinImageFromId(String imageId, String token);
}
