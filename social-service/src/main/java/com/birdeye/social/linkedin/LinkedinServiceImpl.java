package com.birdeye.social.linkedin;

import com.birdeye.social.aspect.Profiled;
import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessGetPageOpenUrlReqRepo;
import com.birdeye.social.dao.BusinessLinkedinPageRepository;
import com.birdeye.social.dao.SocialErrorMessageRepository;
import com.birdeye.social.dao.SocialPagesAuditRepo;
import com.birdeye.social.dto.ApprovalPageInfo;
import com.birdeye.social.dto.SocialBusinessPageInfo;
import com.birdeye.social.dto.SocialTokenValidationDTO;
import com.birdeye.social.entities.BusinessGetPageOpenUrlRequest;
import com.birdeye.social.entities.BusinessLinkedinPage;
import com.birdeye.social.entities.LinkedinRefreshToken;
import com.birdeye.social.entities.SocialErrorMessage;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.exception.SocialPageUpdateException;
import com.birdeye.social.executor.services.ExecutorCommonService;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.external.request.linkedin.Value;
import com.birdeye.social.external.request.linkedin.*;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.linkedin.LinkedInPost.LinkedInMentionsData;
import com.birdeye.social.linkedin.LinkedInPost.LinkedInPageSearch;
import com.birdeye.social.linkedin.LinkedInPost.LinkedInPostRequest;
import com.birdeye.social.linkedin.organization.LinkedInOrganizationResponse;
import com.birdeye.social.linkedin.response.*;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.platform.dao.BusinessUserRepository;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.service.*;
import com.birdeye.social.sro.LinkedinUpdateFieldRequest;
import com.birdeye.social.utils.BusinessUtilsService;
import com.birdeye.social.utils.CoreUtils;
import com.birdeye.social.utils.JSONUtils;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.apache.log4j.MDC;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.social.InsufficientPermissionException;
import org.springframework.social.NotAuthorizedException;
import org.springframework.social.linkedin.api.Company;
import org.springframework.social.linkedin.api.LinkedInProfileFull;
import org.springframework.social.linkedin.api.NewShare;
import org.springframework.social.linkedin.api.NewShare.NewShareContent;
import org.springframework.social.linkedin.api.NewShare.NewShareVisibility;
import org.springframework.social.linkedin.api.NewShare.NewShareVisibilityCode;
import org.springframework.social.linkedin.api.impl.LinkedInTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.*;
import org.springframework.web.util.UriComponentsBuilder;

import javax.ws.rs.NotFoundException;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.*;
import static com.birdeye.social.linkedin.LinkedinConnectServiceImpl.X_RESTLI_PROTOCOL_VERSION;
import static com.birdeye.social.utils.ConversionUtils.convertDatToMentionResponse;
import static com.birdeye.social.utils.ConversionUtils.prepareLogoUrlData;
import static java.util.stream.Collectors.groupingBy;

@Service
@Profiled
public class LinkedinServiceImpl implements LinkedinService {

	public static final String LINKEDIN_COMPANY_URL = "https://www.linkedin.com/company/";

	@Autowired
	private BusinessLinkedinPageRepository linkedInRepo;

	@Autowired
	@Qualifier("socialRestTemplate")
	private RestTemplate linkedinRestTemplate;

	@Autowired
	private CommonService commonService;

	@Autowired
	private BusinessRepository businessRepo;

	@Autowired
	private BusinessUtilsService businessUtilService;

	@Autowired
	private IBusinessCoreService businessCoreService;

	@Autowired
	private BusinessUserRepository businessUserRepository;

	@Autowired
	private IRedisLockService redisService;

	@Autowired
	private IBusinessGetPageService businessGetPageService;

	@Autowired
	private BusinessGetPageOpenUrlReqRepo businessGetPageOpenUrlReqRepo;

	@Autowired
	private ILinkedinConnectService linkedinConnectService;

	@Autowired
	@Qualifier(Constants.SOCIAL_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;

	@Autowired
	private ExecutorCommonService executorCommonService;

	@Autowired
	private SocialPagesAuditRepo socialPagesAuditRepo;

	@Autowired
	private IRedisExternalService redisExternalService;

	@Autowired
	private KafkaProducerService kafkaProducerService;

	@Autowired
	private NexusService nexusService;

	@Autowired
	private ISocialLinkedinRefreshTokenService socialLinkedinRefreshTokenService;

	@Autowired
	private SocialLinkedinService socialLinkedinService;

	@Autowired
	private IBrokenIntegrationService brokenIntegrationService;

	@Autowired
	private SocialErrorMessageRepository socialErrorMessageRepository;

	@Autowired
	private KafkaProducerService kafkaProducer;

	private static final Logger LOGGER = LoggerFactory.getLogger(LinkedinServiceImpl.class);
	public final String LINKEDIN_AUTHENTICATION_URL = "https://www.linkedin.com/oauth/v2/authorization";
	public final String LINKEDIN_REDIRECT_URI_SETUP = "https://%s/dashboard/setup/social?active=linkedin";
	public final String LINKEDIN_REDIRECT_URI_GENERIC = "https://%s/dashboard/social/callback?active=linkedin";
	public final String LINKEDIN_SHARE_URL = "https://www.linkedin.com/feed/update/";
	public static final String LINKEDIN_RELATIONSHIP_IDENTIFIER = "urn:li:userGeneratedContent";
	public static final String LINKEDIN_RECIPE_IMAGE_URL = "urn:li:digitalmediaRecipe:feedshare-image";
	public static final String LINKEDIN_RECIPE_VIDEO_URL = "urn:li:digitalmediaRecipe:feedshare-video";
	private static final String CONNECT = "connect";
	private static final String RECONNECT = "reconnect";
	private static final String LINKEDIN_SOCIAL_ACTIONS_URL = "https://api.linkedin.com/v2/socialActions/";
	private static final String COMMENT = "comments";
	private static final String LINKEDIN_REACTIONS_URL = "https://api.linkedin.com/rest/reactions";
	private static final String LINKEDIN_REACTIONS_V2_URL = "https://api.linkedin.com/v2/reactions";

	private static final String LINKEDIN_API_URL = "https://api.linkedin.com";
	private static final String LINKEDIN_SOCIAL_METADATA_URL = "https://api.linkedin.com/v2/socialMetadata/";
	private static final String LINKEDIN_PROFILE_DETAILS_URL = "https://api.linkedin.com/rest/people/";
	private static final String LINKEDIN_REST_POSTS_URL = "https://api.linkedin.com/rest/posts";
	private static final String AUTHORIZATION = "Authorization";
	private static final String FOLLOWER_COUNT_URL = "https://api.linkedin.com/v2/networkSizes/";
	private static final String FOLLOWER_COUNT_URL_PARAM = "?edgeType=CompanyFollowedByMember";
	private static final String TIME_BASED_FOLLOWER_COUNT_URL = "https://api.linkedin.com/rest/organizationalEntityFollowerStatistics?q=organizationalEntity&organizationalEntity=";
	private static final String TIME_RANGE_START = "timeIntervals.timeRange.start=";
	private static final String TIME_RANGE_END = "timeIntervals.timeRange.end=";
	private static final String TIME_GRANULARITY_TYPE = "timeIntervals.timeGranularityType=";
	private static final String AND = "&";

	private static final String LINKEDIN_VIDEO_MEDIA_URL = "https://api.linkedin.com/rest/videos";
	private static final String LINKEDIN_IMAGE_MEDIA_URL = "https://api.linkedin.com/rest/images";

	public final String LINKEDIN_MEDIA_SHARE_URL = "https://api.linkedin.com/rest/shares/";
	public final String LINKEDIN_MEDIA_UGC_URL = "https://api.linkedin.com/rest/ugcPosts/";

	private static final String NOTIFICATION_TIME_RANGE_START = "timeRange=";

	private static final String LINKEDIN_SOCIAL_REST_ACTIONS_URL = "https://api.linkedin.com/rest/socialActions/";

	private static final String LINKEDIN_SOCIAL_REST_ACTIONS_URL_V2 = "https://api.linkedin.com/v2/socialActions/";

	private static final String LINKEDIN_SOCIAL_REST_ACTIVITY_URL = "https://api.linkedin.com/rest/activities?ids=";

	private static final String LINKEDIN_SOCIAL_REST_ACTIVITY_URL_V2 = "https://api.linkedin.com/v2/activities?ids=";


	private String getErrorMessage(String action) {
		Optional<SocialErrorMessage> errorDetails =  socialErrorMessageRepository.findByAction(action);
		if (!errorDetails.isPresent()) {
			return action;
		}
		return errorDetails.get().getMessage();
	}

	@Override
	@Transactional(propagation=Propagation.REQUIRES_NEW)
	public void updateIsValidForPage(Integer id, Integer isValid, String errorMsg,Integer subErrorCode){
		BusinessLinkedinPage page = linkedInRepo.findById(id);
		page.setIsValid(isValid);
		page.setErrorLog(errorMsg);
		if(isValid == 1){
			page.setLinkedinErrorCode(null);
		}else{
			page.setLinkedinErrorCode(subErrorCode!=null?subErrorCode:0);
		}
		brokenIntegrationService.pushValidIntegrationStatus(page.getEnterpriseId(), SocialChannel.LINKEDIN.getName(),page.getId(),isValid,null);
		linkedInRepo.saveAndFlush(page);
		if(page.getIsValid().equals(0)) {
			commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(page),
					null, page.getBusinessId(), page.getEnterpriseId());
		}
	}


	@Override
	public String getAuthorizationUrl(LinkedinCreds creds, String redirectUri) throws URISyntaxException {
		LOGGER.info("Calling linkedin getAuthorizationUrl for credentials app id {} app secret {} redirect uri {}", creds.getAppId(), creds.getAppSecret(), redirectUri);
		URIBuilder authURL = new URIBuilder(LINKEDIN_AUTHENTICATION_URL);
		List<String> scopes = new ArrayList<>();
		scopes.add("r_liteprofile");
		scopes.add("r_emailaddress");
		scopes.add("w_member_social");
		scopes.add("w_organization_social");
		scopes.add("rw_organization_admin");
		authURL.addParameter("client_id", creds.getAppId());
		authURL.addParameter("response_type", "code");
		authURL.addParameter("redirect_uri", redirectUri);
		authURL.addParameter("state", "e8f0d57f-9370-41c6-877c-24fd2b1fd1f8");
		authURL.addParameter("scope", org.apache.commons.lang3.StringUtils.join(scopes, " "));
		return authURL.toString();
	}

	private LinkedinCreds getLinkedinCreds(Business business) throws Exception {
		ConsumerTokenAndSecret appCreds = commonService.getAppKeyAndToken(business, "linkedin");
		LOGGER.info("Getting linkedin creds for business id {} :: token {} and secret {}", business.getId(), appCreds.getToken(), appCreds.getSecret());
		return new LinkedinCreds(appCreds.getToken(),appCreds.getSecret());
	}
	
	@Override
	public OpenUrlFetchPageResponse submitFetchPageRequestForOpenURL(Long businessId, ChannelAuthOpenUrlRequest channelAuthOpenUrlRequest) {
		String key = SocialChannel.LINKEDIN.getName().concat(String.valueOf(channelAuthOpenUrlRequest.getFirebaseKey()));
		boolean lock = redisService.tryToAcquireLock(key);
		LOGGER.info("[Redis Lock] Lock status : {}",lock);
		if(lock){
			BusinessGetPageOpenUrlRequest reqLinkedin = null;
			try{
				Business business = businessRepo.findByBusinessId(businessId);
				LinkedinCreds linkedinCreds =  getLinkedinCreds(business);
				AccessTokenInfo accessTokenInfo = linkedinConnectService.getAccessToken(linkedinCreds,channelAuthOpenUrlRequest.getRedirectUri(),channelAuthOpenUrlRequest.getAuthCode());
				reqLinkedin = submitLinkedinGetPageRequestForOpenURL(businessId, channelAuthOpenUrlRequest.getFirebaseKey(), accessTokenInfo,CONNECT);
				nexusService.insertMapInFirebase("socialOpenUrl/"+ channelAuthOpenUrlRequest.getFirebaseKey(),channelAuthOpenUrlRequest.getFirebaseKey() ,Status.INITIAL.getName());
				fetchLinkedinForOpenURLPages(reqLinkedin,accessTokenInfo);
				OpenUrlFetchPageResponse openUrlFetchPageResponse = new OpenUrlFetchPageResponse();
				openUrlFetchPageResponse.setFirebaseKey(channelAuthOpenUrlRequest.getFirebaseKey());
				return openUrlFetchPageResponse;
			}catch (Exception ex){
				handleCleanupRedisForOpenurl(key, channelAuthOpenUrlRequest, businessId);
			}
		}else{
			LOGGER.info("Could not acquire redis lock for key {} ", key);
		}
		return null;

	}

	private void handleCleanupRedisForOpenurl(String key, ChannelAuthOpenUrlRequest authRequest, Long businessId) {
		redisService.release(key);
		nexusService.updateMapInFirebase("socialOpenUrl/"+ authRequest.getFirebaseKey(), authRequest.getFirebaseKey(),Status.CANCEL.getName());

		LOGGER.error("[Redis Lock] Lock released for business {}", businessId);
		BusinessGetPageOpenUrlRequest reqGMB = businessGetPageOpenUrlReqRepo.findByFirebaseKey(authRequest.getFirebaseKey());
		if (reqGMB != null) {
			reqGMB.setStatus(Status.CANCEL.getName());
			businessGetPageOpenUrlReqRepo.saveAndFlush(reqGMB);
		}
		throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN);
	}
	
	public BusinessGetPageOpenUrlRequest submitLinkedinGetPageRequestForOpenURL(Long businessId,String fireBaseKey, AccessTokenInfo accessTokenInfo,String connectType) {
		BusinessGetPageOpenUrlRequest gmbRequest = new BusinessGetPageOpenUrlRequest();
		gmbRequest.setChannel(SocialChannel.LINKEDIN.getName());
		gmbRequest.setEnterpriseId(businessId);
		gmbRequest.setStatus(Status.INITIAL.getName());
		gmbRequest.setPageCount(0);
		gmbRequest.setRequestType(connectType);
		gmbRequest.setUserAccessToken(accessTokenInfo.getAccessToken());
		gmbRequest.setFirebaseKey(fireBaseKey);
		return businessGetPageOpenUrlReqRepo.saveAndFlush(gmbRequest);
	}

	@Async
	public void fetchLinkedinForOpenURLPages(BusinessGetPageOpenUrlRequest linkedinRequest, AccessTokenInfo accessTokenInfo) {
		Integer pageCount = 0;
		Integer totalCount = 0;
		Set<String> oldRequestIds = new HashSet<>();
 		try {
			LOGGER.info("delaying request for enterprise Id in open url for {} {}",linkedinRequest.getEnterpriseId(),CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("linkedin.delay.millis"));
			Thread.sleep(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("linkedin.delay.millis"));
		} catch (InterruptedException e) {
			LOGGER.error("exception occurred while sleep after fetching access token {}",e);
		}
		String key = SocialChannel.LINKEDIN.getName().concat(String.valueOf(linkedinRequest.getFirebaseKey()));
		String requestId = linkedinRequest.getId();
		long enterpriseId = linkedinRequest.getEnterpriseId();
		List<BusinessLinkedinPage> businessLinkedinPages = fetchAllPagesForAccount(accessTokenInfo, requestId);
		linkedinRequest.setSocialUserId(socialLinkedinService.getProfileIdFromLinkedinPagesOfSameAccount(businessLinkedinPages));
		LinkedinRefreshToken linkedinRefreshToken = socialLinkedinRefreshTokenService.createOrUpdateLinkedinRefreshToken(null, linkedinRequest.getSocialUserId(), accessTokenInfo);
		Map<String,List<BusinessLinkedinPage>> listMap = businessLinkedinPages.stream().collect(groupingBy(BusinessLinkedinPage :: getPageType));
		if(MapUtils.isNotEmpty(listMap)){
			for(Map.Entry<String,List<BusinessLinkedinPage>> m : listMap.entrySet()){
				String pageType = m.getKey();
				List<BusinessLinkedinPage> newPages = m.getValue();
				Map<String,BusinessLinkedinPage> newBusinessLinkedinPagesMap = newPages.stream().collect(Collectors.toMap(BusinessLinkedinPage :: getProfileId, Function.identity()));
				List<String> profileIds = newPages.stream().map(BusinessLinkedinPage::getProfileId).collect(Collectors.toList());
				List<BusinessLinkedinPage> existingLinkedinPages = linkedInRepo.findByProfileIdInAndPageType(profileIds,pageType);
				Map<String,BusinessLinkedinPage> existingLinkedinPagesMap = existingLinkedinPages.stream().collect(Collectors.toMap(BusinessLinkedinPage :: getProfileId, Function.identity()));
				if(CollectionUtils.isNotEmpty(existingLinkedinPages)){
					List<String> updatedProfileIds = new ArrayList<>();
					for(Map.Entry<String,BusinessLinkedinPage> map : existingLinkedinPagesMap.entrySet()){
						BusinessLinkedinPage businessLinkedinPage = map.getValue();
						oldRequestIds.add(businessLinkedinPage.getBusinessGetPageId());
						businessLinkedinPage.setBusinessGetPageId(requestId);
					if(businessLinkedinPage.getEnterpriseId() == null || businessLinkedinPage.getEnterpriseId().equals(enterpriseId))
						{
							businessLinkedinPage.setRefreshToken(linkedinRefreshToken.getId());
							BusinessLinkedinPage page = newBusinessLinkedinPagesMap.get(businessLinkedinPage.getProfileId());
							socialLinkedinService.updateExisitngPage(businessLinkedinPage, page,linkedinRefreshToken);
							updatedProfileIds.add(page.getProfileId());
							commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(), Arrays.asList(businessLinkedinPage),
									null, businessLinkedinPage.getBusinessId(), businessLinkedinPage.getEnterpriseId());
						}
					}
					linkedInRepo.save(existingLinkedinPages);
					socialLinkedinService.pushToKafkaForValidity(Constants.LINKEDIN, updatedProfileIds);

					existingLinkedinPages.forEach(page->commonService.uploadPageImageToCDN(page));
					removeExistingPages(businessLinkedinPages, existingLinkedinPagesMap);
					pageCount = businessLinkedinPages.size();
				}else{
					pageCount = businessLinkedinPages.size();
				}
				if(CollectionUtils.isNotEmpty(existingLinkedinPages)) {
					totalCount = existingLinkedinPages.size() + pageCount;
				} else {
					totalCount = pageCount;
				}
			}
			if(CollectionUtils.isNotEmpty(oldRequestIds)) {// send event to check invalid get page requests and mark them as cancelled
				kafkaProducer.sendObjectV1(VALIDATE_PAGE_REQUEST, new CheckInvalidGetPageState(LINKEDIN, oldRequestIds));
			}
			if(businessLinkedinPages.size()>0){
				List<String> updatedProfileIds = new ArrayList<>();
				businessLinkedinPages.forEach(page -> {
					page.setRefreshToken(linkedinRefreshToken.getId());
					updatedProfileIds.add(page.getProfileId());
					linkedInRepo.save(page);
					commonService.uploadPageImageToCDN(page);
				});
				socialLinkedinService.pushToKafkaForValidity(Constants.LINKEDIN, updatedProfileIds);

				businessLinkedinPages.forEach(businessLinkedinPage -> {
					SocialTokenValidationDTO socialTokenValidationDTO = new SocialTokenValidationDTO();
					socialTokenValidationDTO.setId(businessLinkedinPage.getId());
					socialTokenValidationDTO.setChannel(SocialChannel.LINKEDIN.getName());
					kafkaProducerService.sendObjectV1(KafkaTopicEnum.LINKEDIN_PERMISSION_CHECK.getName(), socialTokenValidationDTO);
					commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.ADD_PAGES.name(), Arrays.asList(businessLinkedinPage),
							linkedinRequest.getSocialUserId(), businessLinkedinPage.getBusinessId(), businessLinkedinPage.getEnterpriseId());
					businessLinkedinPage.setRefreshToken(linkedinRefreshToken.getId());
				});


			}
		}
		linkedinRequest.setPageCount(pageCount);
		linkedinRequest.setTotalPages(totalCount);

		if(Objects.isNull(linkedinRequest.getTotalPages()) || linkedinRequest.getTotalPages() == 0){
			linkedinRequest.setStatus(Status.NO_PAGES_FOUND.getName());
			nexusService.updateMapInFirebase("socialOpenUrl/"+ linkedinRequest.getFirebaseKey(),linkedinRequest.getFirebaseKey() ,Status.NO_PAGES_FOUND.getName());
		}else{
			linkedinRequest.setStatus(Status.FETCHED.getName());
			nexusService.updateMapInFirebase("socialOpenUrl/"+ linkedinRequest.getFirebaseKey(),linkedinRequest.getFirebaseKey() ,Status.FETCHED.getName());

		}
		businessGetPageOpenUrlReqRepo.saveAndFlush(linkedinRequest);
		redisService.release(key);

	}


	private void removeExistingPages(List<BusinessLinkedinPage> businessLinkedinPages,
			Map<String, BusinessLinkedinPage> existingLinkedinPagesMap) {
		Iterator<BusinessLinkedinPage> itr = businessLinkedinPages.iterator();
		while (itr.hasNext()){
			BusinessLinkedinPage businessLinkedinPage = itr.next();
			if(existingLinkedinPagesMap.get(businessLinkedinPage.getProfileId()) !=null){
				itr.remove();
			}
		}
	}


	private List<BusinessLinkedinPage> fetchAllPagesForAccount(AccessTokenInfo accessTokenInfo, String requestId) {
		int count = 0;
		int maxTries = 2;
		while(true){
			try{
				Map<String, Object> tokenIntrospection = getLinkedinTokenDetails(accessTokenInfo.getAccessToken());
				String scope = (String)tokenIntrospection.get("scope");
				List<BusinessLinkedinPage> businessLinkedinPages = new ArrayList<>();
				LinkedinOrganizationPagesInfo linkedinOrganizationPagesInfo = linkedinOrganizationInfo(accessTokenInfo.getAccessToken());
				LinkedinUserProfileInfo linkedinUserProfileInfo = socialLinkedinService.getLinkedInUserProfile(accessTokenInfo.getAccessToken());
				if(Objects.nonNull(linkedinOrganizationPagesInfo) && !CollectionUtils.isEmpty(linkedinOrganizationPagesInfo.getElements())){
					linkedinOrganizationPagesInfo.getElements().forEach(element -> {
						BusinessLinkedinPage businessLinkedinPage = new BusinessLinkedinPage();
						businessLinkedinPage.setPageType("company");
						businessLinkedinPage.setAccessToken(accessTokenInfo.getAccessToken());
						businessLinkedinPage.setUrn(element.getOrganization());
						businessLinkedinPage.setBusinessGetPageId(requestId);
						businessLinkedinPage.setCompanyName(element.getLocalizedName());
						businessLinkedinPage.setProfileId(element.getOrganization().split(LINKEDIN_ORG_PREFIX)[1]);
						businessLinkedinPage.setPageUrl(LINKEDIN_COMPANY_URL +businessLinkedinPage.getProfileId());
						businessLinkedinPage.setScope(scope);
						businessLinkedinPage.setIsValid(1);
						businessLinkedinPage.setIsSelected(0);
						businessLinkedinPage.setExpiresOn(accessTokenInfo.getExpireTime());
						businessLinkedinPage.setLogoUrl(element.getLogoUrl());
						businessLinkedinPage.setVanityName(element.getVanityName());
						businessLinkedinPages.add(businessLinkedinPage);
					});
				}
				if(Objects.nonNull(linkedinUserProfileInfo)){
					BusinessLinkedinPage businessLinkedinPage = new BusinessLinkedinPage();
					businessLinkedinPage.setPageType("profile");
					businessLinkedinPage.setAccessToken(accessTokenInfo.getAccessToken());
					businessLinkedinPage.setUrn("urn:li:person:"+linkedinUserProfileInfo.getId());
					businessLinkedinPage.setFirstName(linkedinUserProfileInfo.getLocalizedFirstName());
					businessLinkedinPage.setLastName(linkedinUserProfileInfo.getLocalizedLastName());
					businessLinkedinPage.setBusinessGetPageId(requestId);
					businessLinkedinPage.setProfileId(linkedinUserProfileInfo.getId());
					businessLinkedinPage.setPageUrl(SocialLinkedinServiceImpl.LINKEDIN_PROFILE_URL +linkedinUserProfileInfo.getVanityName());
					businessLinkedinPage.setScope(scope);
					businessLinkedinPage.setIsValid(1);
					businessLinkedinPage.setIsSelected(0);
					businessLinkedinPage.setExpiresOn(accessTokenInfo.getExpireTime());
					businessLinkedinPages.add(businessLinkedinPage);
				}
				return businessLinkedinPages;
			}catch (Exception ex){
				try {
					Thread.sleep(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("linkedin.delay.millis"));
				} catch (InterruptedException e) {
					LOGGER.error("exception occurred while sleep in retry open url {}",ex);
				}
				LOGGER.error("exception occurred while fetching pages for linkedin open url {}",ex);
				if (++count == maxTries) throw ex;
			}
		}
	}

	@Override
	public Map<String, Object> getLinkedinTokenDetails(String token) {
		return linkedinConnectService.getLinkedinTokenDetails(token);
	}

	@Override
	public LinkedinOrganizationPagesInfo linkedinOrganizationInfo(String token) {
		return socialLinkedinService.linkedinOrganizationInfo(token);
	}

	@Override
	public LinkedInOrganizationResponse linkedInOrganizationResponse(String token) {
		return linkedinConnectService.linkedInOrganizationResponse(token);
	}

	@Override
	public LinkedinUserInfo getUserProfileInfo(String accessToken) {
		LinkedInTemplate linkedIn = new LinkedInTemplate(accessToken);
		LinkedInProfileFull profile = linkedIn.profileOperations().getUserProfileFull();
		LinkedinUserInfo profileInfo = new LinkedinUserInfo();
		profileInfo.setProfileId(profile.getId());
		profileInfo.setFirstName(profile.getFirstName());
		profileInfo.setLastName(profile.getLastName());
		profileInfo.setProfileUrl(linkedIn.profileOperations().getProfileUrl());
		profileInfo.setProfilePictureUrl(profile.getProfilePictureUrl());
		return profileInfo;
	}

	private static CompanyPageInfo from(Company company) {
		CompanyPageInfo companyInfo = new CompanyPageInfo();
		companyInfo.setCompanyId(company.getId());
		companyInfo.setCompanyName(company.getName());
		companyInfo.setLogoUrl(company.getLogoUrl());
		String companyUrl = COMPANY_URL_PREFIX + company.getId();
		companyInfo.setCompanyUrl(companyUrl);
		return companyInfo;
	}
	
	
	@Override
	public List<CompanyPageInfo> getCompanyPageInfo(String accessToken) throws BirdeyeSocialException {
		LinkedInTemplate linkedIn = new LinkedInTemplate(accessToken);
		ResponseEntity<CompanyDetailsList> response = null;
		try {
			response = linkedIn.restOperations().getForEntity(COMPANY_INFO_URL, CompanyDetailsList.class);
		} catch (NotAuthorizedException e) {
			LOGGER.error("NotAuthorizedException while calling linkedin API getCompanyPageInfo for business id {} and user id {} is :: {}", MDC.get("businessId"), MDC.get("userId"), e);
			throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_PAGES_NOT_FOUND, e.getMessage(), getMarkLinkedinPageInvalidExceptionDataMap());
		} catch (HttpStatusCodeException e) {
			LOGGER.error("HttpStatusCodeException while calling linkedin API getCompanyPageInfo for business id {} and user id {} is :: {}", MDC.get("businessId"), MDC.get("userId"), e);
			throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_PAGES_NOT_FOUND, e.getMessage(), getExceptionDataMap(e.getStatusCode()));
		} catch (RestClientException e) {
			LOGGER.error("RestClientException while calling linkedin API getCompanyPageInfo for business id {} and user id {} is :: {}", MDC.get("businessId"), MDC.get("userId"), e);
			throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_PAGES_NOT_FOUND, e.getMessage());
		} catch (Exception e) {
			LOGGER.error("Exception while calling linkedin API getCompanyPageInfo for business id {} and user id {} is :: {}", MDC.get("businessId"), MDC.get("userId"), e);
			throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_PAGES_NOT_FOUND, e.getMessage());
		}
		LOGGER.info("Linkedin : getCompanyPageInfo response for business id {} and user id {} and access token {} is : response = {}", MDC.get("businessId"), MDC.get("userId"), accessToken,
				response);
		List<CompanyPageInfo> companyInfoList = new ArrayList<>();
		if (response.hasBody()) {
			if (response.getBody().getValues() != null) {
				List<Integer> companyIds = new ArrayList<>();
				for (CompanyDetails company : response.getBody().getValues()) {
					companyIds.add(company.getId());
				}

				if (CollectionUtils.isNotEmpty(companyIds)) {
					// TODO: Is there a batch version? How many rest calls here
					companyIds.stream().forEach(compId -> {
						CompanyPageInfo companyInfo = from(linkedIn.companyOperations().getCompany(compId));
						companyInfoList.add(companyInfo);
					});
				}
			}
		}
		LOGGER.info("Linkedin : getCompanyPageInfo response parsed for for business id {} and user id {} access token {} is : response = {}", MDC.get("businessId"), MDC.get("userId"), accessToken,
				response);
		return companyInfoList;
	}
	
	private Map<String, Object> getExceptionDataMap(HttpStatus status) {
		Map<String, Object> map = new HashMap<>();
		if (HttpStatus.UNAUTHORIZED.value() == status.value()) {
			map.put("mark_page_inactive", true);
		} else {
			map.put("mark_page_inactive", false);
		}
		return map;
	}

	private Map<String, Object> getMarkLinkedinPageInvalidExceptionDataMap() {
		Map<String, Object> map = new HashMap<>();
		map.put("mark_page_inactive", true);
		return map;
	}

	private RegisterLinkedinMediaRequest prepareRegisterMediaRequest(String companyUrn, Boolean isVideo) {
		RegisterLinkedinMediaRequest request = new RegisterLinkedinMediaRequest();
		RegisterUploadRequest uploadRequest = new RegisterUploadRequest();
		ServiceRelationships relationships = new ServiceRelationships();
		List<ServiceRelationships> relationshipsList = new ArrayList<>();
		uploadRequest.setOwner(companyUrn);
		List<String> mediaList = new ArrayList<>();
		String recipeUrl = isVideo?LINKEDIN_RECIPE_VIDEO_URL:LINKEDIN_RECIPE_IMAGE_URL;
		mediaList.add(recipeUrl);
		uploadRequest.setRecipes(mediaList);
		relationships.setRelationshipType("OWNER");
		relationships.setIdentifier(LINKEDIN_RELATIONSHIP_IDENTIFIER);
		relationshipsList.add(relationships);
		uploadRequest.setServiceRelationships(relationshipsList);
		if(!isVideo) {
			List<String> uploadMechanism = new ArrayList<>();
			uploadMechanism.add("SYNCHRONOUS_UPLOAD");
			uploadRequest.setSupportedUploadMechanism(uploadMechanism);
		}
		request.setRegisterUploadRequest(uploadRequest);
		return request;
	}

	private RegisterLinkedinMediaRequestV2 prepareRegisterMediaRequestV2(String companyUrn,Long fileSize, Boolean isVideo) {
		RegisterLinkedinMediaRequestV2 request = new RegisterLinkedinMediaRequestV2();
		InitializeUploadRequest uploadRequest = new InitializeUploadRequest();
		uploadRequest.setOwner(companyUrn);
		if(isVideo) {
			uploadRequest.setFileSizeBytes(fileSize);
		}
		request.setInitializeUploadRequest(uploadRequest);
		return request;
	}

	private SharePostRequestLinkedin prepareShareRequest(String accessToken,LinkedinData data, String companyUrn, Boolean isVideo, Boolean isValidPreview) throws IOException {
		SharePostRequestLinkedin sharePostRequest = new SharePostRequestLinkedin();
		Distribution distribution = new Distribution();

		TargetedEntity targetedEntity = new TargetedEntity();
		LinkedInPostMetadataRequest linkedInPostMetadataRequest;
		List<MediaContent> mediaList = new ArrayList<>();
		//String type;
		if(org.springframework.util.CollectionUtils.isEmpty(data.getMediaUrn())) {
			//type = Objects.nonNull(data.getMediaUrl())?"ARTICLE":"NONE";
			if(Objects.nonNull(data.getMediaUrl()) && (Objects.isNull(isValidPreview) || isValidPreview)) {
				Content content = new Content();
				try {
					MetatagsResponseDTO dataResult=getArticlePosting(accessToken,data.getMediaUrl(),companyUrn);
					ArticleContent articleContent= new ArticleContent();
					articleContent.setSource(data.getMediaUrl());
					articleContent.setTitle(StringUtils.isEmpty(dataResult.getTitle())?"":dataResult.getTitle());
					articleContent.setThumbnail(dataResult.getImage());
					articleContent.setDescription(StringUtils.isEmpty(dataResult.getDescription())?"":dataResult.getDescription());
					content.setArticle(articleContent);
					sharePostRequest.setContent(content);
				} catch (Exception e) {
					LOGGER.error("error in creating link preview url for linkedin post: {}", e.getMessage());
				}
			}
		} else {
			//type = isVideo?"VIDEO":"IMAGE";
			Content content = new Content();
			List<String> mediaArray = data.getMediaUrn();
			if(mediaArray.size()>1) {
				MultiImageContent multiImageContent = new MultiImageContent();
				for (String med : mediaArray) {
					MediaContent media = new MediaContent();
					media.setId(med);
					mediaList.add(media);

				}
				multiImageContent.setImages(mediaList);
				content.setMultiImage(multiImageContent);
			}
			else{
				MediaContent media = new MediaContent();
				media.setId(mediaArray.get(0));
				content.setMedia(media);
			}


		//	LOGGER.info("Media list: {}",mediaContent);
			sharePostRequest.setContent(content);
		}
		distribution.setFeedDistribution("MAIN_FEED");
		linkedInPostMetadataRequest = data.getLinkedInPostMetadataRequest();
		LOGGER.info("linkedInPostMetadataRequest: {}",linkedInPostMetadataRequest);
		if(Objects.nonNull(linkedInPostMetadataRequest)) {
			targetedEntity=createTargetEntity(linkedInPostMetadataRequest);
			LOGGER.info("targetedEntity: {}",targetedEntity);
			List<TargetedEntity> targetedEntityList=new ArrayList<>();
			targetedEntityList.add(targetedEntity);
			distribution.setTargetEntities(targetedEntityList);
			sharePostRequest.setDistribution(distribution);
		}
		else {
			sharePostRequest.setDistribution(distribution);
		}
		sharePostRequest.setAuthor(companyUrn);
		sharePostRequest.setLifecycleState("PUBLISHED");
		sharePostRequest.setVisibility("PUBLIC");
		sharePostRequest.setCommentary(data.getText());


		return sharePostRequest;
	}

	String getOriginalUrl(String imageUrl) throws IOException{
		HttpURLConnection connection = (HttpURLConnection) new URL(imageUrl).openConnection();
		connection.setInstanceFollowRedirects(false);
		int responseCode = connection.getResponseCode();

		if (responseCode >= 300 && responseCode <= 399) {
			// The response code indicates a redirect
			String redirectUrl = connection.getHeaderField("Location");
			if (redirectUrl != null) {
				// Resolve the redirected URL recursively
				return getOriginalUrl(redirectUrl);
			}
		}

		// If no redirect or final URL found, return the original URL
		return imageUrl;
	}

	private MetatagsResponseDTO getArticlePosting(String accessToken, String mediaUrl, String companyUrn) throws IOException {

		MetatagsResponseDTO response = getMetatagsOfAUrl(mediaUrl);

		if(Objects.nonNull(response.getImage())) {
			RegisterLinkedinMediaResponseV2 imageResponse = registerMediaV2(accessToken, companyUrn, null, false);
			String orgUrl =getOriginalUrl(response.getImage());
			URL url = new URL(orgUrl);
			byte[] mediaData = downloadFile(url);
			if(Objects.isNull(mediaData ) || mediaData .length==0)  {
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_UNABLE_TO_DOWNLOAD_MEDIA,"Error Downloading media file");
			}
			boolean imageStatus = uploadMedia(accessToken, mediaData,
					companyUrn, imageResponse.getValue().getUploadUrl(),false);
			if (imageStatus) {
				String statusResponse = checkUploadStatus(imageResponse.getValue().getImage(),accessToken, false,false);
				LOGGER.info("Upload Status: {}\nImage uploaded over api call, proceeding to call api to post that image for linkedin page.", statusResponse);
				response.setImage(imageResponse.getValue().getImage());
			}
		}
		return response;
	}

	private byte[] downloadFile(URL url) {
		InputStream in = null;
		try {
			URLConnection urlConn = url.openConnection();
			urlConn.addRequestProperty("User-Agent", "Mozilla/4.76");
			in = new BufferedInputStream(urlConn.getInputStream());
			ByteArrayOutputStream out = new ByteArrayOutputStream();
			byte[] buf = new byte[1024];
			int n;
			while (-1!=(n=in.read(buf)))
			{
				out.write(buf, 0, n);
			}
			out.close();
			in.close();
			return out.toByteArray();
		} catch (Exception e) {
			LOGGER.info("Error downloading media from url {}", JSONUtils.toJSON(e));
			return null;
		} finally {
			if(in != null) {
				try {
					in.close();
				} catch (Exception e) {
					LOGGER.info("inputStream close IOException: {}",e.getMessage());
				}
			}
		}
	}


	@Cacheable(value = "linksPreviewCache", key = "#url", unless = "#result == null")
	public MetatagsResponseDTO getMetatagsOfAUrl(String url) {
		try {
			LOGGER.info("Request received to fetch url content for {}", url);
			if (StringUtils.isEmpty(url)) {
				return null;
			}
			if (!StringUtils.startsWith(url, "http")) {
				url = "http://" + url;
			}
			LOGGER.info("Fetching url content for {}", url);
			Document document = Jsoup.connect(url).timeout(60000).get();
			if (document == null) {
				String domainNameOrTitleFor404Page = StringUtils.isBlank(CoreUtils.getDomainName(url)) ? url : CoreUtils.getDomainName(url);
				return new MetatagsResponseDTO(domainNameOrTitleFor404Page, url, domainNameOrTitleFor404Page);
			}

			MetatagsResponseDTO responseDTO = new MetatagsResponseDTO();
			responseDTO.setUrl(url);

			org.jsoup.select.Elements metaTags = document.getElementsByTag("meta");

			for(Element element: metaTags) {
				if(element.attr("property").equals("og:title")) {
					responseDTO.setTitle(element.attr("content"));
				} else if(element.attr("property").equals("og:description") || element.attr("name").equals("description")) {
					responseDTO.setDescription(element.attr("content"));
				} else if(element.attr("property").equals("og:image")) {
					responseDTO.setImage(element.attr("content"));
				}
			}

			if(StringUtils.isEmpty(responseDTO.getTitle())) {
				responseDTO.setTitle(document.title());
			}
			responseDTO.setDomainName(CoreUtils.getDomainName(url));
			LOGGER.info("Metadata response for URL {} is {}", url, responseDTO);
			return responseDTO;
		} catch (Exception e) {
			LOGGER.error("Exception while fetching metatags for url {} is {}", url, e);
			String domainNameOrTitleFor404Page = StringUtils.isBlank(CoreUtils.getDomainName(url)) ? url : CoreUtils.getDomainName(url);
			return new MetatagsResponseDTO(domainNameOrTitleFor404Page, url, domainNameOrTitleFor404Page);
		}
	}

	TargetedEntity createTargetEntity(LinkedInPostMetadataRequest linkedInPostMetadataRequest) {
		TargetedEntity targetedEntity = new TargetedEntity();
		if(!org.springframework.util.CollectionUtils.isEmpty(linkedInPostMetadataRequest.getFunctions())){
			targetedEntity.setJobFunctions(linkedInPostMetadataRequest.getFunctions());
		}
		if(!org.springframework.util.CollectionUtils.isEmpty(linkedInPostMetadataRequest.getIndustries())){
			targetedEntity.setIndustries(linkedInPostMetadataRequest.getIndustries());
		}
		if(!org.springframework.util.CollectionUtils.isEmpty(linkedInPostMetadataRequest.getSeniorities())){
			targetedEntity.setSeniorities(linkedInPostMetadataRequest.getSeniorities());
		}
		return targetedEntity;
	}

	@Override
	public LinkedinPostInfo post(String accessToken, LinkedinData data, String companyUrn, Boolean isVideo, Boolean isValidPreview) throws BirdeyeSocialException{
		LOGGER.info("Posting data on linkedin for company urn {}",companyUrn);
		LinkedinPostInfo linkedinPostInfo;
		try{
			SharePostRequestLinkedin sharePostRequest = prepareShareRequest(accessToken,data,companyUrn,isVideo,isValidPreview);
			List<String> responseMap = linkedinConnectService.post(sharePostRequest,accessToken);
			linkedinPostInfo = new LinkedinPostInfo();
			if(CollectionUtils.isNotEmpty(responseMap)) {
				String urn = responseMap.get(0);
				linkedinPostInfo.setPostId(urn);
				linkedinPostInfo.setPostUrl(LINKEDIN_SHARE_URL+urn);
			}
		}catch (BirdeyeSocialException ex){
			throw new BirdeyeSocialException(ErrorCodes.valueOf(ex.getCode()),ex.getErrorCode(), ex.getMessage(), null);
		} catch (NotFoundException ex) {
			throw new BirdeyeSocialException(ErrorCodes.CLIENT_ERROR_404, ex.getMessage() , new HashMap<>());
		} catch(Exception ex) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN,ex.getMessage());
		}
		return linkedinPostInfo;
	}

	@Override
	public boolean uploadMedia(String accessToken, byte[] media, String companyUrn, String apiUrl, Boolean isVideo) throws BirdeyeSocialException{
		LOGGER.info("Uploading media on linkedin for company urn {}",companyUrn);
		try{
			 return linkedinConnectService.uploadMedia(media,accessToken,apiUrl, isVideo);
		}catch (ExternalAPIException ex){
			if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_DUPLICATE_REQUEST){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_DUPLICATE_CONTENT,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_MAX_LIMIT_REACHED){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_LIMIT_REACED,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.UNAUTHORIZED_DEFAULT){
				throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.FORBIDDEN){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_FORBIDDEN,ex.getMessage());
			}else{
				String message = getErrorMessage(ex.getMessage());
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN,message);
			}
		}catch (BirdeyeSocialException bse){
			throw new BirdeyeSocialException(ErrorCodes.valueOf(bse.getCode()),bse.getErrorCode(),bse.getMessage(),null);
		}catch(Exception ex) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN, ex.getMessage());
		}
	}

	@Override
	public RegisterLinkedinMediaResponse registerMedia(String accessToken, String companyUrn, Boolean isVideo) throws BirdeyeSocialException{
		LOGGER.info("Registering data on linkedin for company urn {}",companyUrn);
		RegisterLinkedinMediaResponse mediaResponse;
		Map<String, Object> responseMap;
		try{
			RegisterLinkedinMediaRequest registerMediaRequest = prepareRegisterMediaRequest(companyUrn,isVideo);
			responseMap = linkedinConnectService.registerMedia(registerMediaRequest,accessToken);
			Map<String, Object> valueMap = (Map<String, Object>)responseMap.get("value");
			mediaResponse = new RegisterLinkedinMediaResponse();
			Value value = new Value();
			UploadMechanism uploadMechanism = new UploadMechanism();
			MediaUploadHttpRequest mediaUpload = new MediaUploadHttpRequest();
			value.setAsset((String)valueMap.get("asset"));
			value.setMediaArtifact((String)valueMap.get("mediaArtifact"));
			Map<String, Object> uploadMechMap = (Map<String, Object>)valueMap.get("uploadMechanism");
			Map<String, Object> mediaRequestMap = (Map<String, Object>)uploadMechMap.get("com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest");
			mediaUpload.setUploadUrl((String)mediaRequestMap.get("uploadUrl"));
			uploadMechanism.setMediaUploadHttpRequest(mediaUpload);
			value.setUploadMechanism(uploadMechanism);
			mediaResponse.setValue(value);
		}catch (ExternalAPIException ex){
			if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_DUPLICATE_REQUEST){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_DUPLICATE_CONTENT,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_MAX_LIMIT_REACHED){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_LIMIT_REACED,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.UNAUTHORIZED_DEFAULT){
				throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.FORBIDDEN){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_FORBIDDEN,ex.getMessage());
			}else{
				String message = getErrorMessage(ex.getMessage());
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN,message);
			}
		}catch (BirdeyeSocialException ex){
			throw new BirdeyeSocialException(ErrorCodes.valueOf(ex.getCode()),ex.getErrorCode(),ex.getMessage(),null);
		} catch(Exception ex) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN,ex.getMessage());
		}
		return mediaResponse;
	}

	@Override
	public RegisterLinkedinMediaResponseV2 registerMediaV2(String accessToken, String companyUrn,Long fileSize, Boolean isVideo) throws BirdeyeSocialException{
		LOGGER.info("Registering data on linkedin V2 for company urn {}",companyUrn);
		RegisterLinkedinMediaResponseV2 mediaResponse;
		Map<String, Object> responseMap;
		try{
			RegisterLinkedinMediaRequestV2 registerLinkedinMediaRequestV2 = prepareRegisterMediaRequestV2(companyUrn,fileSize,isVideo);
			responseMap = linkedinConnectService.registerMediaV2(registerLinkedinMediaRequestV2,accessToken,isVideo);
			Map<String, Object> valueMap = (Map<String, Object>)responseMap.get("value");
			mediaResponse = new RegisterLinkedinMediaResponseV2();
			ValueV2 value = new ValueV2();
			if(!isVideo) {
				value.setImage((String) valueMap.get("image"));
				value.setUploadUrl((String) valueMap.get("uploadUrl"));
			} else{
				value.setVideo((String) valueMap.get("video"));
				value.setUploadToken((String) valueMap.get("uploadToken"));
				value.setUploadInstructions((List<Map<String,Object>>) valueMap.get("uploadInstructions"));
			}
			value.setUploadUrlExpiresAt((Long)valueMap.get("uploadUrlExpiresAt"));
			mediaResponse.setValue(value);
		}catch (ExternalAPIException ex){
			LOGGER.info("ExternalAPIException occurred while registering image : {}",JSONUtils.toJSON(ex));
			if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_DUPLICATE_REQUEST){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_DUPLICATE_CONTENT,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_MAX_LIMIT_REACHED){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_LIMIT_REACED,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.UNAUTHORIZED_DEFAULT){
				throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.FORBIDDEN){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_FORBIDDEN,ex.getMessage());
			}else{
				String message = getErrorMessage(ex.getMessage());
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN,message);
			}
		}catch (BirdeyeSocialException ex){
			throw new BirdeyeSocialException(ErrorCodes.valueOf(ex.getCode()),ex.getErrorCode(),ex.getMessage(),null);
		} catch(Exception ex) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN,ex.getMessage());
		}
		return mediaResponse;
	}

	private static NewShare getShareObject(LinkedinData data) {
		NewShare share = new NewShare();
		share.setVisibility(getShareVisibility());
		share.setContent(getShareContent(data));
		share.setComment(data.getText());
		return share;
	}
	
	private static NewShareContent getShareContent(LinkedinData data) {
		NewShareContent content = null;
		if(data.getMediaUrl() != null) {
			content = new NewShareContent(null,data.getMediaUrl(),null,null);
		}
		return content;
	}
	
	private static NewShareVisibility getShareVisibility(){
		NewShareVisibility visibility = new NewShareVisibility();
		visibility.setCode(NewShareVisibilityCode.ANYONE);
		return visibility;
	}
	
	@Override
	public LinkedInFeedData getFeed(String accessToken, Integer companyId, Integer start) throws JsonParseException, JsonMappingException, IOException, BirdeyeSocialException,Exception {
		LinkedInFeedData feedData = null;
		try {
			String url = "https://api.linkedin.com/v1/companies/"+companyId+"/updates?count=20&start="+start;
			LinkedInTemplate linkedIn = new LinkedInTemplate(accessToken);
			String response = linkedIn.restOperations().getForObject(url, String.class);
			feedData = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).readValue(response, LinkedInFeedData.class);
		}catch(NotAuthorizedException | InsufficientPermissionException nae) {
			throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, nae.getLocalizedMessage());
		}
		return feedData;
	}
	
	@Override
	public Boolean postComment(String accessToken, String comment, String postId,Integer companyId) {
		Boolean success = Boolean.FALSE;
		String url = "https://api.linkedin.com/v1/companies/"+companyId+"/updates/key="+postId+"/update-comments-as-company/";
		LinkedInTemplate linkedin = new LinkedInTemplate(accessToken);
		CommentObject data = new CommentObject();
		data.setComment(comment);
		ResponseEntity<String> response = linkedin.restOperations().postForEntity(url, data, String.class);
		if(response.getStatusCodeValue() == 201) {
			success=true;
		}
		return success;
	}
	
	@Override
	public void fetchDisconnectedAndStore() {

		try {
			long queueSize = redisExternalService.getPriorityQueueSize(Constants.PRIORITY_QUEUE_FOR_LINKEDIN_DISCONNECTED_ENTERPRISE);

			if (queueSize != 0) {
				LOGGER.warn(" {} {} In fetchAndStore : Priority queue is not empty",Constants.CHANNEL_DISCONNECT_PREFIX,SocialChannel.LINKEDIN.getName());
				return;
			}

			List<Long> disconnectedEnterpriseList = linkedInRepo.findDistinctEnterpriseIdByIsValid(0);
			redisExternalService.fillPriorityQueue(disconnectedEnterpriseList,Constants.PRIORITY_QUEUE_FOR_LINKEDIN_DISCONNECTED_ENTERPRISE);
		} catch (Exception e) {
			LOGGER.error("{} {} Some error occured due to {} ",Constants.CHANNEL_DISCONNECT_PREFIX,SocialChannel.LINKEDIN.getName(),e.getMessage());

		}

	}

	@Override
	public String getConnectCTA() {
		return "linkedin";
	}

	/*
	* Delete  shared posts from linkedIn on basis of accessToken and sharedPostId both fetched from DB.
	* In case of activity or post which is not shared the API doesn't support delete as we don't store the shareId in case user write some post.
	* */
	@Override
	public boolean deleteObject(String accessToken, String shareId) throws UnsupportedEncodingException {
		   // String url = DELETE_POST_URL + shareId;
		String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;
		StringBuilder builder = new StringBuilder(DELETE_POST_URL).append(URLEncoder.encode(shareId, StandardCharsets.UTF_8.toString()));

		    if(!shareId.contains(SHARE_URN_PREFIX)){
				LOGGER.info("Received request for deleting object which is other than share for LinkedIn URL {}", builder);
				return false;
			}
			LOGGER.info("Received request for API deleteObject for LinkedIn URL {}", builder);
			HttpHeaders headers = new HttpHeaders();
			headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
			headers.add("LinkedIn-Version", VERSION);
			headers.add("X-Restli-Protocol-Version","2.0.0");
			headers.add("X-RestLi-Method","DELETE");
		    headers.set(HttpHeaders.AUTHORIZATION, new StringBuilder("Bearer").append(" ").append(accessToken).toString());
			HttpEntity<Void> entity = new HttpEntity<>(headers);
			try {
				ResponseEntity<Void> responseEntity = linkedinRestTemplate.exchange(URI.create(builder.toString()), HttpMethod.DELETE, entity, Void.class);
				if(responseEntity.getStatusCode().is2xxSuccessful())
					return true;
				return false;
			}catch (HttpStatusCodeException e){
				LOGGER.error("Exception while calling API DELETE_POST_URL for linkedIn  URL {} and exception {}", DELETE_POST_URL, e.getMessage());
				return false;
			}catch (Exception e){
				LOGGER.error("Error occurred while deleting linkedIn Post url {} with exception {}",builder, e);
				return false;
			}
	}

	@Override
	public String checkUploadStatus(String urn, String accessToken, Boolean isVideo,Boolean isNewFlow) throws BirdeyeSocialException{
		LOGGER.info("Checking upload status of media for urn {}",urn);
		String uploadStatus;
		try{
			uploadStatus = (isVideo && isNewFlow) ? linkedinConnectService.checkVideoUploadStatus(urn,accessToken, isVideo)
					:linkedinConnectService.checkUploadStatus(urn,accessToken, isVideo);
		}catch (ExternalAPIException ex){
			if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_DUPLICATE_REQUEST){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_DUPLICATE_CONTENT,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_MAX_LIMIT_REACHED){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_LIMIT_REACED,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.UNAUTHORIZED_DEFAULT){
				throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.FORBIDDEN){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_FORBIDDEN,ex.getMessage());
			}else{
				String message = getErrorMessage(ex.getMessage());
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN,message);
			}
		}catch(LinkedinUploadMediaException ex) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_IMAGE_LINKEDIN,ex.getUploadStatus());
		}catch (LinkedinUploadFailedException ex) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_IMAGE_LINKEDIN,ex.getUploadStatus());
		} catch (Exception ex) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN,ex.getMessage());
		}
		return uploadStatus;
	}

	@Override
	public List<TargetAudienceResponse> getTargetAudienceList(String accessToken, String category) {
		List<TargetAudienceResponse> targetAudienceResponseList = new ArrayList<>();
		try {
			List<Elements> elements = linkedinConnectService.getTargetAudienceList(accessToken, category);
			for(Elements element: elements) {
				TargetAudienceResponse targetAudienceResponse = new TargetAudienceResponse();
				targetAudienceResponse.setUrn(element.get$URN());
				if(category.equalsIgnoreCase("regions")) {
					targetAudienceResponse.setName(element.getName().getValue());
				} else {
					targetAudienceResponse.setName(element.getName().getLocalized().getEn_US());
				}
				targetAudienceResponseList.add(targetAudienceResponse);
			}
		} catch (Exception e) {
			LOGGER.info("Exception while making list of target audience: {}",e.getMessage());
		}
		return targetAudienceResponseList;
	}

	@Override
	public LinkedInAPIResponse getLinkedInPostsForUser(String accessToken , String urn, Date lastPostDate, boolean allPosts) throws Exception {
		String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;
		HttpHeaders headers = new HttpHeaders();
		headers.add(HttpHeaders.ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add("LinkedIn-Version",VERSION);
		// headers.add("X-RestLi-Method","FINDER");
		headers.add(AUTHORIZATION, "Bearer " + accessToken);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		MultiValueMap<String,String> queryParams = new LinkedMultiValueMap<>();
		queryParams.set("author",urn);
		//Removing isDsc as for version 202301 default value for isDsc is false
		//queryParams.set("isDsc","false");
		queryParams.set("q","author");
		queryParams.set("count","100"); // count max value can be 10k
		String url = UriComponentsBuilder.fromHttpUrl(GET_REST_POST_URL).queryParams(queryParams).build().encode().toUriString();
		LOGGER.info("Received request for API get post for LinkedIn URL {}", url);
		try {
			boolean moreData;
			LinkedInAPIResponse data = new LinkedInAPIResponse();
			data.setElements(new ArrayList<>());
			do {
				ResponseEntity<LinkedInAPIResponse> response = linkedinRestTemplate.exchange(url, HttpMethod.GET, entity, LinkedInAPIResponse.class);

				LinkedInPaging paging = response.getBody().getPaging();
				if (!ObjectUtils.isEmpty(paging.getLinks())&& paging.getLinks().get(paging.getLinks().size() - 1).getRel().equals("next")) {
					moreData = true;
					url = java.net.URLDecoder.decode(LINKEDIN_API_URL.concat(paging.getLinks().get(paging.getLinks().size()-1).getHref()),
							StandardCharsets.UTF_8.name());
					LOGGER.info("Contains more posts within itself. Calling again... {}", url);
				} else {
					moreData = false;
				}

				if (allPosts) {
					data.getElements().addAll(response.getBody().getElements());
				} else {
					moreData = filterPostsBasedOnDate(lastPostDate, moreData, data, response);
				}

			} while(moreData);

			return data;
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to fetch all posts for organization - {}, HttpStatusCodeException occurred: {}", urn, clientExp.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.error("unable to fetch all posts for organization - {}, exception occurred", urn, e);
			ResponseEntity<LinkedInAPIResponse> response = linkedinRestTemplate.exchange(url,HttpMethod.GET,entity,LinkedInAPIResponse.class);
			return response.getBody();
		}
		return null;
	}

	private boolean filterPostsBasedOnDate(Date lastPostDate, boolean moreData, LinkedInAPIResponse data, ResponseEntity<LinkedInAPIResponse> response) {

		List<LinkedInElement> postsData = response.getBody().getElements();
		for (LinkedInElement postData : postsData) {
			Date date = new Date(postData.getPublishedAt());
			if (date.toInstant().isBefore(lastPostDate.toInstant())) {
				moreData = false;
				break;
			}
			data.getElements().add(postData);
		}
		return moreData;
	}

	private boolean filterPostsBasedOnDateV2(Date lastPostDate, boolean moreData, LinkedInAPIResponse data, ResponseEntity<LinkedInAPIResponse> response) {

		List<LinkedInElement> postsData = response.getBody().getElements();
		LOGGER.info("Data for filterPostsBasedOnDate {}", JSONUtils.toJSON(postsData));
		for (LinkedInElement postData : postsData) {
			Date date = new Date(postData.getLastModifiedAt());
			if (date.toInstant().isBefore(lastPostDate.toInstant())) {
				moreData = false;
				break;
			}
			data.getElements().add(postData);
		}
		return moreData;
	}

	/**
	 * Fetching all posts using POSTS API
	 *
	 * @param accessToken
	 * @param urn
	 * @param nextUrl
	 * @return
	 * @throws Exception
	 */
	@Override
	public LinkedInAPIResponse getLinkedinFeed(String accessToken, String urn, String nextUrl) throws Exception {
		String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion();
		HttpHeaders headers = new HttpHeaders();
		headers.add(HttpHeaders.ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add("LinkedIn-Version",VERSION);
		headers.add("X-RestLi-Method","FINDER");
		headers.add(AUTHORIZATION, "Bearer " + accessToken);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		String url;
		if (Objects.nonNull(nextUrl)) {
			url = java.net.URLDecoder.decode(LINKEDIN_API_URL.concat(nextUrl), StandardCharsets.UTF_8.name());
		} else {
			MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
			queryParams.set("author", urn);
			queryParams.set("isDsc", "false");
			queryParams.set("q", "author");
			queryParams.set("count", "20");
			url = UriComponentsBuilder.fromHttpUrl(GET_REST_POST_URL).queryParams(queryParams).build().encode().toUriString();
		}
		LOGGER.info("Received request for API get post for LinkedIn URL {}", url);
		try {
			ResponseEntity<LinkedInAPIResponse> response = linkedinRestTemplate.exchange(url,HttpMethod.GET,entity,LinkedInAPIResponse.class);
			return response.getBody();
		} catch (Exception e){
			throw new Exception("Unable to get post for page :" + e.getMessage());
		}
	}

	@Override
	public boolean editPost(LinkedInPostRequest linkedInPostRequest, String postId, String accessToken) throws Exception {
		String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion();
		HttpHeaders headers = new HttpHeaders();
		headers.add(HttpHeaders.ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add(AUTHORIZATION, "Bearer " + accessToken);
		headers.add("Content-Type","application/json");
		headers.add("LinkedIn-Version",VERSION);
		headers.add("X-RestLi-Method","PARTIAL_UPDATE");
		HttpEntity<?> entity = new HttpEntity<>(linkedInPostRequest,headers);
		MultiValueMap<String,String> queryParams = new LinkedMultiValueMap<>();
		//queryParams.add("oauth2_access_token",accessToken);
		//postId = URLEncoder.encode(postId,"UTF-8");
		String url = GET_REST_POST_URL+"/"+postId;
		URI uri = UriComponentsBuilder.fromHttpUrl(url).queryParams(queryParams).build().toUri();
		LOGGER.info("Linkedin Edit Post Request :{} with url :{}", JSONUtils.toJSON(linkedInPostRequest),uri);
		try {
			ResponseEntity<Void> response = linkedinRestTemplate.exchange(uri,HttpMethod.POST,entity,Void.class);
			if(Objects.nonNull(response.getStatusCode())){
				return true;
			}
			else{
				return false;
			}
		}catch (Exception e){
			throw new Exception("Unable to get post for given access token :"+ e.getMessage());
		}
	}

	@Override
	public LinkedinCommentsResponse getCommentsOnPost(String postId, String accessToken) {
		ResponseEntity<LinkedinCommentsResponse> response = null;
		StringBuilder url = new StringBuilder(LINKEDIN_SOCIAL_ACTIONS_URL).append(postId).append("/").append(COMMENT);
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			// response = linkedinRestTemplate.exchange(url, HttpMethod.GET, requestEntity, new ParameterizedTypeReference<List<LinkedInCommentResponse>>() {});
			response = linkedinRestTemplate.exchange(url.toString(), HttpMethod.GET, requestEntity, LinkedinCommentsResponse.class);
			return response.getBody();
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to getCommentsOnPost for post - {}, HttpStatusCodeException occurred: {}", postId, clientExp.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.error("unable to getCommentsOnPost for post - {}, exception occurred", postId, e);
		}
		return null;
	}

	@Override
	public LinkedinCommentsResponse getCommentsOnComment(String accessToken, String parentComment) {
		ResponseEntity<LinkedinCommentsResponse> response = null;
		StringBuilder url = new StringBuilder(LINKEDIN_SOCIAL_ACTIONS_URL).append(parentComment).append("/").append(COMMENT);
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			response = linkedinRestTemplate.exchange(url.toString(), HttpMethod.GET, requestEntity, LinkedinCommentsResponse.class);
			return response.getBody();
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to getCommentsOnComment for comment - {}, HttpStatusCodeException occurred: {}", parentComment, clientExp.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.error("unable to getCommentsOnComment for comment - {}, exception occurred", parentComment, e);
		}
		return null;
	}

	@Override
	public LinkedInCommentResponse addCommentOnComment(LinkedinCommentRequest request, String accessToken) throws Exception{
		LinkedInCommentResponse data = null;
		StringBuilder url = new StringBuilder(LINKEDIN_SOCIAL_ACTIONS_URL).append(request.getParentComment()).append("/").append(COMMENT);
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add("Content-Type", "application/json;charset=UTF-8");
			HttpEntity<?> requestEntity = new HttpEntity<>(request, headers);
			ResponseEntity<LinkedInCommentResponse> response = linkedinRestTemplate.exchange(url.toString(), HttpMethod.POST, requestEntity, LinkedInCommentResponse.class);
			data = response.getBody();
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to comment on post id : {} and comment id :{}, HttpStatusCodeException occurred: {}", request.getParentComment(),request.getObject(), clientExp.getResponseBodyAsString());
			LinkedInErrorResponse linkedInErrorResponse = JSONUtils.fromJSON(clientExp.getResponseBodyAsString(),LinkedInErrorResponse.class);
			if(Objects.isNull(linkedInErrorResponse)){
				throw clientExp;
			}
			throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST,linkedInErrorResponse.getMessage());
		} catch (Exception e) {
			LOGGER.error("unable to addReaction on entity - {}, reactionType - {}, exception occurred", request.getRoot(), request.getReactionType(), e);
			throw new BirdeyeSocialException("unable to add Reaction");
		}
		return data;
	}

	@Override
	public LinkedInCommentResponse addCommentOnPost(LinkedinCommentRequest request, String accessToken, String postUrn) throws Exception {
		LinkedInCommentResponse data = null;
		StringBuilder url = new StringBuilder(LINKEDIN_SOCIAL_ACTIONS_URL).append(postUrn).append("/").append(COMMENT);
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add("Content-Type", "application/json;charset=UTF-8");
			HttpEntity<?> requestEntity = new HttpEntity<>(request, headers);
			ResponseEntity<LinkedInCommentResponse> response = linkedinRestTemplate.exchange(url.toString(), HttpMethod.POST, requestEntity, LinkedInCommentResponse.class);
			data = response.getBody();
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to comment on post id : {}, HttpStatusCodeException occurred: {}", postUrn, clientExp.getResponseBodyAsString());
			LinkedInErrorResponse linkedInErrorResponse = JSONUtils.fromJSON(clientExp.getResponseBodyAsString(),LinkedInErrorResponse.class);
			if(Objects.isNull(linkedInErrorResponse)){
				throw clientExp;
			}
			throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST,linkedInErrorResponse.getMessage());
		} catch (Exception e) {
			LOGGER.error("unable to addReaction on entity - {}, reactionType - {}, exception occurred", request.getRoot(), request.getReactionType(), e);
			throw new BirdeyeSocialException("unable to add Reaction");
		}
		return data;
	}

	@Override
	public boolean editComment(String accessToken, String postId, String companyUrn, String text, String commentId) {
		StringBuilder url = new StringBuilder(LINKEDIN_SOCIAL_ACTIONS_URL).append(postId).append("/").append(COMMENT).append("/").
				append(commentId).append("?actor=").append(companyUrn);
		LinkedinCommentRequest request = new LinkedinCommentRequest(text);
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add("Content-Type", "application/json;charset=UTF-8");
			HttpEntity<?> requestEntity = new HttpEntity<>(request, headers);
			ResponseEntity<LinkedInCommentResponse> response = linkedinRestTemplate.exchange(url.toString(), HttpMethod.POST, requestEntity, LinkedInCommentResponse.class);
			return response.getStatusCode().equals(HttpStatus.OK);
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to editComment for organization - {} and comment - {}, HttpStatusCodeException occurred: {}", companyUrn, commentId, clientExp.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.error("unable to editComment for organization - {} and comment - {}, exception occurred", companyUrn, commentId, e);
		}
		return false;
	}

	@Override
	public boolean deleteComment(String accessToken, String postId, String companyUrn, String commentId) throws Exception {
		ResponseEntity<?> response = null;
		StringBuilder url = new StringBuilder(LINKEDIN_SOCIAL_ACTIONS_URL).append(postId).append("/").append(COMMENT).append("/")
				.append(commentId).append("?actor=").append(companyUrn);
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			response = linkedinRestTemplate.exchange(url.toString(), HttpMethod.DELETE, requestEntity, Object.class);
			if (response.getStatusCode() == HttpStatus.NO_CONTENT) {
				return true;
			}
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to delete comment on comment id  - {} and post id : {}, HttpStatusCodeException occurred: {}", commentId, postId, clientExp.getResponseBodyAsString());
			LinkedInErrorResponse linkedInErrorResponse = JSONUtils.fromJSON(clientExp.getResponseBodyAsString(),LinkedInErrorResponse.class);
			if(Objects.isNull(linkedInErrorResponse)){
				throw clientExp;
			}
			throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST,linkedInErrorResponse.getMessage());
		} catch (Exception e) {
			LOGGER.error("unable to delete comment id  - {} and post id : {}, e occurred: ", commentId, postId,e);
			throw new BirdeyeSocialException("unable to add Reaction");
		}
		return false;
	}

	@Override
	public boolean addReaction(LinkedinCommentRequest request, String companyUrn, String accessToken) throws Exception{
		String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;
		String initialUrl = LINKEDIN_REACTIONS_V2_URL.concat("?actor=").concat(companyUrn);
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add("LinkedIn-Version", VERSION);
			MultiValueMap<String,String> queryParams = new LinkedMultiValueMap<>();
			queryParams.add("actor", companyUrn);
			HttpEntity<?> requestEntity = new HttpEntity<>(request, headers);
			String url = UriComponentsBuilder.fromHttpUrl(initialUrl).queryParams(queryParams).build().encode().toUriString();
			ResponseEntity<LinkedInCommentResponse> response = linkedinRestTemplate.exchange(url, HttpMethod.POST, requestEntity, LinkedInCommentResponse.class);
			if (response.getStatusCode() == HttpStatus.CREATED) {
				LOGGER.info("Reaction {} created for companyUrn {}", request.getReactionType(), companyUrn);
				return true;
			}
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to addReaction on entity - {}, reactionType - {}, HttpStatusCodeException occurred: {}", request.getRoot(), request.getReactionType(), clientExp.getResponseBodyAsString());
			LinkedInErrorResponse linkedInErrorResponse = JSONUtils.fromJSON(clientExp.getResponseBodyAsString(),LinkedInErrorResponse.class);
			if(Objects.isNull(linkedInErrorResponse)){
				throw clientExp;
			}
			throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST,linkedInErrorResponse.getMessage());
		} catch (Exception e) {
			LOGGER.error("unable to addReaction on entity - {}, reactionType - {}, exception occurred", request.getRoot(), request.getReactionType(), e);
			throw new BirdeyeSocialException("unable to add Reaction");
		}
		return false;
	}

	@Override
	public boolean deleteReaction(String objectId, String urn, String accessToken) throws Exception{
		try {
			String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add("LinkedIn-Version", VERSION);
			headers.add("X-Restli-Protocol-Version","2.0.0");
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			StringBuilder builder = new StringBuilder(LINKEDIN_REACTIONS_V2_URL).append("/(actor:")
					.append(URLEncoder.encode(urn, StandardCharsets.UTF_8.toString())).append(",entity:")
					.append(URLEncoder.encode(objectId, StandardCharsets.UTF_8.toString())).append(")");
			ResponseEntity<Object> response = linkedinRestTemplate.exchange(URI.create(builder.toString()), HttpMethod.DELETE, requestEntity, Object.class);
			if (response.getStatusCode() == HttpStatus.NO_CONTENT) {
				LOGGER.info("Reaction deleted for entity {}", objectId);
				return true;
			}
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to remove reaction on entity - {},HttpStatusCodeException occurred: {}", objectId, clientExp.getResponseBodyAsString());
			LinkedInErrorResponse linkedInErrorResponse = JSONUtils.fromJSON(clientExp.getResponseBodyAsString(),LinkedInErrorResponse.class);
			if(Objects.isNull(linkedInErrorResponse)){
				throw clientExp;
			}
			throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST,linkedInErrorResponse.getMessage());
		} catch (Exception e) {
			LOGGER.error("unable to remove reaction on entity - {} exception occurred", objectId, e);
			throw new BirdeyeSocialException("unable to add Reaction");
		}
		return false;
	}

	@Override
	public LinkedinSocialMetadata getSocialMetadata(String objectId, String accessToken) {
		LinkedinSocialMetadata data = new LinkedinSocialMetadata();
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			StringBuilder builder = new StringBuilder(LINKEDIN_SOCIAL_METADATA_URL).append(objectId);
			ResponseEntity<LinkedinSocialMetadata> response = linkedinRestTemplate.exchange(URI.create(builder.toString()), HttpMethod.GET, requestEntity, LinkedinSocialMetadata.class);
			if (response.getStatusCode() == HttpStatus.OK) {
				data = response.getBody();
			}
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to getSocialMetadata on entity - {}, HttpStatusCodeException occurred: {}", objectId, clientExp.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.error("unable to getSocialMetadata on entity - {}, exception occurred", objectId, e);
		}
		return data;
	}

	@Override
	public LinkedinProfileDetails getPersonProfileDetailsWithImageUrl(String personId, String accessToken){
		LinkedinProfileDetails linkedinProfileDetails = getPersonProfileDetails(personId,accessToken);
		if(Objects.isNull(linkedinProfileDetails)){
			LOGGER.info("Data found is null for person Id : {}",personId);
			return null;
		}
		if(Objects.isNull(linkedinProfileDetails.getProfilePicture())){
			return linkedinProfileDetails;
		}
		String profilePictureId = linkedinProfileDetails.getProfilePicture().getDisplayImage();
		if(StringUtils.isNotEmpty(profilePictureId)) {
			profilePictureId = profilePictureId.replace(DISPLAY_MEDIA_ASSET,IMAGE_LINKEDIN);
			UploadMediaResponse mediaResponse = getLinkedinMediaUrl(profilePictureId, accessToken, false);
			if(Objects.nonNull(mediaResponse))
				linkedinProfileDetails.setProfilePictureUrl(mediaResponse.getDownloadUrl());
		}
		return linkedinProfileDetails;
	}

	@Override
	public LinkedinProfileDetails getPersonProfileDetails(String personId, String accessToken) {
		LinkedinProfileDetails data = new LinkedinProfileDetails();
		try {
			String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add("X-RestLi-Protocol-Version", "2.0.0");
			headers.add("LinkedIn-Version", VERSION);
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			StringBuilder url = new StringBuilder(LINKEDIN_PROFILE_DETAILS_URL).append("(id:").append(personId).append(")");
			MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
			queryParams.set("projection", "(localizedLastName,firstName,vanityName,lastName,id,profilePicture(displayImage~digitalmediaAsset:playableStreams))");
			String finalUrl = UriComponentsBuilder.fromHttpUrl(url.toString()).build().encode().toUriString();
			ResponseEntity<LinkedinProfileDetails> response = linkedinRestTemplate.exchange(finalUrl, HttpMethod.GET, requestEntity, LinkedinProfileDetails.class);
			if (response.getStatusCode() == HttpStatus.OK) {
				data = response.getBody();
			}
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to getPersonProfileDetails on id - {}, HttpStatusCodeException occurred: {}", personId, clientExp.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.error("unable to getPersonProfileDetails on id - {}, exception occurred", personId, e);
		}
		return data;
	}

	/**
	 * Fetching posts using UGCPosts API
	 * Initial count is 20
	 *
	 * @param accessToken
	 * @param urn
	 * @param nextUrl
	 * @return
	 * @throws Exception
	 */
	@Override
	public LinkedInAPIResponse fetchAllPostsForOrganization(String accessToken , String urn, String nextUrl) throws Exception {
		String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;

		HttpHeaders headers = new HttpHeaders();
		headers.add(HttpHeaders.ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add("X-Restli-Protocol-Version","2.0.0");
		headers.add(AUTHORIZATION, "Bearer " + accessToken);
		headers.add("LinkedIn-Version",VERSION);
		headers.add("X-RestLi-Method","FINDER");
		HttpEntity<?> entity = new HttpEntity<>(headers);
		String url;
		if (Objects.nonNull(nextUrl)) {
			url = LINKEDIN_API_URL.concat(nextUrl);
		} else {
			url = new StringBuilder(LINKEDIN_REST_POSTS_URL).append("?q=author&author=List(").append(URLEncoder.encode(urn, StandardCharsets.UTF_8.toString()))
					.append(")&count=10").toString();
		}
		LOGGER.info("Received request for API get all ugc posts for LinkedIn URL {}", url);
		try {
			ResponseEntity<LinkedInAPIResponse> response = linkedinRestTemplate.exchange(URI.create(url),HttpMethod.GET,entity,LinkedInAPIResponse.class);
			return response.getBody();
		}  catch(HttpClientErrorException httpEx) {
			if (httpEx.getStatusCode() == HttpStatus.UNAUTHORIZED) {
				throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, "not able to retrieve posts by access token for page: " + urn);
			} else {
				throw new Exception("Unable to get post all posts for organization: " + urn + " , exception: " + httpEx);
			}
		} catch(Exception e) {
			throw new Exception("Unable to get post all posts for organization: " + urn + " , exception: " + e);
		}
	}

	/**
	 * Fetching all posts using UGCPosts API
	 * No count - gets all posts based upon date
	 *
	 * @param accessToken
	 * @param urn
	 * @return
	 * @throws Exception
	 */
	@Override
	public LinkedInAPIResponse fetchAllPostsForOrganization(String accessToken , String urn, Date lastPostDate) throws Exception {
		String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;

		HttpHeaders headers = new HttpHeaders();
		headers.add(HttpHeaders.ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add("X-Restli-Protocol-Version","2.0.0");
		headers.add(AUTHORIZATION, "Bearer " + accessToken);
		headers.add("LinkedIn-Version",VERSION);
		headers.add("X-RestLi-Method","FINDER");
		HttpEntity<?> entity = new HttpEntity<>(headers);
		String url = new StringBuilder(LINKEDIN_REST_POSTS_URL).append("?q=author&author=").append(URLEncoder.encode(urn, StandardCharsets.UTF_8.toString())).toString();
		LOGGER.info("Received request for API get post for LinkedIn URL {}", url);
		try {
			boolean moreData;
			LinkedInAPIResponse data = new LinkedInAPIResponse();
			data.setElements(new ArrayList<>());
			do {
				ResponseEntity<LinkedInAPIResponse> response = linkedinRestTemplate.exchange(URI.create(url), HttpMethod.GET, entity, LinkedInAPIResponse.class);

				LinkedInPaging paging = response.getBody().getPaging();
				if (!ObjectUtils.isEmpty(paging.getLinks()) && paging.getLinks().get(paging.getLinks().size() - 1).getRel().equals("next")) {
					moreData = true;
					/*url = java.net.URLDecoder.decode(LINKEDIN_API_URL.concat(paging.getLinks().get(paging.getLinks().size()-1).getHref()),
							StandardCharsets.UTF_8.name());*/
						url=LINKEDIN_API_URL.concat(paging.getLinks().get(paging.getLinks().size()-1).getHref());
					LOGGER.info("Contains more posts within itself. Calling again... {}", url);
				} else {
					moreData = false;
				}

				moreData = filterPostsBasedOnDateV2(lastPostDate, moreData, data, response);

			} while(moreData);

			return data;
		} catch (Exception e){
			throw new Exception("Unable to get post for given access token :"+ e);
		}
	}

	private boolean filterPostsBasedOnDate(Date lastPostDate, boolean moreData, LinkedinUGCPostsResponse data, ResponseEntity<LinkedinUGCPostsResponse> response) {
		List<LinkedinUGCPostElement> postsData = response.getBody().getElements();
		for (LinkedinUGCPostElement postData : postsData) {
			Date date = new Date(postData.getFirstPublishedAt());
			if (date.toInstant().isBefore(lastPostDate.toInstant())) {
				moreData = false;
				break;
			}
			data.getElements().add(postData);
		}
		return moreData;
	}

	@Override
	public LinkedinProfileDetails fetchCompanyLogoAndDetailsWithLogoUrl(String companyId, String accessToken){
		LinkedinProfileDetails linkedinProfileDetails = fetchCompanyLogoAndDetails(companyId,accessToken);
		if(Objects.isNull(linkedinProfileDetails)){
			LOGGER.info("No data found for company id : {}",companyId);
			return null;
		}
		if(Objects.nonNull(linkedinProfileDetails.getLogoV2()) && StringUtils.isNotEmpty(linkedinProfileDetails.getLogoV2().getOriginal())) {
			String logoUrl = linkedinProfileDetails.getLogoV2().getOriginal().replace(DISPLAY_MEDIA_ASSET,IMAGE_LINKEDIN);
			UploadMediaResponse mediaResponse = getLinkedinMediaUrl(logoUrl, accessToken, false);
			linkedinProfileDetails.setLogoUrl(mediaResponse.getDownloadUrl());
		}
		return linkedinProfileDetails;
	}

	@Override
	public LinkedinProfileDetails fetchCompanyLogoAndDetails(String companyId, String accessToken) {
		LinkedinProfileDetails data = new LinkedinProfileDetails();
		try {
			String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add("X-RestLi-Protocol-Version", "2.0.0");
			headers.add("LinkedIn-Version", VERSION);
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			StringBuilder url = new StringBuilder(LINKEDIN_API_URL).append("/rest/organizations/").append(companyId);
//			MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
//			queryParams.set("projection", "(localizedName,vanityName,id,logoV2(original~:playableStreams))");
			String finalUrl = UriComponentsBuilder.fromHttpUrl(url.toString()).build().encode().toUriString();
			ResponseEntity<LinkedinProfileDetails> response = linkedinRestTemplate.exchange(finalUrl, HttpMethod.GET, requestEntity, LinkedinProfileDetails.class);
			if (response.getStatusCode() == HttpStatus.OK) {
				data = response.getBody();
			}
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to fetchProfilePicture on id - {}, HttpStatusCodeException occurred: {}", companyId, clientExp.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.error("unable to fetchProfilePicture on id - {}, exception occurred", companyId, e);
		}
		return data;
	}

	@Override
	public boolean getReactionOnEntity(String objectUrn, String urn, String accessToken) {
		try {
			HttpHeaders headers = new HttpHeaders();
			String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add("LinkedIn-Version", VERSION);
			headers.add("X-Restli-Protocol-Version","2.0.0");
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			StringBuilder builder = new StringBuilder(LINKEDIN_REACTIONS_URL).append("/(actor:")
					.append(URLEncoder.encode(urn, StandardCharsets.UTF_8.toString())).append(",entity:")
					.append(URLEncoder.encode(objectUrn, StandardCharsets.UTF_8.toString())).append(")");
			ResponseEntity<LinkedInCommentResponse> response = linkedinRestTemplate.exchange(URI.create(builder.toString()), HttpMethod.GET, requestEntity, LinkedInCommentResponse.class);
			LOGGER.info("Object urn {}, response for getReactionOnEntity - {}", objectUrn, response.toString());
			if (response.getStatusCode() == HttpStatus.OK) {
				return true;
			}
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to getReactionOnEntity on entity - {}, HttpStatusCodeException occurred: {}", objectUrn, clientExp.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.error("unable to getReactionOnEntity on entity - {}, exception occurred", objectUrn, e);
		}
		return false;
	}

	/**
	 * This api deletes both ugcPost and share urn posts
	 *
	 * @param accessToken
	 * @param postId
	 * @return
	 */
	@Override
	public boolean deletePost(String accessToken, String postId) throws Exception {
		try {
			HttpHeaders headers = new HttpHeaders();
			String version = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add("LinkedIn-Version", version);
			headers.add("X-Restli-Protocol-Version","2.0.0");
			headers.add("X-RestLi-Method","DELETE");
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			StringBuilder builder = new StringBuilder(LINKEDIN_REST_POSTS_URL).append("/").append(URLEncoder.encode(postId, StandardCharsets.UTF_8.toString()));
			ResponseEntity<Void> response = linkedinRestTemplate.exchange(URI.create(builder.toString()), HttpMethod.DELETE, requestEntity, Void.class);
			if(response.getStatusCode().is2xxSuccessful())
				return true;
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to deletePost on entity - {}, HttpStatusCodeException occurred: {}", postId, clientExp.getResponseBodyAsString());
			if(clientExp.getStatusCode().equals(HttpStatus.NOT_FOUND)) {
				throw new BirdeyeSocialException(ErrorCodes.CLIENT_ERROR_404, clientExp.getMessage());
			}
			throw new Exception("unable to delete Post");
		} catch (Exception e) {
			LOGGER.error("unable to deletePost on entity - {}, exception occurred", postId, e);
			throw new Exception("unable to delete Post");
		}
		return false;
	}

	@Override
	public LinkedInAPIResponse getPostInsightResponse(String accessToken, String postId, String urn) {
		return linkedinConnectService.getPostInsightResponse(accessToken, postId, urn);
	}

	@Override
	public LinkedInAPIResponse getFollowerCountForOrganization(String accessToken, String urn) {
		HttpHeaders headers = new HttpHeaders();
		headers.add(HttpHeaders.ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add(AUTHORIZATION, "Bearer " + accessToken);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		String url = new StringBuilder(FOLLOWER_COUNT_URL).append(urn).append(FOLLOWER_COUNT_URL_PARAM).toString();
		URI uri = UriComponentsBuilder.fromHttpUrl(url).build().toUri();
		try {
			ResponseEntity<LinkedInAPIResponse> response = linkedinRestTemplate.exchange(uri, HttpMethod.GET, entity, LinkedInAPIResponse.class);
			LOGGER.info("[Service] api response for linkedin organization followers {}", response);
			return response.getBody();
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("getFollowerCountForOrganization HttpStatusCodeException occurred: {}", clientExp.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.error("getFollowerCountForOrganization exception occurred", e);
		}
		return null;
	}

	// start time inclusive
	// end time exclusive
	@Override
	public LinkedInAPIResponse getTimeBasedFollowerCountForOrganization(String accessToken, String urn, long startTime,
																		long endTime, LinkedInTimeGranularity time, boolean isEndDateReq) {
		String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;

		HttpHeaders headers = new HttpHeaders();
		headers.add(HttpHeaders.ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add(AUTHORIZATION, "Bearer " + accessToken);
		headers.add("LinkedIn-Version", VERSION); // using linkedin api of version June 2022
		HttpEntity<?> entity = new HttpEntity<>(headers);
		String url = TIME_BASED_FOLLOWER_COUNT_URL + urn + AND + TIME_RANGE_START + startTime +AND +TIME_GRANULARITY_TYPE + time;
		if(isEndDateReq)
			url +=  AND + TIME_RANGE_END + endTime;
		URI uri = UriComponentsBuilder.fromHttpUrl(url).build().toUri();
		LOGGER.info("getTimeBasedFollowerCountForOrganization url :{}",uri );
		try {
			ResponseEntity<LinkedInAPIResponse> response = linkedinRestTemplate.exchange(uri, HttpMethod.GET, entity, LinkedInAPIResponse.class);
			LOGGER.info("[Service] api response for linkedin followers {}", response);
			return response.getBody();
		} catch (Exception e) {
			LOGGER.info("Error occurred while fetching follower count for organization {}", urn);
			return null;
		}
	}

	@Override
	public LinkedInPageSearch getPageMentionWithData(String search,String accessToken){
		LinkedInPageSearch linkedInPageSearch = getPageMentions(search,accessToken);
        if(Objects.isNull(linkedInPageSearch) || CollectionUtils.isEmpty(linkedInPageSearch.getElements())){
			LOGGER.info("No data found for search string : {}",search);
			return null;
		}
		List<Integer> profileIds = new ArrayList<>();
		linkedInPageSearch.getElements().forEach(element ->
				profileIds.add(Integer.valueOf(element.getEntity().split(LINKEDIN_ORG_PREFIX)[1])));
		if (CollectionUtils.isEmpty(profileIds)){
			LOGGER.info("No data found for search string : {}",search);
			return null;
		}
		LinkedinOrganizationInfo linkedinOrganizationInfoForIds =
				linkedinConnectService.linkedinOrganizationLookUpInfoForIds(profileIds,accessToken);
		if(Objects.isNull(linkedinOrganizationInfoForIds) || MapUtils.isEmpty(linkedinOrganizationInfoForIds.getResults())){
			LOGGER.info("No data found for search string : {} and profile ids :{}",search,profileIds);
			return null;
		}
		List<String> imageIds = new ArrayList<>();
        List<LinkedInMentionsData> elements = convertDatToMentionResponse(linkedinOrganizationInfoForIds, imageIds);
		prepareLogoUrlResponseForMentionData(imageIds,elements,accessToken);
		linkedInPageSearch.setElements(elements);
		return linkedInPageSearch;
	}

	private void prepareLogoUrlResponseForMentionData(List<String> imageIds, List<LinkedInMentionsData> elements,String accessToken) {
		Map<String,LinkedinImageData> results = new HashMap<>();
		//use single call to get images from linkedin
		imageIds.forEach(image -> {
			LinkedinImageData imageData = linkedinConnectService.linkedinImageFromId(image,accessToken);
			results.put(image,imageData);
		});
		prepareLogoUrlData(elements, results);
	}

	@Override
	public LinkedInPageSearch getPageMentions(String search, String accessToken) {
		MultiValueMap<String,String> queryParams = new LinkedMultiValueMap<>();
		queryParams.add("query",search);
//		queryParams.add("projection","(elements*(entity~(id,localizedName,logoV2(original~:playableStreams))))");
		queryParams.add("count","10");

		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set(HttpHeaders.AUTHORIZATION, "Bearer" + " " + accessToken);
		LOGGER.info("headers {}",headers);
		HttpEntity<Void> entity = new HttpEntity<>(headers);
		String url = UriComponentsBuilder.fromHttpUrl(COMPANY_SEARCH_URL).queryParams(queryParams).build().toUriString();
		try {
			ResponseEntity<LinkedInPageSearch> response = linkedinRestTemplate.exchange(url,HttpMethod.GET,entity,LinkedInPageSearch.class);
			return response.getBody();
		}catch (HttpStatusCodeException e){
			LOGGER.info("[Linkedin Page Search] HttpStatusCodeException part where http status is = {}",e.getStatusCode().value());
			LOGGER.error("HttpStatusCodeException while fetching social mentions of linkedin API for URL {} :: {}", url, e.getResponseBodyAsString());
			if(HttpStatus.UNAUTHORIZED.equals(e.getStatusCode())) {
				throw new SocialPageUpdateException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN,e.getMessage());
			}
			throw new SocialPageUpdateException(ErrorCodes.UNABLE_TO_FETCH_MENTIONS,e.getMessage());
		}catch (RestClientException e) {
			LOGGER.error("[Linkedin Social Search] Exception in while searching pages of linkedin API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
		} catch (Exception e) {
			LOGGER.error("[Linkedin Social Search] Exception in while searching pages of linkedin API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
	}




	/**
	 * Specially fetching video url using projection using UGCPosts
	 *
	 * @param accessToken
	 * @param postUrn
	 * @return
	 * @throws Exception
	 */
	@Override
	public SharePostRequestLinkedin fetchUGCPostByUrn(String accessToken, String postUrn) throws Exception {
		String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;


		HttpHeaders headers = new HttpHeaders();
		headers.add("X-Restli-Protocol-Version","2.0.0");
		headers.add(AUTHORIZATION, "Bearer " + accessToken);
		headers.add("LinkedIn-Version",VERSION);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		// using projection to fetch video urls
		String url = new StringBuilder(LINKEDIN_REST_POSTS_URL).append("/").append(URLEncoder.encode(postUrn, StandardCharsets.UTF_8.toString())).toString();
		LOGGER.info("Received request for UGC Post API to get video url for LinkedIn - URL {}", url);
		try {
			ResponseEntity<SharePostRequestLinkedin> response = linkedinRestTemplate.exchange(URI.create(url),HttpMethod.GET,entity, SharePostRequestLinkedin.class);
			return response.getBody();
		} catch (Exception e){
			throw new Exception("Unable to get post for given access token :"+ e.getMessage());
		}
	}

	@Override
	public boolean finalizeMedia(RegisterLinkedinMediaResponseV2 videoResponse,List<String> etag,String accessToken) {

		try{
			FinalizeLinkedinVideoRequest finalizeLinkedinVideoRequest = prepareFinalizeMediaRequest(videoResponse,etag);
			return linkedinConnectService.finalizeMediaRequest(finalizeLinkedinVideoRequest,accessToken);
		}catch (ExternalAPIException ex){
			if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_DUPLICATE_REQUEST){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_DUPLICATE_CONTENT,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_MAX_LIMIT_REACHED){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_LIMIT_REACED,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.UNAUTHORIZED_DEFAULT){
				throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.FORBIDDEN){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_FORBIDDEN,ex.getMessage());
			}else{
				String message = getErrorMessage(ex.getMessage());
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN,message);
			}
		}catch(Exception ex) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN, ex.getMessage());
		}
	}

	@Override
	public boolean finalizeMedia(String videoId,List<String> etag,String accessToken,String mediaUploadUrl) {
		try{
			FinalizeLinkedinVideoRequest finalizeLinkedinVideoRequest = prepareFinalizeMediaRequestForMultipart(videoId,etag,mediaUploadUrl);
			return linkedinConnectService.finalizeMediaRequest(finalizeLinkedinVideoRequest,accessToken);
		}catch (ExternalAPIException ex){
			if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_DUPLICATE_REQUEST){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_DUPLICATE_CONTENT,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_MAX_LIMIT_REACHED){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_LIMIT_REACED,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.UNAUTHORIZED_DEFAULT){
				throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.FORBIDDEN){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_FORBIDDEN,ex.getMessage());
			}else{
				String message = getErrorMessage(ex.getMessage());
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN,message);
			}
		}catch(Exception ex) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN, ex.getMessage());
		}
	}

	@Override
	public String uploadVideodMedia( byte[] media, String companyUrn, String apiUrl, String token)  {
		LOGGER.info("Uploading media on linkedin for company urn {}",companyUrn);
		try{
			return linkedinConnectService.uploadVideoMedia(media,apiUrl,token);
		}catch (ExternalAPIException ex){
			if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_DUPLICATE_REQUEST){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_DUPLICATE_CONTENT,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.LINKEDIN_MAX_LIMIT_REACHED){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_LIMIT_REACED,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.UNAUTHORIZED_DEFAULT){
				throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN,ex.getMessage());
			}else if(ex.getCode() == ExternalAPIErrorCode.FORBIDDEN){
				throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MEMBER_FORBIDDEN,ex.getMessage());
			}else{
				String message = getErrorMessage(ex.getMessage());
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN,message);
			}
		}catch(Exception ex) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_POST_ON_LINKEDIN, ex.getMessage());
		}
	}

	private FinalizeLinkedinVideoRequest prepareFinalizeMediaRequest(RegisterLinkedinMediaResponseV2 videoResponse, List<String> etag) {

		FinalizeLinkedinVideoRequest finalizeLinkedinVideoRequest= new FinalizeLinkedinVideoRequest();
		FinalizeUploadRequest finalizeUploadRequest= new FinalizeUploadRequest();
		finalizeUploadRequest.setUploadedPartIds(etag);
		finalizeUploadRequest.setVideo(videoResponse.getValue().getVideo());
		finalizeUploadRequest.setUploadToken(videoResponse.getValue().getUploadToken());
		finalizeLinkedinVideoRequest.setFinalizeUploadRequest(finalizeUploadRequest);
		return finalizeLinkedinVideoRequest;
	}

	private FinalizeLinkedinVideoRequest prepareFinalizeMediaRequestForMultipart(String videoId, List<String> etag) {
		FinalizeLinkedinVideoRequest finalizeLinkedinVideoRequest= new FinalizeLinkedinVideoRequest();
		FinalizeUploadRequest finalizeUploadRequest= new FinalizeUploadRequest();
		finalizeUploadRequest.setUploadedPartIds(etag);
		finalizeUploadRequest.setVideo(videoId);
		finalizeLinkedinVideoRequest.setFinalizeUploadRequest(finalizeUploadRequest);
		return finalizeLinkedinVideoRequest;
	}
	private FinalizeLinkedinVideoRequest prepareFinalizeMediaRequestForMultipart(String videoId, List<String> etag,String mediaUploadUrl) {
		FinalizeLinkedinVideoRequest finalizeLinkedinVideoRequest= new FinalizeLinkedinVideoRequest();
		FinalizeUploadRequest finalizeUploadRequest= new FinalizeUploadRequest();
		finalizeUploadRequest.setUploadedPartIds(etag);
		finalizeUploadRequest.setUploadToken(mediaUploadUrl);
		finalizeUploadRequest.setVideo(videoId);
		finalizeLinkedinVideoRequest.setFinalizeUploadRequest(finalizeUploadRequest);
		return finalizeLinkedinVideoRequest;
	}

	public String encodeListURN(List<String> urn){
		return urn.stream().map(URLEncoder::encode).collect(Collectors.joining(","));
	}


	@Override
	public LinkedinOrganizationInfo fetchOrganizationLookupDetailsWithLogoUrl(String companyId, String accessToken){
		LinkedinOrganizationInfo linkedinOrganizationInfo = fetchOrganizationLookupDetails(companyId,accessToken);
		if(Objects.isNull(linkedinOrganizationInfo) || MapUtils.isEmpty(linkedinOrganizationInfo.getResults())){
			LOGGER.info("No data found for company : {}",companyId);
			return null;
		}
		Map<String,LinkedinProfileDetails> results = linkedinOrganizationInfo.getResults();
		results.forEach((key,value) -> {
            if(Objects.nonNull(value.getLogoV2()) && StringUtils.isNotEmpty(value.getLogoV2().getOriginal())) {
				String logoUrl = value.getLogoV2().getOriginal().replace(DISPLAY_MEDIA_ASSET, IMAGE_LINKEDIN);
				UploadMediaResponse mediaResponse = getLinkedinMediaUrl(logoUrl, accessToken, false);
				value.setLogoUrl(mediaResponse.getDownloadUrl());
			}
		});
		return linkedinOrganizationInfo;
	}

	@Override
	public  LinkedinOrganizationInfo fetchOrganizationLookupDetails(String companyId, String accessToken) {
		ResponseEntity<LinkedinOrganizationInfo> responseEntity = null;
		try {
			String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;

			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add("X-RestLi-Protocol-Version", "2.0.0");
			headers.add("LinkedIn-Version", VERSION);
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			StringBuilder url = new StringBuilder(LINKEDIN_API_URL).append("/rest/organizationsLookup?ids=List(").append(companyId).append(")");
//			MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
//			queryParams.set("projection", "(results*(localizedName,vanityName,id,logoV2(original~:playableStreams)))");
			String finalUrl = UriComponentsBuilder.fromHttpUrl(url.toString()).build().encode().toUriString();
			LOGGER.info("Organization Lookup API url:{}", finalUrl);
			responseEntity = linkedinRestTemplate.exchange(finalUrl, HttpMethod.GET, requestEntity,LinkedinOrganizationInfo.class);

		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to fetchProfilePicture organizationLookup API on id - {}, HttpStatusCodeException occurred: {}", companyId, clientExp.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.error("unable to fetchProfilePicture organizationLookup API on id - {}, exception occurred", companyId, e);
		}
		return responseEntity.getBody();
	}


	@Override
	public UploadMediaResponse getLinkedinMediaUrl(String urn, String accessToken, Boolean isVideo) {
		String status = null;
		try{
			String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;

			String url=isVideo?LINKEDIN_VIDEO_MEDIA_URL:LINKEDIN_IMAGE_MEDIA_URL;
			StringBuilder builder = new StringBuilder(url).append("/")
					.append(URLEncoder.encode(urn,StandardCharsets.UTF_8.toString()));
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add(X_RESTLI_PROTOCOL_VERSION,"2.0.0");
			headers.add("LinkedIn-Version", VERSION);

			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			LOGGER.info("Media URL: {}",builder);
			ResponseEntity<UploadMediaResponse> responseEntity = linkedinRestTemplate.exchange(URI.create(builder.toString()), HttpMethod.GET, requestEntity, UploadMediaResponse.class);
			UploadMediaResponse uploadMediaResponse = responseEntity.getBody();
			LOGGER.info("Response: {}",uploadMediaResponse);
			return uploadMediaResponse;
		} catch (HttpStatusCodeException e){
			LOGGER.info("HTTP Error from LinkedIn during media get status: {}",e.getResponseBodyAsString());
			if(e.getStatusCode().is5xxServerError()) {
				LOGGER.error("InternalServerException while checking  media {}",e.getMessage());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR,e.getStatusCode().getReasonPhrase());
			}
			if(e.getStatusCode().is4xxClientError()) {
				LOGGER.error("RestClientException while checking  media {}", e.getMessage());
			}
		}catch (Exception e){
			LOGGER.error("Error occurred while checking  media {}", e.getMessage());
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN,e.getMessage(),e);
		}

		return null;
	}

	@Override
	public LinkedinPostResponse getSocialNotificationPosts(String urn, String accessToken, Integer postCount,Long startTime,Long endTime) throws UnsupportedEncodingException {
		String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;

		int counter = 100;
		String url;
		if(ObjectUtils.isEmpty(startTime)){
			url = new StringBuilder(ORGANIZATION_NOTIFICATION_URL).append("actions=List(COMMENT,SHARE_MENTION)&organizationalEntity=").
					append(URLEncoder.encode(urn,
							StandardCharsets.UTF_8.toString()))
					.append("&count="+counter).toString();
		}
		else {
			url = new StringBuilder(ORGANIZATION_NOTIFICATION_URL).append("actions=List(COMMENT,SHARE_MENTION,ADMIN_COMMENT)&organizationalEntity=").
					append(URLEncoder.encode(urn,
							StandardCharsets.UTF_8.toString()))
					.append("&count=" + counter)
					.append("&")//(start:1701778083000,end:1701869989606)
					.append(NOTIFICATION_TIME_RANGE_START).append("(start:").append(startTime).append(",end:").append(endTime).append(")")
					.toString();
		}

		LOGGER.info("Request url for getLinkedinNotificationPosts: {}",url);
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.add("LinkedIn-Version", VERSION);
		headers.add("X-Restli-Protocol-Version", "2.0.0");
		headers.set(HttpHeaders.AUTHORIZATION, new StringBuilder("Bearer").append(" ").append(accessToken).toString());
		HttpEntity<Void> entity = new HttpEntity<>(headers);

		try {
			boolean moreData;
			LinkedinPostResponse data = new LinkedinPostResponse();
			data.setElements(new ArrayList<>());
			do {
			ResponseEntity<LinkedinPostResponse> response = linkedinRestTemplate.exchange(URI.create(url), HttpMethod.GET, entity, LinkedinPostResponse.class);

				LinkedInPaging paging = response.getBody().getPaging();
				if (!ObjectUtils.isEmpty(paging.getLinks()) && paging.getLinks().get(paging.getLinks().size() - 1).getRel().equals("next")) {
					moreData = true;
					url = LINKEDIN_REST_URL.concat(paging.getLinks().get(paging.getLinks().size() - 1).getHref());
					LOGGER.info("Contains more posts within itself. Calling again... {}", url);

				} else {
					moreData = false;
				}
				data.getElements().addAll(response.getBody().getElements());
				data.setPaging(response.getBody().getPaging());
			} while (moreData);
			return data;
		} catch (RestClientResponseException e) {
			LOGGER.error("RestClientResponseException while getting linkedin notification posts for url: {}, error: {}",
					url, e.getResponseBodyAsString());
			throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_NOTIFICATION_ERROR, e.getMessage());
		}catch (Exception e) {
			LOGGER.error("Exception while getting linkedin notification posts info for url: {}, error: {} ",
					url, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED, e.getMessage());
		}
	}

	@Override
	public LinkedinMediaNotificationResposne getLinkedinShareMediaUrl(String activityURN, String accessToken) {
		String url=LINKEDIN_MEDIA_SHARE_URL+activityURN;

		LOGGER.info("Received request for API get share post media for LinkedIn URL {}", url);

		HttpHeaders headers = new HttpHeaders();
		String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.add("LinkedIn-Version", VERSION);
		headers.set(HttpHeaders.AUTHORIZATION, new StringBuilder("Bearer").append(" ").append(accessToken).toString());
		HttpEntity<Void> entity = new HttpEntity<>(headers);
		try {
			ResponseEntity<LinkedinMediaNotificationResposne> responseEntity = linkedinRestTemplate.exchange(url, HttpMethod.GET, entity, LinkedinMediaNotificationResposne.class);

			return  responseEntity.getBody();
		} catch (RestClientResponseException e) {
			LOGGER.error("RestClientResponseException while getting linkedin media posts for url: {}, error: {}",
					url, e.getResponseBodyAsString());
			throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MENTION_ERROR, e.getMessage());
		}catch (Exception e) {
			LOGGER.error("Exception while getting linkedin media posts info for url: {}, error: {} ",
					url, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MENTION_ERROR, e.getMessage());
		}
	}

	/*@Override
	public SharePostRequest getLinkedinUGCMediaUrl(String activityURN, String accessToken) {

		String url=LINKEDIN_MEDIA_UGC_URL+activityURN.concat("?projection=(specificContent(com.linkedin.ugc.ShareContent(media(*(media~:playableStreams)))))");

		LOGGER.info("Received request for API get ugc post media for LinkedIn URL {}", url);

		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.add("LinkedIn-Version", VERSION);
		headers.set(HttpHeaders.AUTHORIZATION, new StringBuilder("Bearer").append(" ").append(accessToken).toString());
		HttpEntity<Void> entity = new HttpEntity<>(headers);
		try {
			ResponseEntity<SharePostRequest> responseEntity = linkedinRestTemplate.exchange(url, HttpMethod.GET, entity, SharePostRequest.class);

			return  responseEntity.getBody();
		} catch (RestClientResponseException e) {
			LOGGER.error("RestClientResponseException while getting linkedin media ugc posts for url: {}, error: {}",
					url, e.getResponseBodyAsString());
			throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MENTION_ERROR, e.getMessage());
		}catch (Exception e) {
			LOGGER.error("Exception while getting linkedin media ugc posts info for url: {}, error: {} ",
					url, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_MENTION_ERROR, e.getMessage());
		}
		return Optional.ofNullable(responseEntity.getBody());
	}*/

	@Override
	public void updateLinkedinMapping(LinkedinUpdateFieldRequest updateRequest) {

		int sucessCounter=0;
		int failedCounter=0;

		List<BusinessLinkedinPage> businessLinkedinPages=linkedInRepo.findAllValidPagesWhereProfileIdIsNotNull(updateRequest.getId());

		if(CollectionUtils.isNotEmpty(businessLinkedinPages)) {
			LOGGER.info("[Linkedin] businessLinkedinPages count id {} ", businessLinkedinPages.size());
		}
		else{
			LOGGER.info("[Linkedin] No businessLinkedinPages found for update request");
			return;
		}

		for (BusinessLinkedinPage data : businessLinkedinPages) {
			String accessToken= String.valueOf(data.getAccessToken());
			String logoUrl="";
			String vanityName="";
			try {
			LinkedinProfileDetails profileDetails = fetchCompanyLogoAndDetailsWithLogoUrl(
					(data.getProfileId()),accessToken);
			if (StringUtils.isNotEmpty(profileDetails.getLogoUrl())) {
				logoUrl = profileDetails.getLogoUrl();
			}
			if (Objects.nonNull(profileDetails.getVanityName())) {
				vanityName = profileDetails.getVanityName();
			}
			linkedInRepo.updatePageDetailById(logoUrl,vanityName,data.getId());
			sucessCounter++;
			} catch (Exception e) {
				LOGGER.info("[Linkedin] something went while updating the logoUrl for id {} . Error", data.getId(), e);
				failedCounter++;
			}

		}
		LOGGER.info("[Linkedin] Pages updated with success counter:{} and failed counter :{} with total linkedin pages:{}",sucessCounter,failedCounter,businessLinkedinPages.size());
	}

	@Override
	public List<ApprovalPageInfo> findByLinkedinProfileId(String pageId) {
		return linkedInRepo.findByProfileInLite(pageId);
	}

	@Override
	public LinkedInCommentResponse getCommentsDetails(String accessToken, String urn, String commentId) {
		ResponseEntity<LinkedInCommentResponse> response = null;
		try {
			String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;

			String url = new StringBuilder(LINKEDIN_SOCIAL_REST_ACTIONS_URL_V2).append(urn).append("/").append(COMMENT).append("/")
					.append(URLEncoder.encode(commentId, StandardCharsets.UTF_8.toString())).toString();
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add("LinkedIn-Version", VERSION);
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			LOGGER.info("Request url for getSharedUrn: {}",url);
			response = linkedinRestTemplate.exchange(URI.create(url), HttpMethod.GET, requestEntity, LinkedInCommentResponse.class);
			return response.getBody();
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to getCommentsDetails for comment - {}, HttpStatusCodeException occurred: {}", commentId, clientExp.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.error("unable to getCommentsDetails for comment - {}, exception occurred", commentId, e);
		}
		return null;
	}

	@Override
	public Boolean getCommentsDeleteDetails(String accessToken, String urn, String commentId) {
		ResponseEntity<LinkedInCommentResponse> response = null;
		try {
			String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;

			String url = new StringBuilder(LINKEDIN_SOCIAL_REST_ACTIONS_URL_V2).append(urn).append("/").append(COMMENT).append("/")
					.append(URLEncoder.encode(commentId, StandardCharsets.UTF_8.toString())).toString();
			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add("LinkedIn-Version", VERSION);
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			LOGGER.info("Request url for getSharedUrn: {}",url);
			response = linkedinRestTemplate.exchange(URI.create(url), HttpMethod.GET, requestEntity, LinkedInCommentResponse.class);
			if (response != null) {
				return false;
			}
		}
		catch(HttpStatusCodeException e){
			LOGGER.error("HttpStatusCodeException while calling linkedin comment API for commentId {} :: {}", commentId, e.getResponseBodyAsString());
			LinkedInErrorResponse linkedInErrorResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), LinkedInErrorResponse.class);
			if(404==(linkedInErrorResponse.status)) {
				return true;
			}
			LOGGER.error("unable to getCommentsDetails for comment - {}, exception occurred", commentId, e);
		} catch (Exception e) {
			LOGGER.error("unable to getCommentsDetails for comment - {}, exception occurred", commentId, e);
		}
		return false;
	}

	@Override
	public Optional<Map<String, Object>> getSharedUrn(String accessToken, String activityURN) {

		ResponseEntity<Map<String, Object>> responseEntity = null;

		try {
			String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;

			HttpHeaders headers = new HttpHeaders();
			headers.add(AUTHORIZATION, "Bearer " + accessToken);
			headers.add("LinkedIn-Version", VERSION);
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);
			String url = new StringBuilder(LINKEDIN_SOCIAL_REST_ACTIVITY_URL_V2).append(activityURN).toString();

			LOGGER.info("Request url for getSharedUrn: {}",url);
			responseEntity = linkedinRestTemplate.exchange(URI.create(url), HttpMethod.GET, requestEntity, new ParameterizedTypeReference<Map<String, Object>>() {
			});

		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to getSharedUrn for comment - {}, HttpStatusCodeException occurred: {}", activityURN, clientExp.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.error("unable to getSharedUrn for comment - {}, exception occurred", activityURN, e);
		}
		return (responseEntity != null && responseEntity.getBody() != null)
				? Optional.of(responseEntity.getBody())
				: Optional.empty();
	}

	@Override
	public LinkedinCommentsResponse getCommentsOnPostV2(String postId, String accessToken, int commentCount) {
		String VERSION = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLinkedinVersion() ;


		HttpHeaders headers = new HttpHeaders();
		headers.add(HttpHeaders.ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add("X-Restli-Protocol-Version","2.0.0");
		headers.add(AUTHORIZATION, "Bearer " + accessToken);
		headers.add("LinkedIn-Version", VERSION);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		int counter = 20;
		try {
			String url  = new StringBuilder(LINKEDIN_SOCIAL_REST_ACTIONS_URL_V2).append(URLEncoder.encode(postId, StandardCharsets.UTF_8.toString()))
					.append("/")
					.append(COMMENT)
					.append("?count="+counter).toString();
			LOGGER.info("Received request for API getCommentsOnPostV2( for LinkedIn URL {}", url);
			boolean moreData;
			LinkedinCommentsResponse data = new LinkedinCommentsResponse();
			data.setElements(new ArrayList<>());
			do {
				ResponseEntity<LinkedinCommentsResponse> response = linkedinRestTemplate.exchange(URI.create(url), HttpMethod.GET, entity, LinkedinCommentsResponse.class);
				commentCount = commentCount - counter;
				LinkedInPaging paging = response.getBody().getPaging();
				if (commentCount > 0 && !ObjectUtils.isEmpty(paging.getLinks()) && paging.getLinks().get(paging.getLinks().size() - 1).getRel().equals("next")) {
					moreData = true;
					url = LINKEDIN_API_URL.concat(paging.getLinks().get(paging.getLinks().size() - 1).getHref());
					LOGGER.info("Contains more posts within itself. Calling again... {}", url);

				} else {
					moreData = false;
				}
				data.getElements().addAll(response.getBody().getElements());
				data.setPaging(response.getBody().getPaging());

			} while (moreData);
			return data;
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("unable to getCommentsOnPostV2 for comment - {}, HttpStatusCodeException occurred: {}", postId, clientExp.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.error("unable to getCommentsOnPostV2 for comment - {}, exception occurred", postId, e);
		}
		return null;
	}

	@Override
	public List<String> findByBusinessIdIn(List<Integer> businessIds) {
		return linkedInRepo.findDistinctPageIdByBusinessIdsIn(businessIds);
	}

	@Override
	public List<SocialBusinessPageInfo> findByBusinessIds(List<Integer> businessIds) {
		return linkedInRepo.findSocialBusinessPageInfoByBusinessIdIn(businessIds);
	}

	@Override
	public LinkedInAPIResponse getPostVideoViewResponse(String accessToken, String urn) {
		try {
			return linkedinConnectService.getPostVideoViewsResponse(accessToken, urn);
		} catch (Exception e) {
			LOGGER.error("error while fetching video insights for linkedin for urn: {}, error: {}", urn, e.getMessage());
			return null;
		}
	}
}
