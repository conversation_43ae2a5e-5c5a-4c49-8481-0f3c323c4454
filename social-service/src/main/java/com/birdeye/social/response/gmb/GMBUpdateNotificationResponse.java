package com.birdeye.social.response.gmb;

import java.io.Serializable;

import com.birdeye.social.constant.GMBNotificationType;
import com.birdeye.social.external.request.google.GMBQuestionDTO;
import com.birdeye.social.model.GMBNotificationRequest;
import com.birdeye.social.model.GMBNotificationReviewRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * author <PERSON><PERSON><PERSON>
 */	
@JsonIgnoreProperties(ignoreUnknown = true)
public class GMBUpdateNotificationResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private String businessId;
    private GMBNotificationReviewRequest gmbNotificationRequest;
    
    
	public GMBUpdateNotificationResponse() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	public GMBUpdateNotificationResponse(String businessId, GMBNotificationReviewRequest gmbNotificationReviewRequest) {
		super();
		this.businessId = businessId;
		this.gmbNotificationRequest = gmbNotificationReviewRequest;
	}

	public String getBusinessId() {
		return businessId;
	}
	public void setBusinessId(String businessId) {
		this.businessId = businessId;
	}
	public GMBNotificationReviewRequest getGmbNotificationRequest() {
		return gmbNotificationRequest;
	}
	public void setGmbNotificationRequest(GMBNotificationReviewRequest gmbNotificationRequest) {
		this.gmbNotificationRequest = gmbNotificationRequest;
	}
	@Override
	public String toString() {
		return "GMBUpdateNotificationResponse [businessId=" + businessId + ", gmbNotificationRequest="
				+ gmbNotificationRequest + "]";
	}
    
    
    
}
