package com.birdeye.social.response.gmb;

import java.io.Serializable;

import com.birdeye.social.constant.GMBNotificationType;
import com.birdeye.social.external.request.google.GMBQuestionDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * author <PERSON><PERSON><PERSON>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class GMBQnaNotificationResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private String businessId;
    private String questionId;
    private GMBQuestionDTO.QuestionDTO questionDTO;
    private String questionName;
    private String answerId;
    private GMBNotificationType gmbNotificationType;
    public String getQuestionName() {
        return questionName;
    }
    public void setQuestionName(String questionName) {
        this.questionName = questionName;
    }
    public GMBQuestionDTO.QuestionDTO getQuestionDTO() {
        return questionDTO;
    }
    public void setQuestionDTO(GMBQuestionDTO.QuestionDTO questionDTO) {
        this.questionDTO = questionDTO;
    }
    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public String getAnswerId() {
        return answerId;
    }

    public void setAnswerId(String answerId) {
        this.answerId = answerId;
    }

    public GMBNotificationType getGmbNotificationType() {
        return gmbNotificationType;
    }

    public void setGmbNotificationType(GMBNotificationType gmbNotificationType) {
        this.gmbNotificationType = gmbNotificationType;
    }

    @Override
    public String toString() {
        return "GMBQnaNotificationResponse [businessId=" + businessId + ", questionId=" + questionId + ", answerId="+", questionDTO=" + questionDTO
                + answerId + ", gmbNotificationType=" + gmbNotificationType + "]";
    }


}
