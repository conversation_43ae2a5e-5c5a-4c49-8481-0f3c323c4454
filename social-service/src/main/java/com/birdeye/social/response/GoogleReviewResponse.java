package com.birdeye.social.response;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;


import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GoogleReviewResponse
{
    private Result result;
    private String status;
    private String errorMessage;

    public Result getResult()
    {
        return result;
    }

    public void setResult(Result result)
    {
        this.result = result;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getErrorMessage()
    {
        return errorMessage;
    }

    @JsonProperty(value = "error_message")
    public void setErrorMessage(String errorMessage)
    {
        this.errorMessage = errorMessage;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result
    {
        private Double rating = 0.0;
        private List<Review> reviews;
        private String url;

        private Integer userRatingsTotal = 0;

        public Double getRating()
        {
            return this.rating;
        }

        public void setRating(Double rating)
        {
            this.rating = rating;
        }

        public List<Review> getReviews()
        {
            return reviews;
        }

        public void setReviews(List<Review> reviews)
        {
            this.reviews = reviews;
        }

        public String getUrl()
        {
            return url;
        }

        public void setUrl(String url)
        {
            this.url = url;
        }

        public Integer getUserRatingsTotal()
        {
            return userRatingsTotal;
        }

        @JsonProperty(value = "user_ratings_total")
        public void setUserRatingsTotal(Integer userRatingsTotal)
        {
            this.userRatingsTotal = userRatingsTotal;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Review
        {
            private String authorName;
            private String authorUrl;
            private String language;
            private String profilePhotoUrl;
            private Integer rating;
            private String relativeTime;
            private String text;
            private Long time;

            public String getAuthorName()
            {
                return authorName;
            }

            @JsonProperty(value = "author_name")
            public void setAuthorName(String authorName)
            {
                this.authorName = authorName;
            }

            public String getAuthorUrl()
            {
                return authorUrl;
            }

            @JsonProperty(value = "author_url")
            public void setAuthorUrl(String authorUrl)
            {
                this.authorUrl = authorUrl;
            }

            public String getLanguage()
            {
                return language;
            }

            public void setLanguage(String language)
            {
                this.language = language;
            }

            public String getProfilePhotoUrl()
            {
                return profilePhotoUrl;
            }

            @JsonProperty(value = "profile_photo_url")
            public void setProfilePhotoUrl(String profilePhotoUrl)
            {
                this.profilePhotoUrl = profilePhotoUrl;
            }

            public Integer getRating()
            {
                return rating;
            }

            public void setRating(Integer rating)
            {
                this.rating = rating;
            }

            public String getRelativeTime()
            {
                return relativeTime;
            }

            @JsonProperty(value = "relative_time_description")
            public void setRelativeTime(String relativeTime)
            {
                this.relativeTime = relativeTime;
            }

            public String getText()
            {
                return text;
            }

            public void setText(String text)
            {
                this.text = text;
            }

            public Long getTime()
            {
                return time;
            }

            public void setTime(Long time)
            {
                this.time = time;
            }
        }
    }
}
