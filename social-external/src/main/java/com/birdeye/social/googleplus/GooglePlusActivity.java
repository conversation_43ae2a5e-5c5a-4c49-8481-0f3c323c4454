package com.birdeye.social.googleplus;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class GooglePlusActivity {

    private String id;
    private GooglePlusActivityObject object;
    private ActivityActor actor;
    private Date published;
    private GooglePlusActionsCount plusoners;
    private String url;
    private String annotation;
    private String verb;
    
    public GooglePlusActivity() {
    		
    }

    public GooglePlusActivity(String id, GooglePlusActivityObject object, ActivityActor actor, Date published, GooglePlusActionsCount plusoners, String url, String annotation, String verb) {
        this.id = id;
        this.object = object;
        this.actor = actor;
        this.published = published;
        this.plusoners = plusoners;
        this.url = url;
        this.annotation = annotation;
        this.verb = verb;
    }

    public String getId() {
        return id;
    }

    public GooglePlusActivityObject getObject() {
        return object;
    }

    public ActivityActor getActor() {
        return actor;
    }

    public Date getPublished() {
        return published;
    }

    public GooglePlusActionsCount getPlusoners() {
        return plusoners;
    }

    public String getUrl() {
        return url;
    }

    public String getAnnotation() {
        return annotation;
    }

    public String getVerb() {
        return verb;
    }
}
