package com.birdeye.social.googleplus;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GooglePlusActivityObject {

    private  String content;
    private  String url;
    private  List<ActivityAttachment> attachments;
    private  GooglePlusActionsCount replies;
    private  GooglePlusActionsCount plusoners;
    private  GooglePlusActionsCount resharers;
    private  ActivityStatus statusForViewer;
    private  ActivityActor actor;

    public GooglePlusActivityObject() {
    	
    }
    public GooglePlusActivityObject(String content, String url, List<ActivityAttachment> attachments, GooglePlusActionsCount replies, GooglePlusActionsCount plusoners, GooglePlusActionsCount resharers, ActivityStatus statusForViewer, ActivityActor actor) {
        this.content = content;
        this.url = url;
        this.attachments = attachments;
        this.replies = replies;
        this.plusoners = plusoners;
        this.resharers = resharers;
        this.statusForViewer = statusForViewer;
        this.actor = actor;
    }

    public String getContent() {
        return content;
    }

    public String getUrl() {
        return url;
    }

    public List<ActivityAttachment> getAttachments() {
        return attachments;
    }

    public GooglePlusActionsCount getReplies() {
        return replies;
    }

    public GooglePlusActionsCount getPlusoners() {
        return plusoners;
    }

    public GooglePlusActionsCount getResharers() {
        return resharers;
    }

    public ActivityStatus getStatusForViewer() {
        return statusForViewer;
    }

    public ActivityActor getActor() {
        return actor;
    }
}
