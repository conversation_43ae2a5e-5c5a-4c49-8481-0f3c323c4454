/**
 *
 */
package com.birdeye.social.googleplus;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * <AUTHOR>
 *
 * 22 Mar 2018
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
public class GooglePlusImageResponse implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 2754165229203692107L;

	private String				url;

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}
	
}
