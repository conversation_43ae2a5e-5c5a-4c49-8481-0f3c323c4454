package com.birdeye.social.googleplus;

/**
 *
 * <AUTHOR>
 */
public class ActivityActor {

    private  String id;
    private  String displayName;
    private  String url;
	private String tagline;
    private  ActivityMedia image;
    private Integer circledByCount;

    public ActivityActor() {
    	
    }
    
    public ActivityActor(String id, String displayName, String url, ActivityMedia image) {
        this.id = id;
        this.displayName = displayName;
        this.url = url;
        this.image = image;
    }

    public ActivityActor(String id, String displayName, String url, ActivityMedia image, Integer circledByCount) {
		super();
		this.id = id;
		this.displayName = displayName;
		this.url = url;
		this.image = image;
		this.circledByCount = circledByCount;
	}

	public String getId() {
        return id;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getUrl() {
        return url;
    }

    public ActivityMedia getImage() {
        return image;
    }

	public Integer getCircledByCount() {
		return circledByCount;
	}

	/**
	 * @return the tagline
	 */
	public String getTagline() {
		return tagline;
	}

	/**
	 * @param tagline the tagline to set
	 */
	public void setTagline(String tagline) {
		this.tagline = tagline;
	}

}
