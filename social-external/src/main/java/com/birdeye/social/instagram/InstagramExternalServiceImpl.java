/**
 *
 *
 */
package com.birdeye.social.instagram;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.facebook.FacebookApis;
import com.birdeye.social.facebook.FacebookPageAccessInfo;
import com.birdeye.social.facebook.Permission;
import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.birdeye.social.facebook.response.InstagramUserDetailsResponse;
import com.birdeye.social.facebook.response.SubscribeAppResponse;
import com.birdeye.social.model.FbPage;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.GetMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 *
 */
@Service
public class InstagramExternalServiceImpl implements InstagramExternalService {
	
	@Autowired
	@Qualifier("socialRestTemplate")
	private RestTemplate				socialRestClient;
	
	private static final Logger	logger = LoggerFactory.getLogger(InstagramExternalServiceImpl.class);
	
	public static final String	ACCESS_TOKEN = "access_token";
	
	@Override
	public InstagramUserDetailsResponse getInstagramUserDetails(String accessToken, String userId) throws IOException {
		
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		params.add(ACCESS_TOKEN, accessToken);
		Boolean parityEnabled = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getParityCheckEnabled();
		if(parityEnabled) {
			params.add("parity", "ig_user");
		}
		
		String url = StringUtils.format(FacebookApis.USER, userId);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();
		InstagramUserDetailsResponse response = null;
		try {
			logger.info("Calling API getInstagramUserDetails URL {} with parameters {} ", url, params);
			response = socialRestClient.getForObject(url, InstagramUserDetailsResponse.class);
		}  catch (HttpStatusCodeException e) {
			FacebookBaseResponse errorResponse = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookBaseResponse.class);
			logger.info("HttpStatusCodeException while calling getInstagramUserDetails for URL {} :: {}", url, errorResponse);
			throw new BirdeyeSocialException(ErrorCodes.FB_MESSENGER_USER_DETAILS, errorResponse.getError().getCode() == 190 ? String.valueOf(errorResponse.getError().getCode()) : String.valueOf(errorResponse.getError().getError_subcode()));
		} catch (RestClientException e) {
			logger.error("RestClientException while calling getInstagramUserDetails for URL {} :: {}", url, e);
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
		} catch (Exception e) {
			logger.error("Exception while calling getInstagramUserDetails for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.FB_MESSENGER_USER_DETAILS, e.getMessage());
		}
		return response;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public Map<String, Object> sendMessage(String accountId, String accessToken, InstagramMessengeSendRequest sendRequest) {
		
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		params.add(ACCESS_TOKEN, accessToken);
		
		String url = StringUtils.format(FacebookApis.SEND_MESSAGE_INSTAGRAM, accountId);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();
		ResponseEntity<Map> responseEntity = null;
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<InstagramMessengeSendRequest> requestEntity = new HttpEntity<>(sendRequest, headers);
			logger.info("Calling API sendMessage instagram id {} URL {} and request {} ", accountId, url, sendRequest);
			responseEntity = socialRestClient.exchange(url, HttpMethod.POST, requestEntity, Map.class);
			logger.info("Response recieved: {} for instagram id {} URL {} and request {}",responseEntity != null ? responseEntity.getBody() : null,
					accountId,url,sendRequest);
		}catch (HttpStatusCodeException e) {
			FacebookBaseResponse errorResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookBaseResponse.class);
			logger.error("HttpStatusCodeException while calling instagram API sendMessage for URL {} :: {}", url, errorResponse);
			if(Objects.isNull(errorResponse) || Objects.isNull(errorResponse.getError())){
				throw new BirdeyeSocialException(ErrorCodes.FB_UNABLE_TO_SEND_MESSAGE, e.getMessage());
			}
			String errorMessage;
			if(errorResponse.getError().getCode() == 190) {
				errorMessage = "errorCode: 190";
			} else if(errorResponse.getError().getCode() == 2) {
				errorMessage = "There seems to be a technical issue in processing your request. Please try again in some time.";
			} else {
				errorMessage = String.valueOf(errorResponse.getError().getError_subcode());
			}
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_UNABLE_TO_SEND_MESSAGE, errorMessage);
		} catch (RestClientException e) {
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
		} catch (Exception e) {
			throw new BirdeyeSocialException(ErrorCodes.FB_UNABLE_TO_SEND_MESSAGE, e.getMessage());
		}
		return responseEntity != null ? responseEntity.getBody() : null;
	}

	private MultiValueMap<String, String> getInstagramIdParameters(String accessToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (StringUtils.isEmpty(accessToken)) {
			return map;
		}
		map.add(ACCESS_TOKEN, accessToken);
		map.add(FIELDS, "instagram_business_account");
		return map;
	}

	private void handleError(String url, Exception e,ErrorCodes errorCodes, boolean rethrow){
		if (e instanceof HttpStatusCodeException || e instanceof RestClientException) {
			if (rethrow) {
				logger.error("Error calling facebook API {} with error {} ", url, errorCodes , e);
				throw new BirdeyeSocialException(errorCodes, errorCodes.name(), e);
			}else{
				logger.error("Error calling facebook API {} with error {} ", url, errorCodes , e);
			}
		} else {
			logger.error("Exception while calling facebook API {} ", url);
			throw new BirdeyeSocialException(errorCodes, e.getMessage());
		}
	}

	@Override
	public InstagramInfo fetchInstagramId(FacebookPageAccessInfo creds) throws BirdeyeSocialException, Exception {
		//String url = StringUtils.join(creds.getBaseUrl(), creds.getProfileId(), "?fields=instagram_business_account&access_token=", creds.getAccessToken());

		InstagramInfo response = null;
		MultiValueMap<String, String> params = getInstagramIdParameters(creds.getAccessToken());
		String url = StringUtils.format(FacebookApis.FETCH_INSTAGRAM_INFO, creds.getPageId());
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();
		logger.info("Received request for API instagram id  URL {} and parameters {}", url);
		try {
			response = socialRestClient.getForObject(new URI(url), InstagramInfo.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_GET_INSTAGRAM_DETAILS, true);
		}
		return null;
	}

	private MultiValueMap<String, String> getInstagramDetailParameters(String accessToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (StringUtils.isEmpty(accessToken)) {
			return map;
		}
		map.add(ACCESS_TOKEN, accessToken);
		map.add(FIELDS, "followers_count,name,profile_picture_url,username");
		return map;
	}

	@Override
	public InstagramProfileInfo fetchInstagramDetails(FacebookPageAccessInfo creds) {
		InstagramProfileInfo response = null;
		MultiValueMap<String, String> params = getInstagramDetailParameters(creds.getAccessToken());
		String url = StringUtils.format(FacebookApis.FETCH_INSTAGRAM_INFO, creds.getPageId());
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();
			logger.info("Received request for API instagram details  URL {} and params : {}", url,params);
		try {
			response = socialRestClient.getForObject(new URI(url), InstagramProfileInfo.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			logger.info("Exception occurred while calling insta profile api : {}",e.getLocalizedMessage());
			handleError(url, e, ErrorCodes.UNABLE_TO_GET_INSTAGRAM_DETAILS, true);
		}
		return null;
	}

	private MultiValueMap<String, String> getInstagramConversationParameters(String accessToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (StringUtils.isEmpty(accessToken)) {
			return map;
		}
		map.add(ACCESS_TOKEN, accessToken);
		map.add(PLATFORM, "instagram");
		return map;
	}

	@Override
	public InstagramConversationResponse fetchInstagramConversation(FacebookPageAccessInfo creds) {
		InstagramConversationResponse response = null;
		MultiValueMap<String, String> params = getInstagramConversationParameters(creds.getAccessToken());
		String url = StringUtils.format(FacebookApis.CONVERSATION, creds.getPageId());
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();
		logger.info("Received request for API instagram concersation data.  URL {} and parameters {}", url);
		try {
			response = socialRestClient.getForObject(new URI(url), InstagramConversationResponse.class);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.INVALID_INSTAGRAM_PAGE, true);
		}
		return response;
	}
	private String prepareRoleURL(String fbPageId, String accessToken) {
		String url = StringUtils.format(FacebookApis.FETCH_INSTAGRAM_INFO, fbPageId);
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		params.add(ACCESS_TOKEN, accessToken);
		params.add(FIELDS, "roles");
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();
		return url;
	}

	@Override
	public Permission fetchRole(String pageId, String accessToken) {
		String url = prepareRoleURL(pageId,accessToken);
		Permission responseR  =  new Permission();
		try {
			logger.info("[Instagram]: Received Request for adding role through updateRole URL : {}, access token : {}", url, accessToken);
			responseR = socialRestClient.getForObject(new URI(url), Permission.class);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_INSTAGRAM_ROLE, true);
			logger.error("Exception while adding role through updateRole  {} ", e.getMessage());
		}
		return responseR;
	}

	@Override
	public SubscribeAppResponse getPageSubscribeApps(String pageId, String accessToken) throws IOException {
		String url = new StringBuilder(StringUtils.format(FacebookApis.PAGE_SUBSCRIBED_APPS, "me")).append("?access_token=").append(accessToken).toString();
		SubscribeAppResponse response = null;
		try {
			logger.info("[Instagram]: Calling get API subscribe App URL {}", url);
			ResponseEntity<SubscribeAppResponse> responseEntity = socialRestClient.exchange(url, HttpMethod.GET, null, SubscribeAppResponse.class);
			response = responseEntity.getBody();
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.INSTAGRAM_UNABLE_TO_GET_SUBSCRIBE_APP, true);
		}
		return response;
	}

	@Override
	public FacebookBaseResponse subscribeToFbApp(String accessToken,String subscriptionFields) throws IOException {
		String url = new StringBuilder(StringUtils.format(FacebookApis.PAGE_SUBSCRIBED_APPS, "me")).append("?access_token=").append(accessToken).toString();
		ResponseEntity<FacebookBaseResponse> response = null;
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
			MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
			params.add("subscribed_fields", subscriptionFields);
			HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);

			logger.info("Calling API fbPageSubscribeApps URL {} with parameters {} ", url, params);
			response = socialRestClient.exchange(url, HttpMethod.POST, requestEntity, FacebookBaseResponse.class);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.INSTAGRAM_UNABLE_TO_GET_SUBSCRIBE_APP, true);
		}
		return response.getBody();
	}

	@Override
	public FacebookBaseResponse unSubscribeToFbApp(String accessToken) throws IOException {
		String url = new StringBuilder(StringUtils.format(FacebookApis.PAGE_SUBSCRIBED_APPS, "me")).append("?access_token=").append(accessToken).toString();
		ResponseEntity<FacebookBaseResponse> response = null;
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
			HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(headers);
			logger.info("Calling API unSubscribeToFbApp URL {}", url);
			response = socialRestClient.exchange(url, HttpMethod.DELETE, requestEntity, FacebookBaseResponse.class);
		} catch (Exception e) {
			if(e instanceof RestClientException && pageAlreadyUnsubscribed(accessToken)){
				logger.info("Page is already unsubscribed");
				return new FacebookBaseResponse();
			}
			handleError(url, e, ErrorCodes.INSTAGRAM_UNABLE_TO_GET_SUBSCRIBE_APP, true);
		}
		return response.getBody();
	}

	private boolean pageAlreadyUnsubscribed(String accessToken) {
		try {
			SubscribeAppResponse response = getPageSubscribeApps(null, accessToken);
			if(response != null && CollectionUtils.isEmpty(response.getData())){
				return true;
			}
		}catch (Exception e){
			logger.error("Error while fetching page subscription details : {}",e.getMessage());
		}
		return false;
	}

	private MultiValueMap<String, String> getPageDetailsParameters(String accessToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (StringUtils.isEmpty(accessToken)) {
			return map;
		}
		map.add(ACCESS_TOKEN, accessToken);
		map.add(FIELDS, StringUtils.commaSeparatedString("id",
				"name",
				"access_token",
				"instagram_business_account"));

		String pageLimit = CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class)
				.getSocialFbPagesLimit();

		logger.info("Fb pages limit : {}", pageLimit);
		map.add(LIMIT, pageLimit);
		return map;
	}

	@Override
	public FbPage getPageDetails(String accessToken, String userId) throws Exception {
		//String url = StringUtils.join(baseUrl, userId, "/accounts");
		String url = StringUtils.format(FacebookApis.USER_ACCOUNTS,userId);
		FbPage response = null;
		MultiValueMap<String, String> params = getPageDetailsParameters(accessToken);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();

		logger.info("Received request for API getPageDetails URL {} and parameters {}", url, params);
		try {
			response = socialRestClient.getForObject(new URI(url), FbPage.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_PAGE_DETAILS, true);
		}
		return response;
	}
	@Override
	public FbPage getPageDetailsNext(String nextUrl) throws IOException {
		FbPage page = null;
		GetMethod getMethod = new GetMethod(nextUrl);
		HttpClient httpClient = new HttpClient();
		httpClient.executeMethod(getMethod);
		if (getMethod.getStatusCode() == 200) {
			String response = getMethod.getResponseBodyAsString();
			page = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).readValue(response, FbPage.class);
		} else {
			JsonObject json = new JsonParser().parse(getMethod.getResponseBodyAsString().trim()).getAsJsonObject();
			if (json.has(ERROR)) {
				logger.info("Error occurred while retrieving facebook pages : {} and url : {}", json.getAsJsonObject(ERROR).get(MESSAGE).getAsString(), nextUrl);
				JsonObject errorObj = json.getAsJsonObject(ERROR);
				if (errorObj.has("code")) {
					int code = errorObj.get("code").getAsInt();
					if (code == 100 || code == 190) {
						throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, json.getAsJsonObject(ERROR).get(MESSAGE).getAsString());
					} else {
						throw new BirdeyeSocialException(json.getAsJsonObject(ERROR).get(MESSAGE).getAsString());
					}
				}
			}
		}
		return page;
	}

	@Override
	public List<FbPage> getPageDetailsListForInstagram(String accessToken, String userId) throws Exception {
		FbPage page = getPageDetails(accessToken, userId);
		List<FbPage> list = new ArrayList<>();
		if (page != null) {
			list.add(page);
		}
		while (page != null && page.getPaging() != null && page.getPaging().getNext() != null) {
			page = getPageDetailsNext(page.getPaging().getNext());
			if (page != null) {
				list.add(page);
			}
		}
		return list;
	}
	
	/*
	 * (non-Javadoc)
	 * @see com.birdeye.social.external.service.FbMessengerExternalService#fbPageSubscribeApps(java.lang.String)
	 */
//	@Override
//	public FacebookBaseResponse fbPageSubscribeApps(String pageId, String accessToken,String subscriptionFields) throws IOException {
//		String url = new StringBuilder(StringUtils.format(FacebookApis.PAGE_SUBSCRIBED_APPS, pageId)).append("?access_token=").append(accessToken).toString();
//		ResponseEntity<FacebookBaseResponse> response = null;
//		try {
//			HttpHeaders headers = new HttpHeaders();
//			headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
//			MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
//			params.add("subscribed_fields", subscriptionFields);
//			HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);
//			
//			logger.info("Calling API fbPageSubscribeApps URL {} with parameters {} ", url, params);
//			response = restClient.exchange(url, HttpMethod.POST, requestEntity, FacebookBaseResponse.class);
//		} catch (HttpStatusCodeException e) {
//			FacebookBaseResponse errorResponse = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookBaseResponse.class);
//			logger.error("HttpStatusCodeException while calling facebook API fbPageSubscribeApps for URL {} :: {}", url, errorResponse);
//			throw new BirdeyeSocialException(ErrorCodes.PAGE_UNABLE_TO_SUBSCRIBE_FB_APP, errorResponse.getError().getCode() == 190 ? String.valueOf(errorResponse.getError().getCode()) : String.valueOf(errorResponse.getError().getError_subcode()));
//		} catch (RestClientException e) {
//			logger.error("RestClientException while calling facebook API fbPageSubscribeApps for URL {} :: {}", url, e);
//			throw new ExternalAPIException(ExternAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
//		} catch (Exception e) {
//			logger.error("Exception while calling facebook API fbPageSubscribeApps for URL {} :: {}", url, e);
//			throw new BirdeyeSocialException(ErrorCodes.PAGE_UNABLE_TO_SUBSCRIBE_FB_APP, e.getMessage());
//		}
//		return response.getBody();
//	}
//	
//	/*
//	 * (non-Javadoc)
//	 * @see com.birdeye.social.external.service.FbMessengerExternalService#fbPageUnsubscribeApps(java.lang.String)
//	 */
//	@Override
//	public FacebookBaseResponse fbPageUnsubscribeApps(String pageId, String accessToken) {
//		String url = new StringBuilder(StringUtils.format(FacebookApis.PAGE_SUBSCRIBED_APPS, pageId)).append("?access_token=").append(accessToken).toString();
//		FacebookBaseResponse response = null;
//		try {
//			logger.info("Calling API fbPageUnsubscribeApps URL {}", url);
//			ResponseEntity<FacebookBaseResponse> responseEntity = restClient.exchange(url, HttpMethod.DELETE, null, FacebookBaseResponse.class);
//			response = responseEntity.getBody();
//		} catch (Exception e) {
//			handleError(url, e, ErrorCodes.PAGE_UNABLE_TO_DELETE_SUBSCRIBE_FB_APP, true);
//		}
//		return response;
//	}



//	
//	
//	void handleError(String url, Exception e, ErrorCodes errorCodes, boolean rethrow) {
//		// Spring REST exceptions.
//		if (e instanceof HttpStatusCodeException || e instanceof RestClientException) {
//			if (rethrow) {
//				throw new BirdeyeSocialException(errorCodes.name(), e);
//			} else {
//				// Logging the exception and not throwing.
//				logger.error("Error calling facebook API {} with error {} ", url, errorCodes, e);
//			}
//		} else {
//			logger.error("Exception while calling facebook API {} ", url);
//			throw new BirdeyeSocialException(errorCodes, e.getMessage());
//		}
//	}
}
