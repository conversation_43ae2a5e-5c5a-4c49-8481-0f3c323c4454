package com.birdeye.social.utils;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.dto.SamayScheduleEventRequest;
import org.apache.commons.lang3.StringUtils;

public class SamayUtils {
    private SamayUtils() {

    }

    public static SamayScheduleEventRequest getSamayScheduleEventRequest(String samayPayload, Long externalUid, Long businessId, Integer retryLimit, Integer retryInterval, String retryType,
                                                                         long scheduleTimeEpoch, String eventType, String topicName,boolean retry) {
        SamayScheduleEventRequest samayRequest = new SamayScheduleEventRequest();
        samayRequest.setApplication(Constants.SOCIAL_APPLICATION);

        if (retryLimit != null) {
            samayRequest.setRetryLimit(retryLimit);
        }
        if (retryInterval != null) {
            samayRequest.setRetryInterval(retryInterval);
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(retryType)) {
            samayRequest.setRetryType(retryType);
        }
        if (StringUtils.isNotEmpty(eventType)) {
            samayRequest.setEventType(eventType);
        }
        samayRequest.setExpiry(scheduleTimeEpoch);
        samayRequest.setTopic(topicName);
        samayRequest.setExternalUid(externalUid);
        samayRequest.setAccountId(businessId);
        samayRequest.setPayload(samayPayload);
        return samayRequest;
    }

    public static SamayScheduleEventRequest getDefaultSamayRequest(String samayPayload, Long externalUid, Long businessId, long scheduleTimeEpoch, String topicName) {
        SamayScheduleEventRequest samayRequest = new SamayScheduleEventRequest();
        samayRequest.setAccountId(businessId);
        samayRequest.setExpiry(scheduleTimeEpoch);
        samayRequest.setExternalUid(externalUid);
        samayRequest.setPayload(samayPayload);
        samayRequest.setTopic(topicName);
        return samayRequest;
    }
}
