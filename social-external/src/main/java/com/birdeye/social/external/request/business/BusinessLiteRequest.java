package com.birdeye.social.external.request.business;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BusinessLiteRequest {

    private String serviceName = "Social";
    private String key;
    private Object value;
    private boolean locationRequired = false;

    public BusinessLiteRequest(String key, Object value, boolean locationRequired) {
        this.key = key;
        this.value = value;
        this.locationRequired = locationRequired;
    }

    public BusinessLiteRequest() {
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public boolean isLocationRequired() {
        return locationRequired;
    }

    public void setLocationRequired(boolean locationRequired) {
        this.locationRequired = locationRequired;
    }
}
