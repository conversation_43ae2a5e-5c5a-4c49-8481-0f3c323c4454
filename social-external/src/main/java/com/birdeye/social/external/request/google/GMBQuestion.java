package com.birdeye.social.external.request.google;

import java.io.Serializable;
import java.util.List;

import com.birdeye.social.external.request.google.GMBQuestionDTO.Author;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * author <PERSON><PERSON>han<PERSON>
 */

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
@NoArgsConstructor
@Data
public class GMBQuestion implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private String name;
	private String upvoteCount;
	private String text;
	private String createTime;
	private String updateTime;
	private Integer totalAnswerCount;
	private Author author;
	private List<GMBAnswerDTO> topAnswers;
}
