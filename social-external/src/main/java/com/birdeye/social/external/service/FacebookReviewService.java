/**
 * 
 */
package com.birdeye.social.external.service;

import java.io.IOException;
import java.net.URI;
import java.text.Normalizer;
import java.util.*;

import com.birdeye.social.dto.FBReviewComment;
import com.birdeye.social.facebook.response.*;
import com.fasterxml.jackson.core.JsonParseException;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.methods.GetMethod;
import org.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.external.request.facebook.AddFBReviewCommentRequest;
import com.birdeye.social.external.request.facebook.FacebookOverallRatingRequest;
import com.birdeye.social.external.request.facebook.FacebookReviewRequest;
import com.birdeye.social.external.request.facebook.GetFBReviewCommentRequest;
import com.birdeye.social.external.request.facebook.UpdateFBReviewCommentRequest;
import com.birdeye.social.external.response.facebook.FacebookReviewResponse;
import com.birdeye.social.facebook.FacebookApis;
import com.birdeye.social.utils.FacebookUtils;
import com.birdeye.social.utils.StringUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR> on Apr 11, 2018
 *
 */
@Service
public class FacebookReviewService implements IFacebookReviewService {
	
	/**
	 * 
	 */
	private static final String		FB_REVIEW_EXCEPTION_OCCURRED_WHILE_CALLING_FACEBOOK_WITH_URL	= "[FB Review] Exception occurred while calling Facebook with URL :";
	private static final String		FB_REVIEW_RESPONSE_FROM_FACEBOOK_WITH_URL_IS					= "[FB Review] Response status from Facebook with URL:{} is:{}";
	private static final String		FB_REVIEW_CALLING_FACEBOOK_WITH_URL								= "[FB Review] Calling Facebook with URL:{}";
	
	private static final Logger		logger															= LoggerFactory.getLogger(FacebookReviewService.class);
	
	@Autowired
	@Qualifier("socialRestTemplate")
	private RestTemplate			restClient;
	
	private String getFBUrl() {
		return  FacebookApis.GRAPH_API_BASE_WITH_VERSION;
	}
	
	private String getFBUrlV5() {
		return  FacebookApis.GRAPH_API_BASE_VERSION_DEFAULT_V21;
	}
	
	private static final String MARK_PAGE_INACTIVE = "mark_page_inactive";
	
	/*
	 * (non-Javadoc)
	 * @see com.birdeye.social.external.service.IFacebookReviewService#getReviews(com.birdeye.social.external.request.facebook.FacebookReviewRequest)
	 */
	@Override
	public FacebookReviewResponse getReviews(FacebookReviewRequest request) {
		String url = new StringBuilder(getFBUrlV5()).append(request.getUri()).toString();
		ResponseEntity<FacebookReviewResponse> responseEntity = null;
		try {
			logger.info(FB_REVIEW_CALLING_FACEBOOK_WITH_URL, url);
			responseEntity = restClient.getForEntity(url, FacebookReviewResponse.class);
			logger.info(FB_REVIEW_RESPONSE_FROM_FACEBOOK_WITH_URL_IS, url, responseEntity.getStatusCode());
		} catch (Exception e) {
			logger.error(FB_REVIEW_EXCEPTION_OCCURRED_WHILE_CALLING_FACEBOOK_WITH_URL + url, e);
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
		if (responseEntity.getStatusCode().is5xxServerError()) {
			throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, responseEntity.getStatusCode().getReasonPhrase());
		}
		if (!responseEntity.getStatusCode().is2xxSuccessful()) {
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, responseEntity.getStatusCode().getReasonPhrase());
		}
		return responseEntity.getBody();
	}
	
	/*
	 * (non-Javadoc)
	 * @see com.birdeye.social.external.service.IFacebookReviewService#getRawReviews(com.birdeye.social.external.request.facebook.FacebookReviewRequest)
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public Map<String, Object> getRawReviews(FacebookReviewRequest request) throws IOException {
		if(StringUtils.isEmpty(request.getAccessToken())) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REVIEWS, "Null access token for FB page");
		}
		String url = new StringBuilder(getFBUrlV5()).append(request.getUri()).toString();
		URI uri = UriComponentsBuilder.fromHttpUrl(url).build().encode().toUri();
		ResponseEntity<Map> responseEntity = null;
		try {
			logger.info(FB_REVIEW_CALLING_FACEBOOK_WITH_URL, url);
			responseEntity = restClient.exchange(uri, HttpMethod.GET, null, Map.class);
		} catch (HttpStatusCodeException e) {
			Map<String, Object> exceptionDataMap = new HashMap<>();
			try {
				FacebookBaseResponse errorResponse = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookBaseResponse.class);
				logger.error("HttpStatusCodeException while calling facebook API getRawReviews for URL {} :: {}", url, errorResponse);
				exceptionDataMap = getExceptionDataMap(errorResponse);
				boolean ackException = checkIfAcknowledgeException(errorResponse);
				if(ackException) {
					throw new BirdeyeSocialException(ErrorCodes.FB_ACKNOWLEDGE_ERROR, errorResponse.getError().getMessage(), exceptionDataMap);
				}
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REVIEWS, errorResponse.getError().getMessage(), exceptionDataMap);
			} catch (BirdeyeSocialException ex) {
				if(ex.getCode() == ErrorCodes.FB_ACKNOWLEDGE_ERROR.value()) {
					throw new BirdeyeSocialException(ErrorCodes.FB_ACKNOWLEDGE_ERROR, ex.getMessage(), exceptionDataMap);
				}
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REVIEWS, ex.getMessage(), exceptionDataMap);
			} catch (Exception ex) {
				if(ex instanceof JsonParseException) {
					exceptionDataMap = getExceptionDataMapFromString(e.getResponseBodyAsString());
					throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, e.getMessage(), exceptionDataMap);
				}
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REVIEWS, ex.getMessage(),exceptionDataMap);
			}
		} catch (RestClientException e) {
			logger.error("RestClientException while calling facebook API getRawReviews for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REVIEWS, e.getMessage());
		} catch (Exception e) {
			logger.error("Exception while calling facebook API getRawReviews for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REVIEWS, e.getMessage());
		}
		return responseEntity.getBody();
	}

	private boolean checkIfAcknowledgeException(FacebookBaseResponse errorResponse) {
		if(Objects.isNull(errorResponse) || Objects.isNull(errorResponse.getError()))
			return false;

		FacebookErrorResponse error = errorResponse.getError();

		if(Objects.nonNull(error.getCode()) && error.getCode().equals(190)
				&& Objects.nonNull(error.getError_subcode()) && error.getError_subcode().equals(459)) {
			return true;
		}

		return false;
	}


	/*
	 * (non-Javadoc)
	 * @see com.birdeye.social.external.service.IFacebookReviewService#getOverallRatingAndReviewCount(com.birdeye.social.external.request.facebook.
	 * FacebookOverallRatingRequest)
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public Map<String, Object> getOverallRatingAndReviewCount(FacebookOverallRatingRequest request) throws IOException {
		String url = new StringBuilder(getFBUrl()).append(request.getUri()).toString();
		ResponseEntity<Map> responseEntity = null;
		try {
			logger.info(FB_REVIEW_CALLING_FACEBOOK_WITH_URL, url);
			responseEntity = restClient.exchange(url, HttpMethod.GET, null, Map.class);
		} catch (HttpStatusCodeException e) {
			FacebookBaseResponse errorResponse = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookBaseResponse.class);
			logger.error("HttpStatusCodeException while calling facebook API getOverallRatingAndReviewCount for URL {} :: {}", url, errorResponse);
			Map<String, Object> exceptionDataMap = getExceptionDataMap(errorResponse);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GET_FACEBOOK_OVERALL_RATINGS, errorResponse.getError().getMessage(), exceptionDataMap);
		} catch (RestClientException e) {
			logger.error("RestClientException while calling facebook API getOverallRatingAndReviewCount for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GET_FACEBOOK_OVERALL_RATINGS, e.getMessage());
		} catch (Exception e) {
			logger.error("Exception while calling facebook API getOverallRatingAndReviewCount for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GET_FACEBOOK_OVERALL_RATINGS, e.getMessage());
		}
		return responseEntity.getBody();
	}
	
	/*
	 * (non-Javadoc)
	 * @see com.birdeye.social.external.service.IFacebookReviewService#getComments(java.lang.String, java.lang.String)
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public Map<String, Object> getComments(String reviewId, String accessToken) {
		// TODO add all URLs to some constant
		String url = new StringBuilder(getFBUrl()).append(reviewId).append("/comments?order=reverse_chronological&access_token=").append(accessToken).toString();
		ResponseEntity<Map> responseEntity = null;
		try {
			logger.info(FB_REVIEW_CALLING_FACEBOOK_WITH_URL, url);
			responseEntity = restClient.exchange(url, HttpMethod.GET, null, Map.class);
			logger.info(FB_REVIEW_RESPONSE_FROM_FACEBOOK_WITH_URL_IS, url, responseEntity);
		} catch (Exception e) {
			logger.error(FB_REVIEW_EXCEPTION_OCCURRED_WHILE_CALLING_FACEBOOK_WITH_URL + url, e);
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
		if (responseEntity.getStatusCode().is5xxServerError()) {
			throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, responseEntity.getStatusCode().getReasonPhrase());
		}
		if (!responseEntity.getStatusCode().is2xxSuccessful()) {
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, responseEntity.getStatusCode().getReasonPhrase());
		}
		return responseEntity.getBody();
	}
	
	/*
	 * (non-Javadoc)
	 * @see com.birdeye.social.external.service.IFacebookReviewService#addComment(com.birdeye.social.external.request.facebook.AddFBReviewCommentRequest)
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public Map<String, Object> addComment(AddFBReviewCommentRequest request, String pageId) throws IOException {
		String url = new StringBuilder(getFBUrl()).append(request.getUri()).toString();
		ResponseEntity<Map> responseEntity = null;
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
			MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
			map.add("message", request.getComment());
			HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(map, headers);
			logger.info("[FB Review] Calling Facebook with URL:{} and Input :{}", url, map);
			responseEntity = restClient.exchange(url, HttpMethod.POST, requestEntity, Map.class);
		} catch (HttpStatusCodeException e) {
			Map<String, Object> exceptionDataMap = new HashMap<>();
			try {
				FacebookBaseResponse errorResponse = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookBaseResponse.class);
				logger.error("HttpStatusCodeException while calling facebook API addComment for URL {} :: {}", url, errorResponse);
				exceptionDataMap = getExceptionDataMap(errorResponse);
				if (MapUtils.isNotEmpty(exceptionDataMap) && exceptionDataMap.containsKey(MARK_PAGE_INACTIVE) && MapUtils.getBooleanValue(exceptionDataMap, MARK_PAGE_INACTIVE)) {
					logger.info("[FB Review Reply] oAuthException encountered. Page will be marked as invalid");
				}
				throw new BirdeyeSocialException(ErrorCodes.ERROR_WHILE_POSTING_FB_COMMENT, errorResponse.getError().getCode(), errorResponse.getError().getMessage(),exceptionDataMap);

			} catch (Exception ex) {
				if(ex instanceof JsonParseException) {
					exceptionDataMap = getExceptionDataMapFromString(e.getResponseBodyAsString());
					throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_UPDATE_FACEBOOK_PAGE_INFO, e.getMessage(), exceptionDataMap);
				}
				throw new BirdeyeSocialException(ErrorCodes.ERROR_WHILE_POSTING_FB_COMMENT, e.getMessage());

			}
		} catch (RestClientException e) {
			logger.error("RestClientException while calling facebook API addComment for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_ADD_FACEBOOK_COMMENT, e.getMessage());
		} catch (Exception e) {
			logger.error("Exception while calling facebook API addComment for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_ADD_FACEBOOK_COMMENT, e.getMessage());
		}
		return responseEntity.getBody();
	}

	/*
	 * (non-Javadoc)
	 * @see
	 * com.birdeye.social.external.service.IFacebookReviewService#updateComment(com.birdeye.social.external.request.facebook.UpdateFBReviewCommentRequest)
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public Map<String, Object> updateComment(UpdateFBReviewCommentRequest request) throws IOException {
		String url = new StringBuilder(getFBUrl()).append(request.getUri()).toString();
		ResponseEntity<Map> responseEntity = null;
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
			MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
			map.add("message", request.getComment());
			HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(map, headers);
			logger.info("[FB Review] Calling Facebook with URL:{} and input:{}", url, map);
			responseEntity = restClient.exchange(url, HttpMethod.POST, requestEntity, Map.class);
		} catch (HttpStatusCodeException e) {
			FacebookBaseResponse errorResponse = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookBaseResponse.class);
			logger.error("HttpStatusCodeException while calling facebook API updateComment for URL {} :: {}", url, errorResponse);
			Map<String, Object> exceptionDataMap = getExceptionDataMap(errorResponse);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_ADD_FACEBOOK_COMMENT, errorResponse.getError().getMessage(), exceptionDataMap);
		} catch (RestClientException e) {
			logger.error("RestClientException while calling facebook API updateComment for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_ADD_FACEBOOK_COMMENT, e.getMessage());
		} catch (Exception e) {
			logger.error("Exception while calling facebook API updateComment for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_ADD_FACEBOOK_COMMENT, e.getMessage());
		}
		return responseEntity.getBody();
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public Map<String, Object> getReviewById(GetFBReviewCommentRequest request) throws IOException {
		String url = new StringBuilder(getFBUrl()).append(request.getUri()).toString();
		ResponseEntity<Map> responseEntity = null;
		try {
			logger.info(FB_REVIEW_CALLING_FACEBOOK_WITH_URL, url);
			responseEntity = restClient.exchange(url, HttpMethod.GET, null, Map.class);
		} catch (HttpStatusCodeException e) {
			FacebookBaseResponse errorResponse = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookBaseResponse.class);
			logger.error("HttpStatusCodeException while calling facebook API getReviewById for URL {} :: {}", url, errorResponse);
			Map<String, Object> exceptionDataMap = getExceptionDataMap(errorResponse);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REVIEWS, errorResponse.getError().getMessage(), exceptionDataMap);
		} catch (RestClientException e) {
			logger.error("RestClientException while calling facebook API getReviewById for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REVIEWS, e.getMessage());
		} catch (Exception e) {
			logger.error("Exception while calling facebook API getReviewById for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REVIEWS, e.getMessage());
		}
		return responseEntity.getBody();
	}
	
	private Map<String, Object> getExceptionDataMap(FacebookBaseResponse errorResponse) {
		Map<String, Object> map = new HashMap<>();
		Boolean markPageInactive = FacebookUtils.markPageInactive(errorResponse);
		map.put("mark_page_inactive", markPageInactive);
		return map;
	}

	private Map<String, Object> getExceptionDataMapFromString(String errorString) {
		Map<String, Object> map = new HashMap<>();
		Boolean markPageInactive = errorString.contains("this content isn't available right now");
		map.put("mark_page_inactive", markPageInactive);
		return map;
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.external.service.IFacebookReviewService#aggregateFBReviewComments(java.lang.String, java.util.List)
	 */
	@Override
	public List<FacebookBatchResponse> aggregateFBReviewComments(String accessToken, JSONArray requestArray) throws IOException {
		String url = getFBUrl();
		MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
		params.add("access_token", accessToken);
		params.add("include_headers", "false");
		params.add("batch", requestArray.toString());
		
		ResponseEntity<List<FacebookBatchResponse>> responseEntity = null;
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.MULTIPART_FORM_DATA);
			
			HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(params, headers);
			
			logger.info(FB_REVIEW_CALLING_FACEBOOK_WITH_URL, url);
			responseEntity = restClient.exchange(url, HttpMethod.POST, requestEntity, new ParameterizedTypeReference<List<FacebookBatchResponse>>(){});
			
		} catch (HttpStatusCodeException e) {
			FacebookBaseResponse errorResponse = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookBaseResponse.class);
			logger.error("HttpStatusCodeException while calling facebook API aggregateFBReviewComments for URL {} :: {}", url, errorResponse);
			Map<String, Object> exceptionDataMap = getExceptionDataMap(errorResponse);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REPLIES, errorResponse.getError().getMessage(), exceptionDataMap);
		} catch (RestClientException e) {
			logger.error("RestClientException while calling facebook API aggregateFBReviewComments for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REPLIES, e.getMessage());
		} catch (Exception e) {
			logger.error("Exception while calling facebook API aggregateFBReviewComments for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REPLIES, e.getMessage());
		}
		return responseEntity.getBody();
	}

	@Override
	public String fetchProfileId(String accessToken, String url) throws HttpException, IOException {
		String facebookPageId = null;
		String normalizedFacebookPageUrl = normalizeAndRemoveSpecialCharacter(url);
		HttpClient httpClient = new HttpClient();
		GetMethod getRequest = new GetMethod(FacebookApis.GRAPH_API_BASE_VERSION_DEFAULT_V21 + normalizedFacebookPageUrl);
		List<org.apache.commons.httpclient.NameValuePair> queryParams = new ArrayList<>();
		if(StringUtils.isNotBlank(accessToken)){
			queryParams.add(new org.apache.commons.httpclient.NameValuePair("access_token", accessToken));
			getRequest.setQueryString(queryParams.toArray(new org.apache.commons.httpclient.NameValuePair[1]));
			httpClient.executeMethod(getRequest);
			if(HttpStatus.SC_OK == getRequest.getStatusCode()){
				String response = getRequest.getResponseBodyAsString();
				ObjectMapper mapper = new ObjectMapper();
				FacebookResponseObject facebookObj = mapper.readValue(response, FacebookResponseObject.class);
				if(facebookObj.getId()!= null && facebookObj.getId().matches("\\d+")){
					facebookPageId = facebookObj.getId();
				} else if(facebookObj.getOg_object()!=null && facebookObj.getOg_object().getId()!= null
						&& facebookObj.getOg_object().getId().matches("\\d+")){
					facebookPageId = facebookObj.getOg_object().getId();
				}

			}
		}

		return facebookPageId;
	}
	
	public static String normalizeAndRemoveSpecialCharacter(String businessName) {
        businessName = Normalizer.normalize(businessName, Normalizer.Form.NFD);
        return businessName.replaceAll("[^\\p{ASCII}]", "");
    }

	@Override
	public FBReviewComment.Data getFbReviewer(String facebookReviewId, String accessToken) throws IOException {
		ResponseEntity<FBReviewComment.Data> responseEntity = null;
		String url = null;
		try {
			url = new StringBuilder(getFBUrl())
					.append(facebookReviewId)
					.append("?access_token=")
					.append(accessToken)
					.append("&fields=from").toString();

			logger.info("getFbReviewer: url {}", url);
			responseEntity = restClient.exchange(url, HttpMethod.GET, null, FBReviewComment.Data.class);
			logger.info("getFbReviewer: url {} responseEntity {}", url, responseEntity);
		} catch (HttpStatusCodeException e) {
			FacebookBaseResponse errorResponse = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookBaseResponse.class);
			if(errorResponse.getError().getMessage().contains("(#12) singular statuses API is deprecated for versions v2.4 and higher")){
				logger.info("HttpStatusCodeException while calling facebook API getFbReviewer for URL {} :: {}", url, errorResponse);
			}else {
				logger.error("HttpStatusCodeException while calling facebook API getFbReviewer for URL {} :: {}", url, errorResponse);
			}
			Map<String, Object> exceptionDataMap = getExceptionDataMap(errorResponse);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REVIEWS, errorResponse.getError().getMessage(), exceptionDataMap);
		} catch (RestClientException e) {
			logger.error("RestClientException while calling facebook API getFbReviewer for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REVIEWS, e.getMessage());
		} catch (Exception e) {
			logger.error("Exception while calling facebook API getFbReviewer for URL {} :: {}", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_REVIEWS, e.getMessage());
		}
		return responseEntity.getBody();
	}
}