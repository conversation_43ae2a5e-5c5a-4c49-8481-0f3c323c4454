package com.birdeye.social.external.request.linkedin;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SpecificContent {

    @JsonProperty(value = "com.linkedin.ugc.ShareContent")
    private ShareContent shareContent;

    public ShareContent getShareContent() {
        return shareContent;
    }

    public void setShareContent(ShareContent shareContent) {
        this.shareContent = shareContent;
    }
}
