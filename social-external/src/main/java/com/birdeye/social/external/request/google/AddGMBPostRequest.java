package com.birdeye.social.external.request.google;

public class AddGMBPostRequest {
    private String accessToken;
    private String locationUrl;
    private String postId;
    private String paginationToken;

    public AddGMBPostRequest(String accessToken, String locationUrl, String paginationToken) {
        this.locationUrl = locationUrl;
        this.accessToken = accessToken;
        this.paginationToken = paginationToken;
    }

    public AddGMBPostRequest(String locationUrl, String postId) {
        this.locationUrl = locationUrl;
        this.postId = postId;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getLocationUrl() {
        return locationUrl;
    }

    public void setLocationUrl(String locationUrl) {
        this.locationUrl = locationUrl;
    }

    public String getPostId() {
        return postId;
    }

    public void setPostId(String postId) {
        this.postId = postId;
    }

    public String getPaginationToken() {
        return paginationToken;
    }

    public void setPaginationToken(String paginationToken) {
        this.paginationToken = paginationToken;
    }

    @Override
    public String toString() {
        return "AddGMBPostRequest{" +
                "accessToken='" + accessToken + '\'' +
                ", locationUrl='" + locationUrl + '\'' +
                ", postId='" + postId + '\'' +
                ", paginationToken='" + paginationToken + '\'' +
                '}';
    }
}
