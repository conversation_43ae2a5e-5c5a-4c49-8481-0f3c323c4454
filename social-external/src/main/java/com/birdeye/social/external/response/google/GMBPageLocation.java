package com.birdeye.social.external.response.google;

import com.birdeye.social.external.request.google.*;
import com.birdeye.social.model.GMBAttribute;
import com.birdeye.social.model.gmb.GMBServiceArea;
import com.birdeye.social.model.gmb.LatLng;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GMBPageLocation implements Serializable {

	private static final long serialVersionUID = 7284718338880521495L;
	private String name;
	private String languageCode;
	private String storeCode;
	private String title;
	private GMBLocationRequest.PhoneNumbers phoneNumbers;
	private GMBLocationRequest.Categories categories;
	private GMBPageAddress storefrontAddress;
	private String websiteUri;
	private GMBServiceArea serviceArea;
	private LatLng latlng;
	private GMBLocationPageUrl metadata;
	private OpenInfo openInfo;
	private GMBLocationRequest.BusinessHour regularHours;
	private GMBLocationRequest.SpecialHours specialHours;
	private GMBLocationRequest.Profile profile;
	private List<ServiceItem> serviceItems;
	private GMBAttribute attributeResponse;
	private List<GMBMoreHours> moreHours;

	private GMBLocationState locationState;
	private List<AttributeMetadata> supportedAttributes;
	private String regionCode;

	public List<GMBMoreHours> getMoreHours() {
		return moreHours;
	}
	
	public void setMoreHours(List<GMBMoreHours> moreHours) {
		this.moreHours = moreHours;
	}
	
	public GMBAttribute getAttributeResponse() {
		return attributeResponse;
	}

	public void setAttributeResponse(GMBAttribute attributeResponse) {
		this.attributeResponse = attributeResponse;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getStoreCode() {
		return storeCode;
	}

	public void setStoreCode(String storeCode) {
		this.storeCode = storeCode;
	}

	public GMBLocationRequest.BusinessHour getRegularHours() {
		return regularHours;
	}

	public void setRegularHours(GMBLocationRequest.BusinessHour regularHours) {
		this.regularHours = regularHours;
	}

	public GMBLocationRequest.SpecialHours getSpecialHours() {
		return specialHours;
	}

	public void setSpecialHours(GMBLocationRequest.SpecialHours specialHours) {
		this.specialHours = specialHours;
	}

	public List<AttributeMetadata> getSupportedAttributes() {
		return supportedAttributes;
	}

	public void setSupportedAttributes(List<AttributeMetadata> supportedAttributes) {
		this.supportedAttributes = supportedAttributes;
	}

	public GMBLocationRequest.PhoneNumbers getPhoneNumbers() {
		return phoneNumbers;
	}

	public void setPhoneNumbers(GMBLocationRequest.PhoneNumbers phoneNumbers) {
		this.phoneNumbers = phoneNumbers;
	}

	public GMBLocationRequest.Categories getCategories() {
		return categories;
	}

	public void setCategories(GMBLocationRequest.Categories categories) {
		this.categories = categories;
	}

	public LatLng getLatlng() {
		return latlng;
	}

	public void setLatlng(LatLng latlng) {
		this.latlng = latlng;
	}

	public GMBLocationRequest.Profile getProfile() {
		return profile;
	}

	public void setProfile(GMBLocationRequest.Profile profile) {
		this.profile = profile;
	}

	/**
	 * @return the metadata
	 */
	public GMBLocationPageUrl getMetadata() {
		return metadata;
	}

	/**
	 * @param metadata the metadata to set
	 */
	public void setMetadata(GMBLocationPageUrl metadata) {
		this.metadata = metadata;
	}

	/**
	 * @return the websiteUrl
	 */
	public String getWebsiteUri() {
		return websiteUri;
	}

	/**
	 * @param websiteUri the websiteUrl to set
	 */
	public void setWebsiteUri(String websiteUri) {
		this.websiteUri = websiteUri;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public GMBLocationState getLocationState() {
		return locationState;
	}

	public void setLocationState(GMBLocationState locationState) {
		this.locationState = locationState;
	}

	public GMBServiceArea getServiceArea() {
		return serviceArea;
	}

	public void setServiceArea(GMBServiceArea serviceArea) {
		this.serviceArea = serviceArea;
	}
	public GMBPageAddress getStorefrontAddress() {
		return storefrontAddress;
	}

	public void setStorefrontAddress(GMBPageAddress storefrontAddress) {
		this.storefrontAddress = storefrontAddress;
	}

	public List<ServiceItem> getServiceItems() {
		return serviceItems;
	}

	public void setServiceItems(List<ServiceItem> serviceItems) {
		this.serviceItems = serviceItems;
	}

	public OpenInfo getOpenInfo() {
		return openInfo;
	}

	public void setOpenInfo(OpenInfo openInfo) {
		this.openInfo = openInfo;
	}


	public String getLanguageCode() {
		return languageCode;
	}

	public void setLanguageCode(String languageCode) {
		this.languageCode = languageCode;
	}

	@Override
	public String toString() {
		return "GMBPageLocation{" +
				"name='" + name + '\'' +
				", languageCode='" + languageCode + '\'' +
				", storeCode='" + storeCode + '\'' +
				", title='" + title + '\'' +
				", phoneNumbers=" + phoneNumbers +
				", categories=" + categories +
				", storefrontAddress=" + storefrontAddress +
				", websiteUri='" + websiteUri + '\'' +
				", serviceArea=" + serviceArea +
				", latlng=" + latlng +
				", metadata=" + metadata +
				", openInfo=" + openInfo +
				", profile=" + profile +
				", serviceItems=" + serviceItems +
				", locationState=" + locationState +
				", supportedAttributes=" + supportedAttributes +
				", regionCode='" + regionCode + '\'' +
				'}';
	}
}
