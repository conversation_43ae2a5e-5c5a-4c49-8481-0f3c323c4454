package com.birdeye.social.external.request.google;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RepeatedEnumAttributeValue {

    // Enum values that are set
    private List<String> setValues;

    // Enum values that are unset
    private List<String> unsetValues;

    public List<String> getSetValues() {
        return setValues;
    }

    public void setSetValues(List<String> setValues) {
        this.setValues = setValues;
    }

    public List<String> getUnsetValues() {
        return unsetValues;
    }

    public void setUnsetValues(List<String> unsetValues) {
        this.unsetValues = unsetValues;
    }

    @Override
    public String toString() {
        return "RepeatedEnumAttributeValue{" +
                "setValues=" + setValues +
                ", unsetValues=" + unsetValues +
                '}';
    }
}
