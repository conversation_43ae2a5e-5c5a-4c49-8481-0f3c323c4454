package com.birdeye.social.external.btp;

import com.birdeye.social.entities.btp.BTPDayWiseData;
import com.birdeye.social.entities.btp.BTPPriorityLookup;

import java.util.Date;
import java.util.List;

public interface BTPDayWiseService {
    void save(BTPDayWiseData btpDayWiseData);

    void saveList(List<BTPDayWiseData> btpDayWiseDataList);

    List<BTPDayWiseData> findByCountryAndChannelAndCategoryAndGreaterThanFromTimeAndReportType(
            BTPPriorityLookup btpPriorityLookupForEngagement, Date fromTime,Date toTime, String increaseEngagement,Integer limit);
    List<BTPDayWiseData> findByCountryAndChannelAndCategoryAndGreaterThanFromTimeAndReportTypeDB(
            BTPPriorityLookup btpPriorityLookupForEngagement, Date fromTime,Date toTime, String increaseEngagement,Integer limit);

    List<BTPDayWiseData> findByTop3TimesPerDay(BTPPriorityLookup btpPriorityLookupForEngagement,String increaseEngagement, Date currentTime,Date toTime);
    List<BTPDayWiseData> findByTop3TimesPerDayDB(BTPPriorityLookup btpPriorityLookupForEngagement,String increaseEngagement, Date currentTime,Date toTime);

    /**
     * Optimized method using window functions for better performance
     */
    List<BTPDayWiseData> findByTop3TimesPerDayOptimized(BTPPriorityLookup btpPriorityLookupForEngagement,String increaseEngagement, Date currentTime,Date toTime);

    List<BTPDayWiseData> findByTop3TimesPerWeek(BTPPriorityLookup lookup, String reportType, Date currentDate,Date weekEndDate);
}
