package com.birdeye.social.facebook;

import com.birdeye.social.facebook.response.FacebookErrorResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FbStoryDetailsResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("source")
    private String source;

    @JsonProperty("from")
    private FbUser from;

    @JsonProperty("error")
    private FacebookErrorResponse error;

}