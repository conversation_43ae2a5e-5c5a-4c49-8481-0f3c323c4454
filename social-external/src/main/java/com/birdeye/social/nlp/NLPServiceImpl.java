package com.birdeye.social.nlp;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.dto.LanguageDetectDTO;
import com.birdeye.social.dto.LanguageDetectResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class NLPServiceImpl implements NLPService{

    public static final String DETECT_LANGUAGE_URL = "v1/social/detect-language/";
    @Autowired
    RestTemplate nlpRestClient;
    private static final Logger logger = LoggerFactory.getLogger(NLPServiceImpl.class);

    @Override
    public LanguageDetectResponse detectLanguage(LanguageDetectDTO languageDetectDTO) {
        logger.info("Received request to detect language for DTO: {}",languageDetectDTO);
        String nlpServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("social.nlp.services.url");
        //String nlpServiceUrl = "http://10.51.42.243:8080/api/";
        String url = nlpServiceUrl.concat(DETECT_LANGUAGE_URL);
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<LanguageDetectDTO> requestEntity = new HttpEntity<>(languageDetectDTO, headers);

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        ResponseEntity<LanguageDetectResponse> response;
        try{
            response = nlpRestClient.exchange(builder.build().toString(), HttpMethod.POST, requestEntity, LanguageDetectResponse.class);
            logger.info("response: {}",response.getBody());
            return response.getBody();
        }catch (HttpStatusCodeException ex){
            if (ex.getStatusCode().is5xxServerError()) {
                logger.warn("InternalServerException while calling nlp service for URL {} and exception {}", url, ex.getResponseBodyAsString());
            }
            if (ex.getStatusCode().is4xxClientError()) {
                logger.warn("ClientException while calling nlp service for URL {} and exception {}", url, ex.getResponseBodyAsString());
            }
        } catch (Exception e) {
            logger.warn("Exception while calling nlp service for URL {} and exception ", url, e);
        }
        return null;
    }


}
