package com.birdeye.social.model;

import com.birdeye.social.twitter.TwitterPublicMetrics;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class TwitterResponse implements Serializable {

    private String id;
    private String username;
    private String name;
    @JsonProperty("profile_image_url")
    private String profileImageUrl;
    private Boolean verified;
    private String location;

    @JsonProperty("public_metrics")
    private TwitterPublicMetrics publicMetrics;
}

