package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@ToString
public class FbPublicProfileInfo {
    String id;
    String name;
    String link;
    String about;
    String category;
    String description;
    List<String> emails;
    Integer followers_count;
    String single_line_address;
    String username;
    String website;
    Map<String,String> hours;
    Map<String,String> location;
    Map<String, Object> picture;
    Map<String, Object> cover;
    String verification_status;
    String phone;
    Boolean is_always_open;
    List<Object> category_list;
    Map<String, Object> instagram_business_account;
    String temporary_status;
    Boolean is_permanently_closed;
}
