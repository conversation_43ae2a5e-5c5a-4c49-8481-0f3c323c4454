package com.birdeye.social.twitter;

import java.util.Date;

public class TweetInfo {
	private String id;
	private String text;
	private Date createdAt;
	private String tweetUrl;
	private String retweetCount;
	private Integer followerCount;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public String getTweetUrl() {
		return tweetUrl;
	}

	public void setTweetUrl(String tweetUrl) {
		this.tweetUrl = tweetUrl;
	}

	public String getRetweetCount() {
		return retweetCount;
	}

	public void setRetweetCount(String retweetCount) {
		this.retweetCount = retweetCount;
	}
	
	public Integer getFollowerCount() {
		return followerCount;
	}

	public void setFollowerCount(Integer followerCount) {
		this.followerCount = followerCount;
	}

	@Override
	public String toString() {
		return "TweetInfo [id=" + id + ", text=" + text + ", createdAt=" + createdAt + ", tweetUrl=" + tweetUrl
				+ ", retweetCount=" + retweetCount + ", followerCount=" + followerCount + "]";
	}
}
