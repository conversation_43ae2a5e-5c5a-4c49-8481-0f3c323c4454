package com.birdeye.social.twitter;

public class TwitterUserInfo {
	
	private Long profileId;
	private String name;
	private String profileUrl;
	private String profilePictureUrl;
	private String Handle;
	public Long getProfileId() {
		return profileId;
	}
	public void setProfileId(Long profileId) {
		this.profileId = profileId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getProfileUrl() {
		return profileUrl;
	}
	public void setProfileUrl(String profileUrl) {
		this.profileUrl = profileUrl;
	}
	public String getProfilePictureUrl() {
		return profilePictureUrl;
	}
	public void setProfilePictureUrl(String profilePictureUrl) {
		this.profilePictureUrl = profilePictureUrl;
	}
	public String getHandle() {
		return Handle;
	}
	public void setHandle(String handle) {
		Handle = handle;
	}
}
