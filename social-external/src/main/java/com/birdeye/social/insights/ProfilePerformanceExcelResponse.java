package com.birdeye.social.insights;

import com.fasterxml.jackson.annotation.JsonInclude;

@lombok.Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfilePerformanceExcelResponse {

//    @JsonProperty(index=1) if order is changed, please set order manually by adding index
    private String label;
    private String channel;
    private String locationName;
    private Integer totalAudience;
    private Integer netAudienceGrowth;
    private Integer netPageLikes;
    private Integer fans;
    private Integer impressions;
    private Integer reach;
    private Integer engagement;
    private Double eng;
//    private Integer postLinkClicks;
    private Integer videoViews;
    private Integer videoCompleteViews30s;
    private Integer videoPartialViews;
    private Integer publishedPosts;
    private Integer totalMessages;
    private Integer totalSentMessages;
    private Integer totalReceivedMessages;
    private Integer likeCount;
    private Integer commentCount;
    private Integer shareCount;
    private Integer linkClickCount;
    private Integer otherClickCount;
    private String profileId;
    private Integer males;
    private Integer females;
    private Integer others;
    private String countryName;
    private String cityName;
    private Integer totalCount;
    private Integer followerLost;
    private Integer publishedVideoPosts;
    private Integer publishedImagePosts;
    private Integer publishedTextPosts;
    private Integer publishedLinkPosts;
    private Integer publishedStories;
}
