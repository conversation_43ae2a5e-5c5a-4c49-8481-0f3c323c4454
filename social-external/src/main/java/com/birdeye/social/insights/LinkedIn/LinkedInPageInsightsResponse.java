package com.birdeye.social.insights.LinkedIn;

import com.birdeye.social.insights.PageInsightData;
import com.birdeye.social.insights.PageInsightDataPoint;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.List;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public class LinkedInPageInsightsResponse extends PageInsightData {

    private Double followerGainPer;
    private Double followerLostPer;
    private Double likeLostPer;
    private Double likeGainPer;
    private Double engRatePer;
    private Double engagementPer;
    private Double impressionPer;
    private Double reachPer;
    private Double totalPostPer;
    // private List<PageInsightDataPoint> pageDataPointsList;
}
