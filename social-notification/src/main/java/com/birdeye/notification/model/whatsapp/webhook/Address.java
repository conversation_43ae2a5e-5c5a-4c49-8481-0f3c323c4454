package com.birdeye.notification.model.whatsapp.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * The type Address.
 */
public class Address{

        @JsonProperty("zip") String zip;

        @JsonProperty("country") String country;

        @JsonProperty("country_code") String countryCode;

        @JsonProperty("city") String city;

        @JsonProperty("street") String street;

        @JsonProperty("state") String state;

        @JsonProperty("type")
                String type;

        public String getZip() {
                return zip;
        }

        public void setZip(String zip) {
                this.zip = zip;
        }

        public String getCountry() {
                return country;
        }

        public void setCountry(String country) {
                this.country = country;
        }

        public String getCountryCode() {
                return countryCode;
        }

        public void setCountryCode(String countryCode) {
                this.countryCode = countryCode;
        }

        public String getCity() {
                return city;
        }

        public void setCity(String city) {
                this.city = city;
        }

        public String getStreet() {
                return street;
        }

        public void setStreet(String street) {
                this.street = street;
        }

        public String getState() {
                return state;
        }

        public void setState(String state) {
                this.state = state;
        }

        public String getType() {
                return type;
        }

        public void setType(String type) {
                this.type = type;
        }
}