package com.birdeye.notification.model.whatsapp.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * The type Order.
 */
public class Order {

        @JsonProperty("catalog_id") String catalogId;

        @JsonProperty("product_items") List<Product> productItems;

        @JsonProperty("text") String text;

        public String getCatalogId() {
                return catalogId;
        }

        public void setCatalogId(String catalogId) {
                this.catalogId = catalogId;
        }

        public List<Product> getProductItems() {
                return productItems;
        }

        public void setProductItems(List<Product> productItems) {
                this.productItems = productItems;
        }

        public String getText() {
                return text;
        }

        public void setText(String text) {
                this.text = text;
        }
}