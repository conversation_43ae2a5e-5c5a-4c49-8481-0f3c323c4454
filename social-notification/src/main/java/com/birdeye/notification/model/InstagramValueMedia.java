package com.birdeye.notification.model;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


@JsonIgnoreProperties(ignoreUnknown = true)
public class InstagramValueMedia implements Serializable {
    private static final long serialVersionUID = 1L;
    private String id;
    private String media_product_type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMedia_product_type() {
        return media_product_type;
    }

    public void setMedia_product_type(String media_product_type) {
        this.media_product_type = media_product_type;
    }

    @Override
    public String toString() {
        return "InstagramValueMedia{" +
                "id='" + id + '\'' +
                ", media_product_type='" + media_product_type + '\'' +
                '}';
    }
}
