package com.birdeye.notification.model;

import java.util.*;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.birdeye.notification.model.*;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TwitterNotification implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long for_user_id;
    private Boolean user_has_blocked;
 /*   private List<Tweet> tweet_create_events;
    private List<TwitterFavoriteEvent> favorite_events;
    private List<TwitterFollowEvent> follow_events;
    private List<TwitterBlockEvent> block_events;*/


  /*  @java.lang.Override
    public java.lang.String toString() {
        return "TwitterMentionsNotification{" +
                "for_user_id=" + for_user_id +
                ", user_has_blocked=" + user_has_blocked +
                ", tweet_create_events=" + tweet_create_events +
                '}';
    }*/
}
