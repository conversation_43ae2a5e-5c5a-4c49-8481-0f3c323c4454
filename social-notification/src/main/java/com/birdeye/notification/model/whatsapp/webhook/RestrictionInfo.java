package com.birdeye.notification.model.whatsapp.webhook;

import com.birdeye.notification.model.whatsapp.webhook.type.RestrictionType;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * The type Restriction info.
 */
public class RestrictionInfo{

        @JsonProperty("restriction_type")
        RestrictionType restrictionType;

        @JsonProperty("expiration") String expiration;

        public RestrictionType getRestrictionType() {
                return restrictionType;
        }

        public void setRestrictionType(RestrictionType restrictionType) {
                this.restrictionType = restrictionType;
        }

        public String getExpiration() {
                return expiration;
        }

        public void setExpiration(String expiration) {
                this.expiration = expiration;
        }
}
