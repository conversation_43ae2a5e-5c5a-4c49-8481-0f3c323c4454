package com.birdeye.notification.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class InstagramReferral implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private InstagramProduct product;
	
	public InstagramProduct getProduct() {
		return product;
	}
	public void setProduct(InstagramProduct product) {
		this.product = product;
	}
	
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("InstagramReferral [product=").append(product).append("]");
		return builder.toString();
	}
    
}
