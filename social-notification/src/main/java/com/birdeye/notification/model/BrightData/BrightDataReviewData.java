package com.birdeye.notification.model.BrightData;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

public class BrightDataReviewData {
    private long inorganicReviewCount;
    private long invalidReviewCount;
    private long duplicateReviewCount;
    private String aggregationStatus;
    private String servedByIp;
    private String servedByPort;
    private long timeTaken;
    private Map<String, ?> error;
    private long originalReviewCount;
    private long reviewReturnedCount;
    private String startDate;
    private String endDate;
    private double overallRatingOnSource;
    private long reviewCountOnSource;
    private String reviewAggregationId;
    private long businessInfoId;
    private long aggInfoId;
    private long businessId;
    private boolean freeProdEnv;
    private String aggregationFrequencyType;
    private List<BrightDataReviewsList> data;


    public long getInorganicReviewCount() {
        return inorganicReviewCount;
    }

    public void setInorganicReviewCount(long inorganicReviewCount) {
        this.inorganicReviewCount = inorganicReviewCount;
    }

    public long getInvalidReviewCount() {
        return invalidReviewCount;
    }

    public void setInvalidReviewCount(long invalidReviewCount) {
        this.invalidReviewCount = invalidReviewCount;
    }

    public long getDuplicateReviewCount() {
        return duplicateReviewCount;
    }

    public void setDuplicateReviewCount(long duplicateReviewCount) {
        this.duplicateReviewCount = duplicateReviewCount;
    }

    public String getAggregationStatus() {
        return aggregationStatus;
    }

    public void setAggregationStatus(String aggregationStatus) {
        this.aggregationStatus = aggregationStatus;
    }

    public String getServedByIp() {
        return servedByIp;
    }

    public void setServedByIp(String servedByIp) {
        this.servedByIp = servedByIp;
    }

    public String getServedByPort() {
        return servedByPort;
    }

    public void setServedByPort(String servedByPort) {
        this.servedByPort = servedByPort;
    }

    public long getTimeTaken() {
        return timeTaken;
    }

    public void setTimeTaken(long timeTaken) {
        this.timeTaken = timeTaken;
    }

    @JsonProperty("error")
    public Map<String, ?> getError() {
        return error;
    }

    @JsonProperty("err")
    public void setError(Map<String, ?> error) {
        this.error = error;
    }

    public long getOriginalReviewCount() {
        return originalReviewCount;
    }

    public void setOriginalReviewCount(long originalReviewCount) {
        this.originalReviewCount = originalReviewCount;
    }

    public long getReviewReturnedCount() {
        return reviewReturnedCount;
    }

    public void setReviewReturnedCount(long reviewReturnedCount) {
        this.reviewReturnedCount = reviewReturnedCount;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public double getOverallRatingOnSource() {
        return overallRatingOnSource;
    }

    public void setOverallRatingOnSource(double overallRatingOnSource) {
        this.overallRatingOnSource = overallRatingOnSource;
    }

    public long getReviewCountOnSource() {
        return reviewCountOnSource;
    }

    public void setReviewCountOnSource(long reviewCountOnSource) {
        this.reviewCountOnSource = reviewCountOnSource;
    }

    public String getReviewAggregationId() {
        return reviewAggregationId;
    }

    public void setReviewAggregationId(String reviewAggregationId) {
        this.reviewAggregationId = reviewAggregationId;
    }

    public long getBusinessInfoId() {
        return businessInfoId;
    }

    public void setBusinessInfoId(long businessInfoId) {
        this.businessInfoId = businessInfoId;
    }

    public long getAggInfoId() {
        return aggInfoId;
    }

    public void setAggInfoId(long aggInfoId) {
        this.aggInfoId = aggInfoId;
    }

    public long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(long businessId) {
        this.businessId = businessId;
    }

    public boolean isFreeProdEnv() {
        return freeProdEnv;
    }

    public void setFreeProdEnv(boolean freeProdEnv) {
        this.freeProdEnv = freeProdEnv;
    }

    public String getAggregationFrequencyType() {
        return aggregationFrequencyType;
    }

    public void setAggregationFrequencyType(String aggregationFrequencyType) {
        this.aggregationFrequencyType = aggregationFrequencyType;
    }

    public List<BrightDataReviewsList> getData() {
        return data;
    }

    public void setData(List<BrightDataReviewsList> data) {
        this.data = data;
    }


    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("BrightDataReviewData{");
        sb.append("inorganicReviewCount=").append(inorganicReviewCount);
        sb.append(", invalidReviewCount=").append(invalidReviewCount);
        sb.append(", duplicateReviewCount=").append(duplicateReviewCount);
        sb.append(", aggregationStatus='").append(aggregationStatus).append('\'');
        sb.append(", servedByIp='").append(servedByIp).append('\'');
        sb.append(", servedByPort='").append(servedByPort).append('\'');
        sb.append(", timeTaken=").append(timeTaken);
        sb.append(", error=").append(error);
        sb.append(", originalReviewCount=").append(originalReviewCount);
        sb.append(", reviewReturnedCount=").append(reviewReturnedCount);
        sb.append(", startDate='").append(startDate).append('\'');
        sb.append(", endDate='").append(endDate).append('\'');
        sb.append(", overallRatingOnSource=").append(overallRatingOnSource);
        sb.append(", reviewCountOnSource=").append(reviewCountOnSource);
        sb.append(", reviewAggregationId='").append(reviewAggregationId).append('\'');
        sb.append(", businessInfoId=").append(businessInfoId);
        sb.append(", aggInfoId=").append(aggInfoId);
        sb.append(", businessId=").append(businessId);
        sb.append(", freeProdEnv=").append(freeProdEnv);
        sb.append(", aggregationFrequencyType='").append(aggregationFrequencyType).append('\'');
        sb.append(", data=").append(data);
        sb.append('}');
        return sb.toString();
    }
}
