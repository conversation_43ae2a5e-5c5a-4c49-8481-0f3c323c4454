package com.birdeye.notification.model.BrightData;

import java.util.List;

public class BrightDataResponse {
    private BrightDataBusinessAggregationDetails input;
    private List<BrightDataReviewData> lines;
    private boolean error;
    private String message;

    public BrightDataBusinessAggregationDetails getInput() {
        return input;
    }

    public void setInput(BrightDataBusinessAggregationDetails input) {
        this.input = input;
    }

    public List<BrightDataReviewData> getLines() {
        return lines;
    }

    public void setLines(List<BrightDataReviewData> lines) {
        this.lines = lines;
    }

    public boolean isError() {
        return error;
    }

    public void setError(boolean error) {
        this.error = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("BrightDataResponse{");
        sb.append("input=").append(input);
        sb.append(", lines=").append(lines);
        sb.append(", error=").append(error);
        sb.append(", errorMessage='").append(message).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
