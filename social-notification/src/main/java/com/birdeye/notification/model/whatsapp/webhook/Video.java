package com.birdeye.notification.model.whatsapp.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * The type Video.
 *
 * @param sha256   The checksum of the media.
 * @param mimeType The mime type of the media.                 The caption that describes the media.
 * @param caption  Added to Webhooks if it has been previously specified.                 The caption that describes the media.
 * @param id       The ID of the medi
 */
public class Video{

        @JsonProperty("mime_type") String mimeType;

        @JsonProperty("sha256") String sha256;

        @JsonProperty("caption") String caption;

        @JsonProperty("id") String id;

        public String getMimeType() {
                return mimeType;
        }

        public void setMimeType(String mimeType) {
                this.mimeType = mimeType;
        }

        public String getSha256() {
                return sha256;
        }

        public void setSha256(String sha256) {
                this.sha256 = sha256;
        }

        public String getCaption() {
                return caption;
        }

        public void setCaption(String caption) {
                this.caption = caption;
        }

        public String getId() {
                return id;
        }

        public void setId(String id) {
                this.id = id;
        }
}
