package com.birdeye.notification.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Value implements Serializable {

    private Integer created_time;
    private String item;
    private String open_graph_story_id;
    private String review_text;
    private String recommendation_type;
    private String reviewer_id;
    private String reviewer_name;
    private String verb;
    private String reviewerImageUrl;
    private Map<String,Object> overlayDetails;
    private Integer businessId;
    private FbPost post;
    private ReviewerDetails from;
    private String post_id;
    private String comment_id;
    private String parent_id;
    private String message;
    private String photo;

    public Integer getCreated_time() {
        return created_time;
    }

    public void setCreated_time(Integer created_time) {
        this.created_time = created_time;
    }

    public String getItem() {
        return item;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public String getOpen_graph_story_id() {
        return open_graph_story_id;
    }

    public void setOpen_graph_story_id(String open_graph_story_id) {
        this.open_graph_story_id = open_graph_story_id;
    }

    public String getReview_text() {
        return review_text;
    }

    public void setReview_text(String review_text) {
        this.review_text = review_text;
    }

    public String getRecommendation_type() {
        return recommendation_type;
    }

    public void setRecommendation_type(String recommendation_type) {
        this.recommendation_type = recommendation_type;
    }

    public String getReviewer_id() {
        return reviewer_id;
    }

    public void setReviewer_id(String reviewer_id) {
        this.reviewer_id = reviewer_id;
    }

    public String getReviewer_name() {
        return reviewer_name;
    }

    public void setReviewer_name(String reviewer_name) {
        this.reviewer_name = reviewer_name;
    }

    public String getVerb() {
        return verb;
    }

    public void setVerb(String verb) {
        this.verb = verb;
    }

    public FbPost getPost() {
        return post;
    }

    public void setPost(FbPost post) {
        this.post = post;
    }

    public ReviewerDetails getFrom() {
        return from;
    }

    public void setFrom(ReviewerDetails from) {
        this.from = from;
    }

    public String getReviewerImageUrl() {
        return reviewerImageUrl;
    }

    public void setReviewerImageUrl(String reviewerImageUrl) {
        this.reviewerImageUrl = reviewerImageUrl;
    }

    public Map<String, Object> getOverlayDetails() {
        return overlayDetails;
    }

    public void setOverlayDetails(Map<String, Object> overlayDetails) {
        this.overlayDetails = overlayDetails;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public String getPost_id() {
        return post_id;
    }

    public void setPost_id(String post_id) {
        this.post_id = post_id;
    }

    public String getComment_id() {
        return comment_id;
    }

    public void setComment_id(String comment_id) {
        this.comment_id = comment_id;
    }

    public String getParent_id() {
        return parent_id;
    }

    public void setParent_id(String parent_id) {
        this.parent_id = parent_id;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    @Override
    public String toString() {
        return "Value{" +
                "created_time=" + created_time +
                ", item='" + item + '\'' +
                ", open_graph_story_id='" + open_graph_story_id + '\'' +
                ", review_text='" + review_text + '\'' +
                ", recommendation_type='" + recommendation_type + '\'' +
                ", reviewer_id='" + reviewer_id + '\'' +
                ", reviewer_name='" + reviewer_name + '\'' +
                ", verb='" + verb + '\'' +
                ", reviewerImageUrl='" + reviewerImageUrl + '\'' +
                ", overlayDetails=" + overlayDetails +
                ", businessId=" + businessId +
                ", post=" + post +
                ", from=" + from +
                ", post_id='" + post_id + '\'' +
                ", comment_id='" + comment_id + '\'' +
                ", parent_id='" + parent_id + '\'' +
                ", message='" + message + '\'' +
                ", photo='" + photo + '\'' +
                '}';
    }
}
