package com.birdeye.notification.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class InstagramMessaging implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Long timestamp;
    private InstagramMessage message;
    private InstagramUser sender; // IGSID
    private InstagramUser recipient; // IGID
    private InstagramReaction reaction;

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public InstagramMessage getMessage() {
        return message;
    }

    public InstagramReaction getReaction() {
        return reaction;
    }

    public void setReaction(InstagramReaction reaction) {
        this.reaction = reaction;
    }

    public void setMessage(InstagramMessage message) {
        this.message = message;
    }

    public InstagramUser getSender() {
        return sender;
    }

    public void setSender(InstagramUser sender) {
        this.sender = sender;
    }

    public InstagramUser getRecipient() {
        return recipient;
    }

    public void setRecipient(InstagramUser recipient) {
        this.recipient = recipient;
    }

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("InstagramMessaging [timestamp=").append(timestamp).append(", message=").append(message)
				.append(", sender=").append(sender).append(", recipient=").append(recipient).append("]");
		return builder.toString();
	}

}
