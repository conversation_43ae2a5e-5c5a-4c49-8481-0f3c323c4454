package com.birdeye.notification.model;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AppleInteractiveData implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private InteractiveData data;
	
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class InteractiveData implements Serializable{
		/**
		 * 
		 */
		private static final long serialVersionUID = 1L;
		private ReplyMessage replyMessage;
		@JsonProperty(value = "quick-reply")
		private QuickReply quickReply;

		public ReplyMessage getReplyMessage() {
			return replyMessage;
		}

		public void setReplyMessage(ReplyMessage replyMessage) {
			this.replyMessage = replyMessage;
		}
	}
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class ReplyMessage implements Serializable{
		/**
		 * 
		 */
		private static final long serialVersionUID = 1L;
		private String title;

		public String getTitle() {
			return title;
		}

		public void setTitle(String title) {
			this.title = title;
		}
		
	}

	public static class QuickReply implements Serializable {
		private static final long serialVersionUID = 1L;
		private String selectedIdentifier;

		public String getSelectedIdentifier() {
			return selectedIdentifier;
		}

		public void setSelectedIdentifier(String selectedIdentifier) {
			this.selectedIdentifier = selectedIdentifier;
		}
	}

	public InteractiveData getData() {
		return data;
	}
	public void setData(InteractiveData data) {
		this.data = data;
	}
	
}
