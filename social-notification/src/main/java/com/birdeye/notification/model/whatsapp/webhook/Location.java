package com.birdeye.notification.model.whatsapp.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * The type Location.
 */
public class Location {

        @JsonProperty("address") String address;

        @JsonProperty("latitude") double latitude;

        @JsonProperty("name") String name;

        @JsonProperty("longitude") double longitude;

        @JsonProperty("url") String url;

        public String getAddress() {
                return address;
        }

        public void setAddress(String address) {
                this.address = address;
        }

        public double getLatitude() {
                return latitude;
        }

        public void setLatitude(double latitude) {
                this.latitude = latitude;
        }

        public String getName() {
                return name;
        }

        public void setName(String name) {
                this.name = name;
        }

        public double getLongitude() {
                return longitude;
        }

        public void setLongitude(double longitude) {
                this.longitude = longitude;
        }

        public String getUrl() {
                return url;
        }

        public void setUrl(String url) {
                this.url = url;
        }
}